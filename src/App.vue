<template>
  <el-config-provider :locale="zhCn">
  <div class="app">
    <router-view />
  </div>
</el-config-provider>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 初始化 Store
const cityStore = useCityStore()


// 应用初始化
onMounted(() => {
  // 初始化城市 Store
  // cityStore.initialize()
  
})


</script>

<style lang="scss">
@import '@/assets/iconfont/iconfont.css';
@import 'gxrcw-ui/style.css';

.app {
  min-height: 100vh;
  /* 避免过渡/切换期间出现纯白底导致的闪白 */
  background-color: transparent;
}
</style>
