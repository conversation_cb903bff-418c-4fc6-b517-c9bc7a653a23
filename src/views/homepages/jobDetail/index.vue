<template>
  <div id="jobDetail" class="job-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-content">
        <i class="el-icon-loading" style="font-size: 24px; margin-right: 8px;"></i>
        正在加载职位详情...
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 头部信息 -->
      <div id="header" class="job-header" :class="{ 'job-header-float': isHeaderFloat }">
        <div class="w-1200px mx-auto px-0 job-clearfix">
          <div class="job-base-info job-fl">
            <div class="job-pos-wrap job-clearfix">
              <div class="job-title-salary-wrapper">
                <h1 class="job-title" :title="jobData.title">{{ jobData.title }}</h1>
                <div class="job-salary">
                  {{ jobData.salary }}
                </div>
              </div>
              <div class="pos-state-wrap">
                <!-- 职位状态标签 -->
              </div>
            </div>

            <p class="job-detail-text" :class="{ 'job-detail-hidden': isHeaderFloat }">
              {{ jobData.location }}<span class="job-separator">|</span>学历{{ jobData.education }}<span
                class="job-separator">|</span>经验{{ jobData.experience }}<span class="job-separator">|</span>招聘{{
              jobData.recruitNum }}人
            </p>
          </div>
          <div class="job-btn-wrap job-fr">
            <el-button class="job-btn job-btn-collection" @click="collectPosition">
              <i class="i-carbon-star job-icon"></i>收藏职位
            </el-button>
            <el-button type="primary" class="job-btn job-btn-apply" @click="applyPosition">
              立即投递
            </el-button>
          </div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="job-main-wrap bg-gray-50">
        <div class="main-content w-1200px mx-auto job-clearfix bg-white">
          <div class="job-main-left job-fl">
            <!-- 职位描述 -->
            <div class="pos-details job-one-column">
              <div class="job-title-blue">
                <i class="job-border"></i>职位描述
              </div>
              <div v-if="jobData.keywords && jobData.keywords.length">
                <ul class="job-clearfix">
                  <li v-for="keyword in jobData.keywords" :key="keyword" class="job-keyword">
                    <span class="job-keyword-text">{{ keyword }}</span>
                  </li>
                </ul>
              </div>
              <div class="pos-details-con job-content">
                <pre class="job-description">{{ jobData.description }}</pre>
              </div>
              <div v-if="jobData.welfare && jobData.welfare.length" class="job-welfare-con job-content">
                <ul class="job-clearfix">
                  <li v-for="item in jobData.welfare" :key="item" class="job-welfare-item">{{ item }}</li>
                </ul>
              </div>
            </div>

            <!-- 工作地点 -->
            <div class="working-place job-one-column" v-if="jobData.workLocation">
              <div class="job-title-blue">
                <i class="job-border"></i>工作地点
              </div>
              <div class="working-place-con job-content">
                <!-- 地图组件 -->
                <div class="company-map">
                  <MapComponent :title="jobData.workLocation.company" :address="jobData.workLocation.address"
                    :longitude="jobData.workLocation.longitude" :latitude="jobData.workLocation.latitude" width="830px"
                    height="400px" :show-coordinates="true" />
                </div>
              </div>
            </div>

            <!-- 其它要求 -->
            <div class="contact-info job-one-column">
              <div class="job-title-blue">
                <i class="job-border"></i>其它要求
              </div>
              <div class="job-contact-info-con job-content">
                <p class="job-contact-text job-requirements">
                  <span class="job-contact-span job-req-w1">工作性质：<label class="job-contact-label">{{ jobData.workType
                      }}</label></span>
                  <span class="job-contact-span job-req-w2">专业要求：<label class="job-contact-label">{{
                      jobData.majorRequirement || '不限' }}</label></span>
                  <span class="job-contact-span job-req-w3">语言/程度：<label class="job-contact-label">{{
                      jobData.languageRequirement || '不限' }}</label></span>
                </p>
                <p class="job-contact-text job-requirements">
                  <span class="job-contact-span job-req-w1">职称要求：<label class="job-contact-label">{{
                      jobData.titleRequirement || '不限' }}</label></span>
                  <span class="job-contact-span job-req-w3">更新时间：<label class="job-contact-label">{{ jobData.updateTime
                      }}</label></span>
                </p>
              </div>
            </div>

            <!-- 公司介绍 -->
            <div class="pos-details job-one-column">
              <div class="job-title-blue">
                <i class="job-border"></i>公司介绍
              </div>
              <div class="pos-details-con job-content">
                <pre class="job-company-description">{{ jobData.companyInfo.description }}</pre>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="contact-info job-one-column">
              <div class="job-title-blue">
                <i class="job-border"></i>联系方式
              </div>
              <div class="job-contact-info-con job-content">
                <p class="job-contact-text">联系人：<label class="job-contact-label">{{ jobData.contact.name }}</label></p>
                <p class="job-contact-text">
                  联系电话：
                  <label class="job-contact-label">{{ showPhone ? jobData.contact.phone : hidePhone(jobData.contact.phone)
                    }}</label>
                  <el-button v-if="!showPhone" type="primary" size="small" class="job-contact-btn"
                    @click="showContactInfo">
                    查看
                  </el-button>
                </p>
                <p class="job-contact-text">
                  电子邮箱：
                  <label class="job-contact-label">{{ showEmail ? jobData.contact.email : hideEmail(jobData.contact.email)
                    }}</label>
                </p>
                <p class="job-contact-text">联系地址：<label class="job-contact-label">{{ jobData.contact.address }}</label>
                </p>
              </div>
            </div>

            <!-- 职位推荐 -->
            <div class="job-pos-recommend job-one-column">
              <div class="job-title-blue">
                <i class="job-border"></i>职位推荐
              </div>
              <div class="pos-recommend-con job-content">
                <ul v-if="recommendedPositions.length > 0" class="job-recommend-list job-clearfix">
                  <li v-for="pos in recommendedPositions" :key="pos.id" class="job-recommend-item">
                    <h3 class="job-recommend-title">
                      <router-link :to="cityStore.getCityPagePath(`jobDetail/${pos.id}`)" :title="pos.title" class="job-link job-pos-name">
                        {{ pos.title }}
                      </router-link>
                      <span class="job-pos-salary">{{ pos.salary }}</span>
                    </h3>
                    <p class="job-recommend-text">
                      <router-link :to="cityStore.getCityPagePath(`company/${pos.companyId}`)" :title="pos.companyName"
                        class="job-link job-ent-name">
                        {{ pos.companyName }}
                      </router-link>
                    </p>
                  </li>
                </ul>
                <div v-else class="no-recommended-positions">
                  <p>暂无相关职位推荐</p>
                </div>
                <div v-if="recommendedPositions.length > 0" class="job-recommend-btn-wrap">
                  <router-link :to="cityStore.getCityPagePath('oddjob')" class="btn-check-more">
                    查看更多职位
                  </router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧栏 -->
          <div class="job-main-right job-fr">
            <div class="job-ent-wrap job-clearfix">
              <div class="job-ent-logo">
                <router-link :to="cityStore.getCityPagePath(`company/${jobData.companyInfo.id}`)" class="ent-head">
                  <img :src="jobData.companyInfo.logo" :alt="jobData.companyInfo.name" class="job-ent-logo-img" />
                </router-link>
              </div>
              <div class="job-ent-name">
                <router-link :to="cityStore.getCityPagePath(`company/${jobData.companyInfo.id}`)" class="job-link job-ent-link">
                  {{ jobData.companyInfo.name }}
                </router-link>
              </div>
            </div>

            <div class="job-ent-details">
              <p class="job-ent-detail-item">
                <span class="job-ent-detail-cell"><i class="i-carbon-building job-ent-icon"></i></span>
                <span class="job-ent-detail-cell">{{ jobData.companyInfo.type }}</span>
              </p>
              <p class="job-ent-detail-item">
                <span class="job-ent-detail-cell"><i class="i-carbon-group job-ent-icon"></i></span>
                <span class="job-ent-detail-cell">{{ jobData.companyInfo.scale }}</span>
              </p>
              <p class="job-ent-detail-item">
                <span class="job-ent-detail-cell"><i class="i-carbon-industry job-ent-icon"></i></span>
                <span class="job-ent-detail-cell">{{ jobData.companyInfo.industry }}</span>
              </p>
              <p class="job-ent-detail-item">
                <span class="job-ent-detail-cell"><i class="i-carbon-location job-ent-icon"></i></span>
                <span class="job-ent-detail-cell address">{{ jobData.companyInfo.address }}</span>
              </p>
            </div>

            <div class="job-ent-btn-wrap job-btn-look">
              <router-link :to="cityStore.getCityPagePath(`company/${jobData.companyInfo.id}`)"
                class="job-button job-button-blue-border job-btn-check-all">
                查看该公司所有职位
              </router-link>
              <div v-if="jobData.companyInfo.hasProfile" class="job-company-badge">有形象展示</div>
            </div>

            <!-- 看过该职位的人还看了 -->
            <div class="job-interesting">
              <div class="job-title-blue">
                <i class="job-border"></i>看过该职位的人还看了
              </div>
              <div class="interesting-con">
                <ul v-if="relatedPositions.length > 0">
                  <li v-for="pos in relatedPositions" :key="pos.id" class="job-interesting-item">
                    <h3 class="job-interesting-title">
                      <router-link :to="cityStore.getCityPagePath(`jobDetail/${pos.id}`)" :title="pos.title" class="job-link job-interesting-link">
                        {{ pos.title }}
                      </router-link>
                    </h3>
                    <p class="job-interesting-text job-interesting-salary">{{ pos.salary }}</p>
                    <p class="job-interesting-text">
                      <router-link :to="cityStore.getCityPagePath(`company/${pos.companyId}`)" :title="pos.companyName"
                        class="job-link job-interesting-ent-name">
                        {{ pos.companyName }}
                      </router-link>
                    </p>
                  </li>
                </ul>
                <div v-else class="no-related-positions">
                  <p>暂无相关职位推荐</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 提醒信息 -->
        <div v-if="showReminder" class="job-reminder w-1200px mx-auto job-clearfix">
          <i class="i-carbon-warning text-orange-500 job-reminder-icon"></i>
          <p class="job-reminder-text">
            提示：若用人单位存在发布虚假招聘广告，以招聘为名的培训、招生，许诺推荐其他工作机会，提供培训贷款，或者以其他任何名义向求职者收取财物（如支付体检费、服装费、押金、办卡费、培训费等），以及扣押或以保管为名索要身份证、毕业证及其他证件的，都属于违法行为。请您提高警惕并注意保护个人信息！如遇涉密信息或情报收集活动，请及时拨打国家安全机关举报热线12339进行举报。
            <a href="javascript:;" class="job-link job-reminder-link" @click="reportJob">立即举报</a>
          </p>
          <a href="javascript:;" class="job-reminder-close" @click="showReminder = false">×</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { useCityStore } from '@/stores/cityStore'
  import MapComponent from '@/components/common/MapComponent.vue'
  import { PositionApi, BussDistrict, ApplicationPlatform, PositionRecommendRequest } from '@/api-services'
  import { getPcAPI } from '@/utils/axios-utils'

  // 路由参数
  const route = useRoute()
  const jobId = route.params.id
  
  // 城市状态管理
  const cityStore = useCityStore()

  // API实例
  const positionApi = getPcAPI(PositionApi)

  // 响应式数据
  const showPhone = ref(false)
  const showEmail = ref(false)
  const showReminder = ref(true)
  const isHeaderFloat = ref(false)
  const loading = ref(true)

  // 职位详情数据 - 从API获取
  const jobData = ref({
    id: jobId,
    title: '',
    salary: '',
    location: '',
    education: '',
    experience: '',
    recruitNum: '',
    workType: '',
    majorRequirement: '',
    languageRequirement: '',
    titleRequirement: '',
    updateTime: '',
    keywords: [],
    description: '',
    welfare: [],
    workLocation: {
      company: '',
      address: '',
      longitude: 0,
      latitude: 0
    },
    companyInfo: {
      id: '',
      name: '',
      logo: '',
      type: '',
      scale: '',
      industry: '',
      address: '',
      hasProfile: false,
      description: ''
    },
    contact: {
      name: '',
      phone: '',
      email: '',
      address: ''
    }
  })

  // 推荐职位数据
  const recommendedPositions = ref([])
  
  // 相关职位数据（看过该职位的人还看了）
  const relatedPositions = ref([])

  // 获取职位推荐数据
  const fetchRecommendedPositions = async (districtId) => {
    try {
      // 构建推荐请求参数
      const recommendRequest = {
        page: 1,
        pageSize: 8, // 只获取8个推荐职位
        positionCareeID: 0, // 0表示职位推荐
        sort: 1, // 1=推荐，2=附近，3=最新
        districtId: districtId,
        isAddPush: 0 // 不添加推送信息
      }

      const response = await positionApi.apiPositionRecommendByCareePost(recommendRequest)

      if (response.data?.data && Array.isArray(response.data.data)) {
        // 映射API数据到组件数据结构
        recommendedPositions.value = response.data.data.map(item => ({
          id: item.positionGuid || item.positionID?.toString() || '',
          title: item.positionName || '',
          salary: item.payPackage || '',
          companyId: item.enterpriseGuid || item.enterpriseID?.toString() || '',
          companyName: item.enterpriseName || ''
        }))
      } else {
        recommendedPositions.value = []
      }
    } catch (error) {
      console.error('获取职位推荐失败:', error)
      // 推荐职位获取失败时显示空状态
      recommendedPositions.value = []
    }
  }

  // 获取相似职位数据
  const fetchSimilarPositions = async (positionGuid, districtId) => {
    try {
      // 调用相似职位API
      const response = await positionApi.apiPositionSimilarsGet(
        positionGuid,                    // positionGuid: 职位guid
        districtId,                     // districtId: 请求地市
        ApplicationPlatform.PC          // from: 请求来源
      )

      if (response.data?.data && Array.isArray(response.data.data)) {
        // 映射API数据到组件数据结构
        relatedPositions.value = response.data.data.map(item => ({
          id: item.positionGuid || item.positionID?.toString() || '',
          title: item.positionName || '',
          salary: item.payPackage || '',
          companyId: item.enterpriseGuid || item.enterpriseID?.toString() || '',
          companyName: item.enterpriseName || ''
        }))
      } else {
        relatedPositions.value = []
      }
    } catch (error) {
      console.error('获取相似职位失败:', error)
      // 相似职位获取失败不影响主要功能，只记录错误
      relatedPositions.value = []
    }
  }

  // 获取职位详情数据
  const fetchJobDetail = async () => {
    try {
      loading.value = true
      
      // 获取当前城市对应的BussDistrict
      const currentCityInfo = cityStore.cityInfo
      let districtId = BussDistrict.NUMBER_0 // 默认广西
      
      // 根据城市ID映射到BussDistrict
      switch (currentCityInfo.id) {
        case 0:
          districtId = BussDistrict.NUMBER_0 // 广西
          break
        case 1:
          districtId = BussDistrict.NUMBER_1 // 桂林
          break
        case 2:
          districtId = BussDistrict.NUMBER_2 // 柳州
          break
        case 4:
          districtId = BussDistrict.NUMBER_4 // 梧州
          break
        case 6:
          districtId = BussDistrict.NUMBER_6 // 百色
          break
        case 7:
          districtId = BussDistrict.NUMBER_7 // 钦州
          break
        case 8:
          districtId = BussDistrict.NUMBER_8 // 河池
          break
        case 9:
          districtId = BussDistrict.NUMBER_9 // 北海
          break
        case 11:
          districtId = BussDistrict.NUMBER_11 // 防港
          break
        case 12:
          districtId = BussDistrict.NUMBER_12 // 玉林
          break
        case 13:
          districtId = BussDistrict.NUMBER_13 // 崇左
          break
        case 14:
          districtId = BussDistrict.NUMBER_14 // 贵港
          break
        case 15:
          districtId = BussDistrict.NUMBER_15 // 来宾
          break
        case 18:
          districtId = BussDistrict.NUMBER_18 // 贺州
          break
        case 19:
          districtId = BussDistrict.NUMBER_19 // 南宁
          break
        default:
          districtId = BussDistrict.NUMBER_0
      }

      // 调用职位详情API
      const response = await positionApi.apiPositionDetailGet(
        jobId,                           // id: 职位ID
        undefined,                       // trackingGuid: 可选
        districtId,                     // districtId: 地市来源
        ApplicationPlatform.PC,         // from: 地市来源
        0                               // isRecommend: 是否推荐来源
      )

      if (response.data?.data) {
        const apiData = response.data.data
        
        // 映射API数据到组件数据结构
        jobData.value = {
          id: jobId,
          title: apiData.positionName || '',
          salary: apiData.payPackage || '',
          location: apiData.workPlace || '',
          education: apiData.degreeName || '',
          experience: apiData.workAge || '',
          recruitNum: apiData.positionAmount || '',
          workType: apiData.workProperty || '',
          majorRequirement: apiData.majorRequirement || '',
          languageRequirement: apiData.languageRequirement || '',
          titleRequirement: apiData.professionalTitle || '',
          updateTime: apiData.publishTime ? new Date(apiData.publishTime).toLocaleDateString() : '',
          keywords: apiData.positionKeywords?.map(k => k.keywordName || '') || [],
          description: apiData.positionDescription || '',
          welfare: apiData.positionWelfareNames || [],
          workLocation: {
            company: apiData.enterpriseName || '',
            address: apiData.detailAddress || apiData.workPlace || '',
            longitude: apiData.longitudex || 108.366543,
            latitude: apiData.latitudey || 22.817002
          },
          companyInfo: {
            id: apiData.enterpriseGuid || '',
            name: apiData.enterpriseName || '',
            logo: apiData.enterpriseLogo || '//image.gxrc.com/EnterpriseMaterial/Logo/19483_b6c10f37-91b7-460f-b97d-5abcd829f3a8.png',
            type: apiData.enterpriseProperty || '',
            scale: apiData.enterpriseEmployeeNumber || '',
            industry: apiData.enterpriseIndustry || '',
            address: apiData.detailAddress || apiData.workPlace || '',
            hasProfile: apiData.haveImageDisplay || false,
            description: apiData.enterpriseIntroduction || ''
          },
          contact: {
            name: apiData.hrName || '',
            phone: apiData.hrMobile || '',
            email: apiData.hrEmail || '',
            address: apiData.detailAddress || apiData.workPlace || ''
          }
        }

        // 获取相似职位数据
        if (apiData.positionGuid) {
          await fetchSimilarPositions(apiData.positionGuid, districtId)
        }
        
        // 获取职位推荐数据
        await fetchRecommendedPositions(districtId)
      }
    } catch (error) {
      console.error('获取职位详情失败:', error)
      ElMessage.error('获取职位详情失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 方法
  const hidePhone = (phone) => {
    if (!phone) return ''
    return phone.replace(/(\d{4})\d{4}(\d{4})/, '$1****')
  }

  const hideEmail = (email) => {
    if (!email) return ''
    const [username, domain] = email.split('@')
    return `${username.slice(0, 2)}****@${domain}`
  }

  const showContactInfo = () => {
    showPhone.value = true
    showEmail.value = true
    ElMessage.success('联系方式已显示')
  }

  const collectPosition = () => {
    ElMessage.success('职位收藏成功！')
  }

  const applyPosition = () => {
    ElMessage.info('投递功能开发中...')
  }

  const reportJob = () => {
    ElMessage.info('举报功能开发中...')
  }

  // 滚动监听
  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
    isHeaderFloat.value = scrollTop > 100 // 当滚动超过100px时，头部悬浮
  }

  // 页面挂载
  onMounted(async () => {
    console.log('职位详情页面加载，职位ID:', jobId)
    
    // 获取职位详情数据
    await fetchJobDetail()
    
    window.addEventListener('scroll', handleScroll) // 监听滚动事件
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll) // 移除滚动事件监听
  })
</script>

<style scoped lang="scss">
  /* 职位详情页面专用样式 */
  .job-detail {

    /* 基础样式重置 - 职位详情页面专用 */
    .job-clearfix::after {
      content: "";
      display: table;
      clear: both;
    }

    .job-fl {
      float: left;
    }

    .job-fr {
      float: right;
    }

    .job-link {
      color: #3399ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    /* 列表样式重置 - 职位详情页面专用 */
    .job-list,
    .job-ordered-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .job-list-item {
      list-style: none;
      list-style-type: none;
    }

    /* 头部样式 */
    .job-header {
      min-width: 1200px;
      height: 105px;
      background: #e9ecef url(//image.gxrc.com/gxrcsite/images/jobDetail_top_bg.jpg) no-repeat center top;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;
      width: 100%;
      z-index: 9998;
      transition: all 0.3s ease;
    }

    .job-header-float {
      position: fixed;
      top: 0;
      left: 0;
      height: 70px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .job-detail-hidden {
        display: none;
      }

      .job-base-info .job-pos-wrap {
        padding-top: 15px;
      }

      .job-btn-wrap {
        padding-top: 13px;
      }
    }

    /* 基础信息区域 */
    .job-base-info {
      .job-pos-wrap {
        width: 740px;
        padding: 20px 0 10px;
      }

      .job-title-salary-wrapper {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 5px;
      }

      .job-title {

        line-height: 36px;
        color: #2052a4;
        font-size: 24px;
        font-weight: normal;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin: 0;
      }

      .job-salary {
        flex-shrink: 0;
        line-height: 36px;
        color: #ff6666;
        font-size: 24px;
        font-weight: bold;
      }

      .job-detail-text {
        color: #999;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        .job-separator {
          padding: 0 8px;
          font-size: 12px;
        }
      }
    }

    /* 按钮包装器 */
    .job-btn-wrap {
      width: 450px;
      padding-top: 28px;
      position: relative;

      .job-btn {
        float: right;
        width: 140px;
        font-size: 16px;
        height: 44px;
        margin-right: 15px;
      }

      .job-btn-collection {
        width: 126px;
        border: 1px solid #3399ff;
        background: white;
        color: #3399ff;

        .job-icon {
          font-size: 20px;
          color: #3399ff;
          margin-right: 5px;
          display: inline-block;
          margin-top: -3px;
        }
      }

      .job-btn-apply {
        background: #3399ff;
        color: white;
        border: none;
      }
    }

    /* 主要内容区域 */
    .job-main-wrap {
      padding: 20px 0 20px;
      color: #4c4c4c;
    }

    .job-main-left {
      width:930px;
      padding: 20px 27px 25px;
      border-right: 1px solid #eaeaea;
    }

    .job-main-right {
      width: 260px;
      padding: 30px 15px 25px;
    }

    /* 通用栏目样式 */
    .job-one-column {
      padding-bottom: 20px;

      .job-content,
      .job-content p,
      .job-content pre {
        line-height: 30px;
        color: #4c4c4c;
      }
    }

    /* 标题样式 */
    .job-title-blue {
      font-size: 18px;
      color: #2052a4;
      margin-bottom: 15px;
      position: relative;
      padding-left: 20px;

      .job-border {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: #2052a4;
        border-radius: 2px;
      }
    }

    /* 福利标签 */
    .job-welfare-con {
      padding-top: 20px;

      .job-welfare-item {
        float: left;
        height: 28px;
        line-height: 28px;
        background: #e9ecef;
        color: #39f;
        padding: 0 10px;
        border-radius: 24px;
        margin: 0 10px 10px 0;
      }
    }

    /* 联系信息 */
    .job-contact-info-con {
      .job-contact-text {
        color: #a6a6a6;

        .job-contact-span {
          display: inline-block;
          width: 33%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 25px;
        }

        .job-contact-label {
          color: #4c4c4c;
        }

        .job-contact-btn {
          width: 120px;
          height: 40px;
          line-height: 40px;
          font-size: 14px;
          margin-left: 50px;
        }
      }

      .job-requirements {
        .job-req-w1 {
          width: 25%;
        }

        .job-req-w2 {
          width: 50%;
        }

        .job-req-w3 {
          width: 23%;
        }
      }
    }

    /* 职位推荐 */
    .job-pos-recommend {
      .job-recommend-list {
        width: 900px;
      }

      .job-recommend-item {
        float: left;
        width: 395px;
        height: 70px;
        border-bottom: 1px solid #eceef1;
        padding: 0px 10px;
        margin: 0 20px 10px 0;

        .job-recommend-title {
          height: 30px;

          .job-pos-name,
          .job-pos-salary {
            line-height: 30px;
            font-size: 18px;
          }

          .job-pos-name {
            display: block;
            float: left;
            max-width: 220px;
            color: #34495e;
            font-weight: normal;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .job-pos-salary {
            float: right;
            color: #f66;
          }
        }

        .job-recommend-text {
          line-height: 30px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;

          .job-ent-name {
            color: #999;
          }
        }
      }

      .job-recommend-btn-wrap {
        text-align: center;
        padding-top: 10px;

        .btn-check-more {
          display: inline-block;
          width: 203px;
          height: 48px;
          line-height: 48px;
          border: 1px solid #3399ff;
          background: none;
          color: #3399ff;
          text-align: center;
          font-size: 18px;
          border-radius: 8px;
          transition: all 0.3s;
          cursor: pointer;
          text-decoration: none;
          
          &:hover {
            background: #3399ff;
            color: white;
            text-decoration: none;
          }
        }
      }
      
      .no-recommended-positions {
        padding: 20px 0;
        text-align: center;
        color: #999;
        font-size: 14px;
      }
    }

    /* 右侧栏样式 */
    .job-main-right {
      .job-ent-wrap {
        padding-bottom: 15px;

        .job-ent-logo {
          float: left;
          width: 70px;

          .job-ent-logo-img {
            display: block;
            width: 60px;
            height: 60px;
            border-radius: 8px;
            border: 1px solid #EEEEEE;
          }
        }

        .job-ent-name {
          float: right;
          width: 155px;

          .job-ent-link {
            color: #4c4c4c;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }

      .job-ent-details {
        padding-bottom: 15px;

        .job-ent-detail-item {
          padding: 10px 0;
          display: table;

          .job-ent-detail-cell {
            display: table-cell;
            line-height: 18px;
            color: #767676;
          }

          .job-ent-icon {
            margin-right: 5px;
            color: #4c4c4c;
          }
        }
      }

      .job-ent-btn-wrap {
        text-align: center;
        padding-bottom: 20px;
        position: relative;

        .job-btn-check-all {
          width: 200px;
          font-size: 15px;
          border: 1px solid #3399ff;
          color: #3399ff;
          background: white;
          line-height: 40px;
        }

        .job-company-badge {
          height: 24px;
          line-height: 24px;
          padding: 0 10px;
          font-size: 12px;
          background: #F69B22;
          border-radius: 20px;
          color: #fff;
          position: absolute;
          top: -10px;
          right: 0px;
        }
      }

      .job-btn-look {
        width: 234px;
      }

      .job-interesting {
        .job-interesting-item {
          border-bottom: 1px solid #eceef1;
          padding: 10px 0;

          .job-interesting-title,
          .job-interesting-text {
            line-height: 30px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .job-interesting-link {
            color: #34495e;
            font-size: 16px;
            font-weight: normal;
          }

          .job-interesting-text {
            color: #999;

            &.job-interesting-salary {
              color: #f66;
              font-weight: bold;
            }

            .job-interesting-ent-name {
              color: #999;
            }
          }
        }
        
        .no-related-positions {
          padding: 20px 0;
          text-align: center;
          color: #999;
          font-size: 14px;
        }
      }
    }

    /* 提醒信息 */
    .job-reminder {
      background: #FFFAF0;
      color: #FF9500;
      padding: 10px 0;

      .job-reminder-icon {
        float: left;
        font-size: 20px;
        margin: 10px 15px 0 20px;
      }

      .job-reminder-text {
        float: left;
        width: 1100px;
        color: #FF9500;
        line-height: 22px;

        .job-reminder-link {
          color: #3399ff;
        }
      }

      .job-reminder-close {
        float: right;
        font-size: 30px;
        color: #ddd;
        margin-right: 20px;

        &:hover {
          color: #999;
          text-decoration: none;
        }
      }
    }
  }

  /* 关键词标签 - 职位详情页面专用 */
  .job-keyword {
    float: left;
    margin-right: 12px;
    margin-bottom: 15px;
    margin-top: 10px;
    background: rgba(255, 255, 255, 0.39);
    border: 1px solid #E6E6E6;
    border-radius: 2px;
  }

  .job-keyword-text {
    margin-left: 10px;
    margin-right: 12px;
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
    color: #999999;
  }

  /* 通用按钮样式 - 职位详情页面专用 */
  .job-button {
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: none;
    }
  }

  .job-button-blue {
    color: #fff;
    background-color: #3399ff;
    border-color: #3399ff;

    &:hover {
      background-color: #2d8cee;
      border-color: #2d8cee;
    }
  }

  .job-button-blue-border {
    color: #3399ff;
    background-color: #fff;
    border-color: #3399ff;

    &:hover {
      color: #fff;
      background-color: #3399ff;
      border-color: #3399ff;
    }
  }

  /* 文本内容样式 - 职位详情页面专用 */
  .job-description,
  .job-company-description {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
    margin: 0;
    background: transparent;
    border: none;
    padding: 0;
  }

  /* 保留用户的全局样式 */
  body,
  pre {
    font: 14px/1.5 Microsoft YaHei, tahoma, arial, Hiragino Sans GB, \\5b8b\4f53, sans-serif;
    color: #333
  }

  body,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  p,
  blockquote,
  dl,
  dt,
  dd,
  ul,
  ol,
  li,
  pre,
  form,
  fieldset,
  legend,
  button,
  input,
  textarea,
  th,
  td {
    margin: 0;
    padding: 0
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: 100%;
    font-weight: bold
  }

  strong {
    font-weight: bold
  }

  address,
  cite,
  dfn,
  em,
  var {
    font-style: normal
  }

  small {
    font-size: 12px
  }

  ul,
  ol,
  li {
    list-style: none
  }

  a {
    color: #333;
    text-decoration: none
  }

  a:hover {
    text-decoration: none
  }

  sup {
    vertical-align: text-top
  }

  sub {
    vertical-align: text-bottom
  }

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  legend {
    color: #000
  }

  fieldset,
  img {
    border: 0
  }

  button,
  input,
  select,
  textarea {
    font-size: 100%;
    outline: none;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0
  }

  em,
  i {
    font-style: normal
  }

  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both
  }

  .clearfix {
    zoom: 1
  }

  .fl {
    float: left
  }

  .fr {
    float: right
  }

  .clear {
    clear: both
  }

  .w960 {
    width: 960px;
    margin: 0 auto
  }

  .w1000 {
    width: 1000px;
    margin: 0 auto
  }

  .w1200 {
    width: 1200px;
    margin: 0 auto
  }

  .none {
    display: none
  }

  .red {
    color: red
  }

  .green {
    color: green
  }

  .orange {
    color: #f60
  }

  .bg-white {
    background: #fff
  }

  .tc {
    text-align: center
  }

  .tl {
    text-align: left
  }

  .tr {
    text-align: right
  }

  .ti {
    text-indent: 2em
  }
  
  /* 加载状态样式 */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: #f5f5f5;
  }
  
  .loading-content {
    display: flex;
    align-items: center;
    padding: 20px 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 16px;
    color: #666;
  }
</style>