<script setup lang="ts">
import SearchPageLayout from "@/components/search/SearchPageLayout.vue";
import { useRoute } from "vue-router";
import type { SearchConfig } from "@/types/search";

const route = useRoute();
const isElderly = ref(false);
const searchType = ref<SearchConfig["searchType"]>("search");
const keyword = ref("");
const schType = ref(1);
onMounted(() => {
  isElderly.value = !!route.query?.isElderly;
  searchType.value = isElderly.value ? "elderly" : "search";
  keyword.value = route.query?.keyword as string;
  if (route.query?.schType) {
    schType.value = Number(route.query?.schType);
  }
});

// 大龄工作者搜索的固定参数
const defaultParams = computed(() => ({
  schType: schType.value,
  keyword: keyword.value, // 响应式更新
  ageLimit: 45,
}));
</script>

<template>
  <SearchPageLayout :searchType="searchType" :defaultParams="defaultParams" />
</template>
