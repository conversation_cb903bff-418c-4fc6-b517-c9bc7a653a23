<script setup lang="ts">
import { ref } from 'vue';
import Industry from './component/industry.vue';
import MainCarouselAd from './component/MainCarouselAd.vue';
import News from './component/News.vue';
import City from './component/City.vue';
import JobFair from './component/JobFair.vue';
import BannerAd from './component/BannerAd.vue';
import HotJobs from './component/HotJobs.vue';
import HotEnterprises from './component/HotEnterprises.vue';
import OddJobs from './component/OddJobs.vue';
import Service from './component/Service.vue';

</script>
<template>
  <div class="gx-homepage">

    <!-- 主要内容 -->
    <div class="index-content">
      <div class="main-content">
                <div class="three-column-layout">
                    <!-- 左侧分类 -->
                    <div class="left-sidebar">
                        <Industry />
                    </div>

                    <!-- 中间内容  -->
                    <div class="middle-content">
                      <MainCarouselAd />
                      <News />
                    </div>

                    <!-- 右侧内容 -->
                    <div class="right-sidebar">
                      <City />
                      <JobFair />
                    </div>
                </div>
        </div>

        <BannerAd />
        <HotJobs />
      <HotEnterprises />
      <OddJobs />
      <Service />
  </div>

  </div>
</template>
<style scoped lang="scss">
.gx-homepage {
 .index-content{
  width: 1200px;
  margin: 0 auto;

  .main-content {
    padding: 24px 0;
}

.three-column-layout {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

/* 左侧分类 */
.left-sidebar {
    width: 260px;
    flex-shrink: 0;
}


/* 中间内容 */
.middle-content {
    width: 600px;
    flex-shrink: 0;
}
/* 右侧内容 */
.right-sidebar { width: 308px; flex-shrink: 0; }

 }

 /*通用章节标题 */
 ::v-deep(.section-title) {
    display: flex;
    justify-content: space-between;
    line-height: 55px;
    text-align: left;
    padding: 0px 0 5px;

    h2{
        font-weight: normal;
    font-size: 24px;
    color: #333333;
    }
    a{
        font-weight: 400;
font-size: 14px;
color: #1E58D2;
text-align: right;
    }
}

 /*标签页样式 */
::v-deep(.blue-tabs-wrapper .el-tabs__header) { 
}

::v-deep(.blue-tabs-wrapper .el-tabs__nav-wrap::after) {
    background-color: #F3F3F3;
}

::v-deep(.blue-tabs-wrapper .el-tabs__item) {
    height: 45px;
    line-height: 45px;
    padding: 0 30px 0 0;
    font-weight: normal;
    font-size: 16px;
    color: #000000;
    text-align: center;
}

::v-deep(.blue-tabs-wrapper .el-tabs__item.is-active) {
    color: #145AFE;
}

::v-deep(.blue-tabs-wrapper .el-tabs__active-bar) {
    height: 2px;
    background: linear-gradient(90deg, #83B8FF 0%, #A495FF 100%);
    border-radius: 3px 3px 3px 3px;
}

/* 轮播图样式 */
::v-deep(.el-carousel__indicators--horizontal) {
    bottom: 0px;
    right: 15px;
    left: auto;
    transform: none;
    text-align: right;
}

::v-deep(.el-carousel__indicator) {
    width: 8px;
    height: 8px;
}

::v-deep(.el-carousel__button) {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
}

::v-deep(.el-carousel__indicator.is-active .el-carousel__button) {
    background-color: #1E58D2;
}

/* 分页组件样式 */
::v-deep(.el-pagination.is-background .el-pager li) {
    background-color: #f3f4f6;
    color: #6b7280;
}

::v-deep(.el-pagination.is-background .el-pager li.is-active) {
    background-color: #1E58D2;
    color: #fff;
}

/* 下拉菜单样式 */
::v-deep(.el-dropdown-menu) {
    border: 1px solid #e5e7eb;
}

::v-deep(.el-dropdown-menu__item:hover) {
    background-color: #f3f4f6;
    color: #1E58D2;
}

}
</style>