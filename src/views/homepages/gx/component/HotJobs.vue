<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";
import Pagination from './Pagination.vue'
import JobCard from './JobCard.vue'
import { useCityStore } from '@/stores/cityStore'
import mockData from '../mock'

const cityStore = useCityStore()

// 定义职位数据接口
interface Position {
    EducationDegree: null | string
    WorkCity: string
    WorkPlace: string
    WorkYear: string
    EnterpriseId: number
    EnterpriseGuid: string
    EnterpriseName: string
    EnterpriseProperty: string
    EnterpriseEmployeeNumber: null | string
    PositionAmount: string
    RequirementOfEducationSpecialty: string
    RequirementOfEducationDegree: string
    EmergencyRrecruitmentFlag: boolean
    EnterpriseIndustry: string
    LogoUrl: string
    PositionId: number
    PositionGuid: string
    PositionName: string
    Pay: string
    IsReceiveGraduate: boolean
    PublishTime: string
    WorkProperty?: number
}

interface PositionCategory {
    PositionCareerID: number
    PositionCareerName: string
    positionList: Position[]
}

const activeJobTab = ref('');
// 为每个tab维护独立的分页状态
const tabPageStates = ref<Record<string, { currentPage: number, autoTimer: number | null }>>({})
const pageSize = ref(9)
const hovered = ref(false)

const hotPositionData = ref<PositionCategory[]>([]);

// 获取当前tab的分页状态
const getCurrentTabState = () => {
    if (!tabPageStates.value[activeJobTab.value]) {
        tabPageStates.value[activeJobTab.value] = {
            currentPage: 1,
            autoTimer: null
        }
    }
    return tabPageStates.value[activeJobTab.value]
}

// 计算属性 - 当前tab的当前页数据
const currentTabPositions = computed(() => {
    const categoryData = processedHotPositionData.value[activeJobTab.value]
    if (!categoryData) return []

    const currentState = getCurrentTabState()
    const start = (currentState.currentPage - 1) * pageSize.value
    const end = start + pageSize.value
    return categoryData.slice(start, end)
})

// 计算属性 - 当前tab的总页数
const currentTabTotalPages = computed(() => {
    const categoryData = processedHotPositionData.value[activeJobTab.value]
    if (!categoryData) return 1
    return Math.ceil(categoryData.length / pageSize.value)
})

// 计算属性 - 当前tab的当前页码
const currentTabPageNumber = computed(() => {
    return getCurrentTabState().currentPage
})

// 自动翻页控制方法
const startAutoPage = (tabKey: string) => {
    const tabState = tabPageStates.value[tabKey]
    if (!tabState || hovered.value) return

    // 清除之前的定时器
    if (tabState.autoTimer) {
        clearInterval(tabState.autoTimer)
    }

    tabState.autoTimer = window.setInterval(() => {
        if (!hovered.value && activeJobTab.value === tabKey) {
            nextPageEvent()
        }
    }, 5000)
}

const stopAutoPage = (tabKey: string) => {
    const tabState = tabPageStates.value[tabKey]
    if (tabState && tabState.autoTimer) {
        clearInterval(tabState.autoTimer)
        tabState.autoTimer = null
    }
}

// 方法定义
const nextPageEvent = () => {
    const currentState = getCurrentTabState()
    const totalPages = currentTabTotalPages.value

    if (currentState.currentPage < totalPages) {
        currentState.currentPage++
    } else {
        currentState.currentPage = 1 // 循环到第一页
    }
}

const prevPageEvent = () => {
    const currentState = getCurrentTabState()
    const totalPages = currentTabTotalPages.value

    if (currentState.currentPage > 1) {
        currentState.currentPage--
    } else {
        currentState.currentPage = totalPages // 循环到最后一页
    }
}

// 悬停控制
const handleMouseEnter = () => {
    hovered.value = true
    stopAutoPage(activeJobTab.value)
}

const handleMouseLeave = () => {
    hovered.value = false
    startAutoPage(activeJobTab.value)
}

const hotPositionCategories = computed(() => {
    return hotPositionData.value.map(category => {
        return {
            id: category.PositionCareerID,
            name: category.PositionCareerName,
            key: 'category_' + category.PositionCareerID
        }
    })
})
const processedHotPositionData = computed(() => {
    let processedData: { [key: string]: any } = {};

    hotPositionData.value.forEach(function (category) {
        if (category.positionList && category.positionList.length > 0) {
            let categoryKey = 'category_' + category.PositionCareerID;
            processedData[categoryKey] = category.positionList;
        }
    });

    return processedData;
})
// tab切换处理
const hotJobsTabChange = (tab: string) => {
    // 停止之前tab的自动翻页
    Object.keys(tabPageStates.value).forEach(tabKey => {
        stopAutoPage(tabKey)
    })

    // 确保新tab有初始状态
    if (!tabPageStates.value[tab]) {
        tabPageStates.value[tab] = {
            currentPage: 1,
            autoTimer: null
        }
    } else {
        // 重置到第一页
        tabPageStates.value[tab].currentPage = 1
    }

    // 启动新tab的自动翻页
    setTimeout(() => {
        startAutoPage(tab)
    }, 100)
}

// 初始化数据和事件
onMounted(() => {
    hotPositionData.value = mockData.hotPositionData as PositionCategory[]

    // 设置初始tab
    if (hotPositionData.value.length > 0) {
        activeJobTab.value = `category_${hotPositionData.value[0].PositionCareerID}`

        // 初始化第一个tab的状态
        tabPageStates.value[activeJobTab.value] = {
            currentPage: 1,
            autoTimer: null
        }

        // 启动自动翻页
        setTimeout(() => {
            startAutoPage(activeJobTab.value)
        }, 500)
    }

    // 添加鼠标悬停事件监听器 (延迟执行确保DOM渲染完成)
    setTimeout(() => {
        const jobsContainer = document.querySelector('.hot-jobs .hot-jobs-container')
        if (jobsContainer) {
            jobsContainer.addEventListener('mouseenter', handleMouseEnter)
            jobsContainer.addEventListener('mouseleave', handleMouseLeave)
        }

        // 为热招职位的tab项添加鼠标悬停事件
        const jobTabItems = document.querySelectorAll('.el-tabs__item');
        jobTabItems.forEach((tabItem) => {
            tabItem.addEventListener('mouseenter', function (this: HTMLElement) {
                const tabName = this.getAttribute('aria-controls');
                if (tabName && tabName.includes('category_')) {
                    const categoryMatch = tabName.match(/category_(\d+)/);
                    if (categoryMatch) {
                        const targetTab = 'category_' + categoryMatch[1];
                        if (targetTab !== activeJobTab.value) {
                            activeJobTab.value = targetTab;
                            hotJobsTabChange(targetTab);
                        }
                    }
                }
            });
        });
    }, 200)
})

// 组件卸载时清理
onBeforeUnmount(() => {
    // 清除所有定时器
    Object.keys(tabPageStates.value).forEach(tabKey => {
        stopAutoPage(tabKey)
    })

    // 移除事件监听器
    const jobsContainer = document.querySelector('.hot-jobs .hot-jobs-container')
    if (jobsContainer) {
        jobsContainer.removeEventListener('mouseenter', handleMouseEnter)
        jobsContainer.removeEventListener('mouseleave', handleMouseLeave)
    }
})

// 监听tab切换
watch(activeJobTab, (newTab, oldTab) => {
    if (newTab !== oldTab && newTab) {
        hotJobsTabChange(newTab)
    }
})

</script>

<template>
    <div class="hot-jobs blue-tabs-wrapper">
        <div class="container">
            <div class="section-title"><h2>热招职位</h2><router-link :to="cityStore.getCityPagePath('company')">查看更多>></router-link></div>
            <el-tabs v-model="activeJobTab" @tab-change="hotJobsTabChange">
                <el-tab-pane v-for="category in hotPositionCategories" :key="category.key" :label="category.name"
                    :name="category.key">
                    <div class="hot-jobs-container category-jobs-container">
                        <transition name="fade" mode="out-in">
                            <div class="jobs-grid" :key="currentTabPageNumber">
                                <div v-for="(job, index) in currentTabPositions" :key="index" class="job-cards">
                                    <JobCard :job="job" />
                                </div>
                            </div>
                        </transition>
                        <!-- 分页组件 -->
                        <Pagination :current-page="currentTabPageNumber" :total-pages="currentTabTotalPages"
                            :show-pagination="currentTabTotalPages > 1" @prev="prevPageEvent" @next="nextPageEvent" />
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.hot-jobs {
    padding: 0px 0 24px;

    ::v-deep(.el-tabs__item) {
        padding: 0 20px;
    }

    ::v-deep(.el-tabs__item.is-active) {
        font-weight: bold !important;
    }

    .hot-jobs-container {
        height: 530px;
    }

    .category-jobs-container {
        .jobs-grid {
            height: 469px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 143px);
            gap: 20px;
            align-content: start;
        }

    }

    /* Vue 3 过渡动画 */
    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }

}
</style>
