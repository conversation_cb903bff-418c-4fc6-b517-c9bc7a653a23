<script setup lang="ts">
// 通用分页组件
interface Props {
  currentPage: number
  totalPages: number
  showPagination?: boolean
}

interface Emits {
  prev: []
  next: []
}

const props = withDefaults(defineProps<Props>(), {
  showPagination: true
})

const emit = defineEmits<Emits>()

const handlePrevPage = () => {
  emit('prev')
}

const handleNextPage = () => {
  emit('next')
}
</script>

<template>
  <!-- 分页组件 -->
  <div v-if="showPagination && totalPages > 1" class="pagination-wrapper">
    <img 
      class="page-arrow prev" 
      @click="handlePrevPage" 
      src="https://image.gxrc.com/gxrcsite/rcw/2025/hot_icon_left.png" 
      alt="上一页" 
    />
    <span class="page-info">
      <span class="page-info-num">{{ currentPage }}</span>/{{ totalPages }}
    </span>
    <img 
      class="page-arrow next" 
      @click="handleNextPage" 
      src="https://image.gxrc.com/gxrcsite/rcw/2025/hot_icon_right.png" 
      alt="下一页" 
    />
  </div>
</template>

<style lang="scss" scoped>
/* 分页组件样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 12px;
}

.page-arrow {
  width: 20px;
  height: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-arrow:hover {
  opacity: 0.8;
}

.page-info {
  color: #a7a7a7;
  font-weight: 400;
  font-size: 14px;
  min-width: 30px;
  text-align: center;
}

.page-info-num {
  color: #1e58d2;
}
</style>
