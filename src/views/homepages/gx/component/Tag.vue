<script setup lang="ts">
import { ref, computed } from "vue";

interface Props {
    job: object
}
const props = withDefaults(defineProps<Props>(), {
    job: {
        EmergencyRrecruitmentFlag: false,
        IsReceiveGraduate: false,
        WorkProperty: 0
    }
})
</script>

<template>
<el-tag v-if="job.EmergencyRrecruitmentFlag" type="danger" size="small"
                                                class="el-tag-biao el-tag-ji">急</el-tag>
                                            <el-tag v-if="job.IsReceiveGraduate" type="danger" size="small"
                                                class="el-tag-biao el-tag-bi">毕</el-tag>
                                            <el-tag v-if="job.WorkProperty == 1009" type="danger" size="small"
                                                class="el-tag-biao el-tag-shi">实</el-tag>
                                            <el-tag v-if="job.WorkProperty == 916" type="danger" size="small"
                                                class="el-tag-biao el-tag-jian">兼</el-tag>
</template>

<style lang="scss" scoped>
/* 急聘标签样式 */
.el-tag.el-tag-biao { 
    background-color: #FF6666; 
    border: none; 
    color: #fff; 
    padding: 0 2px; 
    height: 16px; 
    line-height: 15px; 
    font-size: 12px;
    border-radius: 4px;
}
.el-tag.el-tag-ji { background-color: #FF6666; }
.el-tag.el-tag-bi { background-color: #65cc88; }
.el-tag.el-tag-shi { background-color: #37A1F7; }
.el-tag.el-tag-jian { background-color: #FC911D; }

</style>
