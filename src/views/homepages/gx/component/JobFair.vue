<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCityStore } from '@/stores/cityStore'
import mockData from '../mock'
import { useRouter } from 'vue-router'

const router = useRouter()
const cityStore = useCityStore()

// 招聘会数据接口
interface JobFair {
    ArticleID: number
    ArticleTitle: string
    ArticleUrl: string
    ArticleHits: number
    ArticleCategoryID: number
    TabStr: string[]
    ArticlePublishTime: string
}

// 响应式数据
const jobFairs = ref<JobFair[]>([])
const activeTab = ref('招聘会')

// 初始化数据
onMounted(() => {
    jobFairs.value = mockData.jobFairData as JobFair[]
})

// 日期tabs
const dateTabs = computed(() => {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)
    const dayAfterTomorrow = new Date(today)
    dayAfterTomorrow.setDate(today.getDate() + 2)

    // 获取星期几的映射
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

    return [
        { key: '招聘会', name: '招聘会', active: true },
        { 
            key: 'today', 
            name: `${today.getMonth() + 1}/${today.getDate()}`, 
            weekday: weekdays[today.getDay()],
            date: today 
        },
        { 
            key: 'tomorrow', 
            name: `${tomorrow.getMonth() + 1}/${tomorrow.getDate()}`, 
            weekday: weekdays[tomorrow.getDay()],
            date: tomorrow 
        },
        { 
            key: 'dayAfter', 
            name: `${dayAfterTomorrow.getMonth() + 1}/${dayAfterTomorrow.getDate()}`, 
            weekday: weekdays[dayAfterTomorrow.getDay()],
            date: dayAfterTomorrow 
        },
        { key: 'more', name: '查看更多' }
    ]
})

// 根据活跃tab过滤招聘会数据
const filteredJobFairs = computed(() => {
    if (activeTab.value === '招聘会') {
        return jobFairs.value.slice(0, 5) // 显示前5条
    }
    // 其他tab可以根据日期过滤，这里暂时返回前5条
    return jobFairs.value.slice(0, 5)
})

// 获取招聘会类型标签文本
const getJobFairTypeText = (tabStr: string[]) => {
    if (!tabStr || tabStr.length === 0) return '综合'
    return (tabStr[0] || '').split(',')[0].trim()
}

// 获取类型标签样式
const getJobFairTypeClass = (tabStr: string[]) => {
    if (!tabStr || tabStr.length === 0) return 'general'
    
    const str = getJobFairTypeText(tabStr)
    if (str.includes('校园')) return 'campus'
    if (str.includes('网络')) return 'online'
    if (str.includes('综合')) return 'general'
    return 'general'
}

// tab切换
const handleTabHover = (tab: any) => {
    if (tab.key != 'more') {
        activeTab.value = tab.key
    }
}

// 招聘会点击
const goRouter = (url: string) => {
    router.push(url)
}

</script>

<template>
    <div class="job-fair">
        <!-- 日期tabs -->
        <div class="date-tabs">
            <div 
                v-for="tab in dateTabs" 
                :key="tab.key"
                class="date-tab"
                :class="{ 'active': activeTab === tab.key }"
                @mouseenter="handleTabHover(tab)"
            >
                <div v-if="tab.weekday" class="date-content">
                    <div class="text date-text">{{ tab.name }}</div>
                    <div class="text weekday-text">{{ tab.weekday }}</div>
                </div>
                <div v-else-if="tab.key === 'more'" @click="goRouter(cityStore.getCityPagePath('jobfair'))">{{ tab.name }}</div>
                <div v-else>{{ tab.name }}</div>
            </div>
        </div>
        
        <!-- 招聘会列表 -->
        <div class="job-fair-list">
            <div 
                v-for="jobFair in filteredJobFairs" 
                :key="jobFair.ArticleID"
                class="job-fair-item"
                @click="goRouter(jobFair.ArticleUrl)"
            >
                <div class="job-fair-content">
                    <span 
                        class="job-fair-type"
                        :class="getJobFairTypeClass(jobFair.TabStr)"
                    >
                        {{ getJobFairTypeText(jobFair.TabStr) }}
                    </span>
                    <span class="job-fair-title" :title="jobFair.ArticleTitle">
                        {{ jobFair.ArticleTitle }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.job-fair {
    height: 189px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}

.date-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.date-tab {
    flex: 1;
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 10px;
    color: #666666;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    border-left: 1px solid #fff;
    background: #E2E2E2;
    
    &:first-child {
        font-weight: normal;
font-size: 14px;
border-left: none;
    }
    
    &.active {
        color: #fff;
        background:#1E58D2;
    }
    
    .date-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    .text{
        font-size: 10px;
        line-height: 10px;
    }
    .date-text {
        padding-top: 3px;
    }
}

.job-fair-list {
    padding: 5px 10px 0 10px;
}

.job-fair-item {
    padding: 4px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:last-child {
        border-bottom: none;
    }
    
    &:hover {
        background: #f8f9fa;
        padding-left: 8px;
        padding-right: 8px;
        margin: 0 -8px;
        border-radius: 4px;
    }
}

.job-fair-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.job-fair-type {
    flex-shrink: 0;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    
    &.campus {
        background: #EDF8F5;
        color: #48DF9E;
    }
    
    &.general {
        background: #ECF9FC;
    color: #365EAF;
    }
    
    &.online {
        background: #EBF4F9;
        color: #17C1DC;
    }
}

.job-fair-title {
    flex: 1;
    font-size: 14px;
    color: #333333;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    &:hover {
        color: #4a90e2;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .date-tab {
        padding: 10px 4px;
        font-size: 12px;
    }
    
    .job-fair-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .job-fair-title {
        white-space: normal;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
</style>
