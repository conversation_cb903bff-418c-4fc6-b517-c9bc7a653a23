<script setup lang="ts">
import { ref, computed } from "vue";

const bannerAds = ref([
    {
        LogoID: '1',
        LinkUrl: '#',
        LogoSrc: 'https://image.gxrc.com/gxrcsite/rcw/2025/bysfw.jpg?v=20250808',
        LogoName: '南宁高校就业服务专区'
    },
    {
        LogoID: '2',
        LinkUrl: '#',
        LogoSrc: 'https://image.gxrc.com/gxrcsite/rcw/2025/bysfw.jpg?v=20250808',
        LogoName: '南宁高校就业服务专区'
    }
])

const logoClickLog = (logoID: string) => {
    console.log(logoID);
}
</script>

<template>
    <!-- 横幅广告 -->
    <div class="banner-ad-section">
            <div class="banner-ad" v-for="(bannerAd, index) in bannerAds" :key="index">
                <a :href="bannerAd.LinkUrl || '#'" target="_blank" @click="logoClickLog(bannerAd.LogoID)">
                    <el-image :src="bannerAd.LogoSrc" :alt="bannerAd.LogoName" class="banner-ad-image" lazy></el-image>
                </a>
            </div>
    </div>
</template>

<style lang="scss" scoped>
.banner-ad-section {
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;

    .banner-ad {
        width: 592px;
        height: 80px;
        overflow: hidden;
        padding-bottom: 14px;
    }

    .banner-ad-image {
        display: block;
        width: 100%;
        height: 100%;

        img {
            display: block;
        }
    }

    .banner-ad-section {}
}

</style>
