<script setup lang="ts">
import { ref, computed } from "vue";

const talentServices = [
                { title: '职称服务', subtitle: '人力资源行业正高、副高级职称评审、工程系列副高、中初级职称评审', backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_zcfw.png',url:'https://www.gxrczc.com/' },
                { title: '公共培训', subtitle: '人社业务/岗前、在岗/专业科目/学历教育', backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_ggpx.png',url:'https://gg.gxzgrc.com/' },
                { title: '人才培训', subtitle: '多层次、综合性、含金量高的各类培训',  backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_rcpx.png',url:'https://px.gxrcpx.com/' },
                { title: '人才考评', subtitle: '专业人才评价   科学精准服务',backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_rckp.png',url:'https://bgshare.gxrc.org/talentevaluation/about' },
                { title: '劳务派遣', subtitle: '派遣/外包/薪酬福利/劳动合规咨询', backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_lwpq.png',url:'https://www.gxsfht.com/#/Home' },
                { title: '党员之家', subtitle: '在线交纳党费、党员信息查询、党员教育',backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_dyzj.png',url:'https://dyzj.gxrc.com/' },
                { title: '单位档案服务', subtitle: '档案管理系统、档案数字化、档案寄存、档案整理等', backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_jyzd.png?v=2',url:'https://www.gxrchr.com' },
                { title: '八桂英才网', subtitle: '广西高层次人才“一站式”服务平台', backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_bgycw.png', url:'https://www.gxrc.org/' },
                { title: '广西人力资源服务行业协会', subtitle: '专业培训 · 人力资源咨询 · 权威人才评价', backgroundImage: 'https://image.gxrc.com/gxrcsite/rcw/2025/zone_xh.png',url:'https://www.gxrlzy.com/' }
            ]
</script>

<template>
   <div class="talent-market">
                <div class="container">
                    <div class="section-title"><h2>更多服务</h2></div>
                    <div class="services-grid">
                        <div v-for="(service, index) in talentServices"
                             :key="index"
                             class="service-card"
                             :style="{ backgroundImage: `url(${service.backgroundImage})` }">
                            <a :href="service.url" target="_blank">
                                <div class="service-content">
                                    <h3 class="service-title">{{ service.title }}</h3>
                                    <p class="service-subtitle">{{ service.subtitle }}</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
</template>

<style lang="scss" scoped>

.talent-market {
    padding: 0px 0 24px;

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.service-card,
.service-content {
    height: 100px;
    box-sizing: border-box;
}

.service-card {
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.3s ease;
    border-radius: 8px;

    a {
    display: block;
}

&:hover {
    box-shadow: 0px 6px 12px 1px rgba(111, 146, 199, 0.27);
}
}

.service-content {
    padding: 20px 90px 0 20px;
}

.service-title {
    font-weight: bold;
    font-size: 18px;
    color: #333333;
    text-align: left;
    padding-bottom: 0px;
}

.service-subtitle {
    font-weight: 400;
    font-size: 14px;
    color: #646464;
    text-align: left;
}
}

</style>
