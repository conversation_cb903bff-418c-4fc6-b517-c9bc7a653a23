<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useCityStore } from '@/stores/cityStore'
import Pagination from './Pagination.vue'
import Tag from './Tag.vue'
// import { EnterpriseApi } from '@/api-services'
// import { getPcAPI } from '@/utils/axios-utils'

import mockData from '../mock'

// 使用城市Store
const cityStore = useCityStore()

// 模拟热门企业数据接口
interface HotEnterprise {
    EnterpriseGuid: string
    EnterpriseName: string
    LogoSrc: string
    LogoID?: string | number
    LogoName?: string
    LinkUrl?: string
    SourcePic?: string
    EnterpriseID?: number
    Industry?: string
    EnterpriseScale?: string
    EnterpriseProperty?: string
    positionList?: Array<{
        PositionGuid: string
        PositionName: string
        PayPackage: string
        WorkCity?: string
        RequirementOfWorkAge?: string
        RequirementOfEducationDegree?: string
        PositionAmount?: string
        EmergencyRrecruitmentFlag?: boolean
        IsReceiveGraduate?: boolean
        WorkProperty?: number
        WorkPropertyName?: string
        EnterpriseId?: number
        EnterPriseGuid?: string
        EnterpriseName?: string | null
        PositionId?: number
    }>
}

// 响应式数据
const hotEnterprises = ref<HotEnterprise[]>([])
const currentPage = ref(1)
const pageSize = ref(6)
const autoTimer = ref<number | null>(null)
const hovered = ref(false)

// 计算属性
const currentHotEnterprises = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return hotEnterprises.value.slice(start, end)
})

const totalPages = computed(() => {
    return Math.ceil(hotEnterprises.value.length / pageSize.value)
})

// 方法定义
const nextPageEvent = () => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++
    } else {
        currentPage.value = 1 // 循环到第一页
    }
}

const prevPageEvent = () => {
    if (currentPage.value > 1) {
        currentPage.value--
    } else {
        currentPage.value = totalPages.value // 循环到最后一页
    }
}

const startHotEnterpriseAutoPage = () => {
    stopHotEnterpriseAutoPage() // 先清除之前的定时器
    // 只有在没有悬停时才启动自动翻页
    if (!hovered.value) {
        autoTimer.value = window.setInterval(() => {
            nextPageEvent()
        }, 5000) // 5秒自动翻页
    }
}

const stopHotEnterpriseAutoPage = () => {
    if (autoTimer.value) {
        clearInterval(autoTimer.value)
        autoTimer.value = null
    }
}

const handleHotEnterprisesMouseEnter = () => {
    hovered.value = true
    stopHotEnterpriseAutoPage()
}

const handleHotEnterprisesMouseLeave = () => {
    hovered.value = false
    startHotEnterpriseAutoPage()
}

const logoClickLog = (logoID?: string | number) => {
    // 记录Logo点击日志的逻辑
    if (logoID) {
        console.log('Logo clicked:', logoID)
        // TODO: 实现真实的点击统计逻辑
    }
}

// 真实API数据加载函数（示例） - 当需要时取消注释使用
// const loadHotEnterprisesData = async () => {
//   try {
//     const response = await getPcAPI(EnterpriseApi).apiEnterpriseHotListGet()
//     hotEnterprises.value = response.data || []
//   } catch (error) {
//     console.error('加载热门企业数据失败:', error)
//   }
// }

// 生命周期
onMounted(() => {
    // 模拟数据加载
    // TODO: 替换为真实的API调用
    hotEnterprises.value = mockData.hotEnterprises as HotEnterprise[]

    // 启动自动翻页
    startHotEnterpriseAutoPage()

    // 添加悬停事件监听器 (延迟执行确保DOM渲染完成)
    setTimeout(() => {
        const enterpriseContainer = document.querySelector('.hot-enterprises .hot-enterprises-container')
        if (enterpriseContainer) {
            enterpriseContainer.addEventListener('mouseenter', handleHotEnterprisesMouseEnter)
            enterpriseContainer.addEventListener('mouseleave', handleHotEnterprisesMouseLeave)
        }
    }, 100)
})

onBeforeUnmount(() => {
    // 清除定时器
    stopHotEnterpriseAutoPage()

    // 移除事件监听器
    const enterpriseContainer = document.querySelector('.hot-enterprises .hot-enterprises-container')
    if (enterpriseContainer) {
        enterpriseContainer.removeEventListener('mouseenter', handleHotEnterprisesMouseEnter)
        enterpriseContainer.removeEventListener('mouseleave', handleHotEnterprisesMouseLeave)
    }
})
</script>

<template>
    <div class="hot-enterprises">
        <div class="container">
            <div class="section-title"><h2>热门企业</h2><router-link :to="cityStore.getCityPagePath('company')">查看更多>></router-link></div>
            <div class="hot-enterprises-container">
                <Transition name="fade" mode="out-in">
                    <div class="enterprises-grid" :key="currentPage">
                        <div v-for="(enterprise, index) in currentHotEnterprises" :key="index" class="enterprise-card">
                            <router-link :to="cityStore.getCityPagePath(`company/${enterprise.EnterpriseGuid}`)"
                                @click="logoClickLog(enterprise.LogoID)">
                                <div class="enterprise-header">
                                    <el-image :src="enterprise.LogoSrc" :alt="enterprise.EnterpriseName"
                                        class="enterprise-logo" />
                                    <div class="enterprise-info">
                                        <h3 class="enterprise-name" :title="enterprise.EnterpriseName">
                                            {{ enterprise.EnterpriseName }}
                                        </h3>
                                        <div class="enterprise-meta"
                                            :title="[enterprise.Industry, enterprise.EnterpriseScale, enterprise.EnterpriseProperty].filter(x => x).join(' | ')">
                                            <span v-if="enterprise.Industry">{{ enterprise.Industry }}</span>
                                            <span
                                                v-if="enterprise.Industry && (enterprise.EnterpriseScale || enterprise.EnterpriseProperty)"
                                                class="vl">|</span>
                                            <span v-if="enterprise.EnterpriseScale">{{ enterprise.EnterpriseScale
                                                }}</span>
                                            <span v-if="enterprise.EnterpriseScale && enterprise.EnterpriseProperty"
                                                class="vl">|</span>
                                            <span v-if="enterprise.EnterpriseProperty">{{ enterprise.EnterpriseProperty
                                                }}</span>
                                        </div>
                                    </div>
                                </div>
                            </router-link>
                            <div class="enterprise-jobs">
                                <div v-for="(job, jobIndex) in (enterprise.positionList || []).slice(0, 2)"
                                    :key="jobIndex" class="enterprise-job-item">
                                    <router-link :to="cityStore.getCityPagePath(`jobDetail/${job.PositionGuid}`)"
                                        @click="logoClickLog(enterprise.LogoID)">
                                        <div class="job-header">
                                            <div class="job-title-wrapper">
                                                <span class="job-title" :title="job.PositionName">{{ job.PositionName
                                                    }}</span>
                                                <Tag :job="job" />
                                            </div>
                                            <span class="job-salary">{{ job.PayPackage }}</span>
                                        </div>
                                        <div class="job-details">
                                            <span v-if="job.WorkCity" class="job-location">{{ job.WorkCity }}</span>
                                            <span v-if="job.RequirementOfWorkAge" class="job-experience">{{
                                                job.RequirementOfWorkAge }}</span>
                                            <span v-if="job.RequirementOfEducationDegree" class="job-education">{{
                                                job.RequirementOfEducationDegree }}</span>
                                            <span
                                                v-if="job.PositionAmount && job.PositionAmount !== '0' && job.PositionAmount !== '0人'"
                                                class="job-count">招{{ job.PositionAmount }}</span>
                                        </div>
                                    </router-link>
                                </div>
                                <div class="view-more-wrapper">
                                    <router-link
                                        :to="cityStore.getCityPagePath(`company/${enterprise.EnterpriseGuid}`)"
                                        class="view-more-btn" @click="logoClickLog(enterprise.LogoID)">
                                        查看更多职位
                                    </router-link>
                                </div>
                            </div>
                        </div>
                    </div>
                </Transition>

                <!-- 使用通用分页组件 -->
                <Pagination :current-page="currentPage" :total-pages="totalPages" @prev="prevPageEvent"
                    @next="nextPageEvent" />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.hot-enterprises {
    padding: 0px 0 24px;


    .enterprises-grid {
        height: 660px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        box-sizing: border-box;
    }

    .enterprise-card {
        background-color: #fff;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-sizing: border-box;
        width: 386px;
        height: 320px;
        display: flex;
        flex-direction: column;
    }

    .enterprise-card:hover {
        box-shadow: 0px 6px 12px 1px rgba(111, 146, 199, 0.27);
    }

    .enterprise-header {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        height: 82px;
        padding: 8px;
        box-sizing: border-box;
        background: linear-gradient(269deg, #ffffff 0%, #eefbff 100%);
        border-radius: 8px 8px 0px 0px;
    }

    .enterprise-logo {
        width: 64px;
        height: 64px;
        border: 1px solid #fff;
        border-radius: 2px;
        background-color: #fff;
        flex-shrink: 0;
    }

    .enterprise-info {
        flex: 1;
        min-width: 0;
        max-width: 280px;
    }

    .enterprise-name {
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        padding: 5px 0 4px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .enterprise-meta {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 280px;
        width: 100%;

        .vl {
            font-size: 14px;
            color: #c5c5c5;
            white-space: pre;
        }
    }


    .enterprise-jobs {
        padding: 10px 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .enterprise-job-item {
        padding: 8px 0;

        &:last-of-type {
            border-bottom: none;
            margin-bottom: 8px;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .job-title-wrapper {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            gap: 6px;
        }

        .job-title {
            max-width: 240px;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .job-salary {
            font-weight: 400;
            font-size: 16px;
            color: #ff6666;
        }

        .job-details {
            display: flex;
            gap: 12px;

            span {
                white-space: nowrap;
                background: #f8f8f8;
                border-radius: 4px 4px 4px 4px;
                padding: 6px 8px;
                font-size: 14px;
                color: #a1a1a1;
            }
        }

    }

    .view-more-wrapper {
        text-align: center;
        padding: 12px 0 10px;
        margin-top: auto;


        .view-more-btn {
            font-weight: 400;
            font-size: 16px;
            color: #1e58d2;
            text-decoration: none;
            border: 1px solid #1e58d2;
            border-radius: 8px;
            padding: 6px 30px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .view-more-btn:hover {
            box-shadow: 0px 6px 12px 1px rgba(111, 146, 199, 0.27);
        }
    }


    /* 热门企业分页容器和过渡动画 */
    .hot-enterprises-container {
        position: relative;
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s ease;
    }

    .fade-enter,
    .fade-leave-to {
        opacity: 0;
    }
}
</style>
