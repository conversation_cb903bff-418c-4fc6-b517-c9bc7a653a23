<script setup lang="ts">
import { ref, computed } from "vue";
import Tag from './Tag.vue'
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()

interface Props {
    job: object
}
const props = withDefaults(defineProps<Props>(), {
    job: {
        
    }
})
</script>

<template>
<div class="job-card">
    <router-link :to="cityStore.getCityPagePath(`jobDetail/${job.PositionGuid}`)">
                                    <div class="job-header">
                                        <div class="job-title-wrapper">
                                            <span class="job-title" :title="job.PositionName">{{ job.PositionName
                                                }}</span>
                                            <Tag :job="job" />
                                        </div>
                                        <span class="job-salary">{{ job.Pay }}</span>
                                    </div>
                                    <div class="job-info">
                                        <span class="job-location">{{ job.WorkCity }}</span>
                                        <span class="job-experience">{{ job.WorkYear }}</span>
                                        <span class="job-education">{{ job.RequirementOfEducationDegree }}</span>
                                        <span class="job-education"
                                            v-if="job.PositionAmount && job.PositionAmount !== '0' && job.PositionAmount !== '0人'">招{{ job.PositionAmount
                                            }}</span>
                                    </div>
                                </router-link>
                                <router-link :to="cityStore.getCityPagePath(`company/${job.EnterpriseGuid}`)">
                                    <div class="job-footer">
                                        <div class="company-info">
                                            <img :src="job.LogoUrl" :alt="job.EnterpriseName" class="company-logo" />
                                            <span class="company-name" :title="job.EnterpriseName">{{ job.EnterpriseName
                                                }}</span>
                                        </div>
                                        <span class="company-tags">{{ job.EnterpriseProperty }}</span>
                                    </div>
                                </router-link>
</div>
</template>

<style lang="scss" scoped>
.job-card{
        width: 386px;
        height: 143px;
        background-color: #fff;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-sizing: border-box;

    .job-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px 0px;
    }

    .job-title-wrapper {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        gap: 6px;
    }

    .job-title {
        max-width: 220px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .job-salary {
        font-weight: 400;
        font-size: 16px;
        color: #FF6666;
    }

    /* 保证job-info内容单行溢出省略号 */
    .job-info {
        display: block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 15px 20px 20px;
    }

    .job-info span {
        display: inline-block;
        margin-right: 6px;
        font-size: 14px;
        color: #A1A1A1;
        background: #F8F8F8;
        border-radius: 4px;
        padding: 4px 10px;
        /* 取消单独省略相关属性 */
    }

    .job-footer {
        height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        padding: 10px;
        background: linear-gradient(269deg, #FFFFFF 0%, #EEFBFF 100%);
        border-radius: 0px 0px 8px 8px;
        box-sizing: border-box;
    }

    .company-info {
        display: flex;
        align-items: center;
        flex: 1;
        padding-right: 15px;
        min-width: 0;
    }

    .company-logo {
        width: 32px;
        height: 32px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #FFFFFF;
        margin-right: 8px;
        flex-shrink: 0;
    }

    .company-name {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .company-tags {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
    }

    .company-tags .vl {
        color: #C5C5C5;
        padding: 0 4px;
    }
}


.job-card:hover {
        box-shadow: 0px 6px 12px 1px rgba(111, 146, 199, 0.27);
    }
</style>
