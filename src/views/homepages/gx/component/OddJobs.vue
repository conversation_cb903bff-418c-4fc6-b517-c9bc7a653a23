<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import Pagination from './Pagination.vue'
import JobCard from './JobCard.vue'
import { useCityStore } from '@/stores/cityStore'
import mockData from '../mock'

const cityStore = useCityStore()

// 定义职位数据接口
interface Position {
    EducationDegree: null | string
    WorkCity: string
    WorkPlace: string
    WorkYear: string
    EnterpriseId: number
    EnterpriseGuid: string
    EnterpriseName: string
    EnterpriseProperty: string
    EnterpriseEmployeeNumber: null | string
    PositionAmount: string
    RequirementOfEducationSpecialty: string
    RequirementOfEducationDegree: string
    EmergencyRrecruitmentFlag: boolean
    EnterpriseIndustry: string
    LogoUrl: string
    PositionId: number
    PositionGuid: string
    PositionName: string
    Pay: string
    IsReceiveGraduate: boolean
    PublishTime: string
    WorkProperty?: number
}

// 响应式数据
const oddJobsData = ref<Position[]>([])
const currentPage = ref(1)
const pageSize = ref(9)
const hovered = ref(false)
let autoTimer: number | null = null

// 计算属性 - 当前页数据
const currentPagePositions = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return oddJobsData.value.slice(start, end)
})

// 计算属性 - 总页数
const totalPages = computed(() => {
    return Math.ceil(oddJobsData.value.length / pageSize.value)
})

// 分页方法
const nextPageEvent = () => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++
    } else {
        currentPage.value = 1 // 循环到第一页
    }
}

const prevPageEvent = () => {
    if (currentPage.value > 1) {
        currentPage.value--
    } else {
        currentPage.value = totalPages.value // 循环到最后一页
    }
}

// 自动翻页控制
const startAutoPage = () => {
    if (hovered.value || autoTimer) return
    
    autoTimer = window.setInterval(() => {
        if (!hovered.value) {
            nextPageEvent()
        }
    }, 5000)
}

const stopAutoPage = () => {
    if (autoTimer) {
        clearInterval(autoTimer)
        autoTimer = null
    }
}

// 悬停控制
const handleMouseEnter = () => {
    hovered.value = true
    stopAutoPage()
}

const handleMouseLeave = () => {
    hovered.value = false
    startAutoPage()
}

// 初始化数据
onMounted(() => {
    // 直接使用零工兼职专门的数据源
    oddJobsData.value = mockData.oddJobsData as Position[]
    
    // 启动自动翻页
    setTimeout(() => {
        startAutoPage()
    }, 500)
    
    // 添加鼠标悬停事件监听器
    setTimeout(() => {
        const container = document.querySelector('.odd-jobs .jobs-container')
        if (container) {
            container.addEventListener('mouseenter', handleMouseEnter)
            container.addEventListener('mouseleave', handleMouseLeave)
        }
    }, 200)
})

// 组件卸载时清理
onBeforeUnmount(() => {
    stopAutoPage()
    
    // 移除事件监听器
    const container = document.querySelector('.odd-jobs .jobs-container')
    if (container) {
        container.removeEventListener('mouseenter', handleMouseEnter)
        container.removeEventListener('mouseleave', handleMouseLeave)
    }
})
</script>

<template>
    <div class="odd-jobs">
        <div class="container">
            <div class="section-title">
                <h2>零工/兼职</h2>
                <router-link :to="cityStore.getCityPagePath('oddJob')">查看更多>></router-link>
            </div>
            
            <div class="jobs-container">
                <transition name="fade" mode="out-in">
                    <div class="jobs-grid" :key="currentPage">
                        <div v-for="(job, index) in currentPagePositions" :key="index" class="job-cards">
                            <JobCard :job="job" />
                        </div>
                    </div>
                </transition>
                
                <!-- 分页组件 -->
                <Pagination 
                    :current-page="currentPage"
                    :total-pages="totalPages"
                    :show-pagination="totalPages > 1"
                    @prev="prevPageEvent"
                    @next="nextPageEvent"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.odd-jobs {
    padding: 24px 0;
    
    .jobs-container {
        height: 530px;
        position: relative;
        
        .jobs-grid {
            height: 469px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 143px);
            gap: 20px;
            align-content: start;
        }
    }
    
    /* Vue 3 过渡动画 */
    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s ease;
    }
    
    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }
}
</style>
