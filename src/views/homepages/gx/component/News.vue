<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useCityStore } from '@/stores/cityStore'
import mockData from '../mock'

const cityStore = useCityStore()

const activeTab = ref('government');


const allNews = ref([])

onMounted(() => {
    allNews.value = mockData.newsListData

// 新闻Tab鼠标悬停切换功能
        // 使用setTimeout确保Element UI已经完全渲染
        setTimeout(function() {
            // 为新闻区域的tab项添加鼠标悬停事件
            var newsTabItems = document.querySelectorAll('.news-area .el-tabs__item');
            
            newsTabItems.forEach(function(tabItem) {
                tabItem.addEventListener('mouseenter', function() {
                    var tabName = (this as HTMLElement).getAttribute('aria-controls');
                    if (tabName) {
                        // 根据aria-controls属性获取tab名称
                        if (tabName.includes('government')) {
                            activeTab.value = 'government';
                        } else if (tabName.includes('news')) {
                            activeTab.value = 'news';
                        } else if (tabName.includes('live')) {
                            activeTab.value = 'live';
                        }
                    }
                });
            });
        }, 100);

})

const newsTabChange = (tabName: string) => {
    activeTab.value = tabName;
}

</script>

<template>
    <!-- 新闻Tab区域 -->
    <div class="news-area bg-white blue-tabs-wrapper">
        <el-tabs v-model="activeTab" @tab-change="newsTabChange">
            <el-tab-pane name="government">
                <template #label>
                    <router-link :to="cityStore.getCityPagePath('sydw')" class="text-inherit text-decoration-none">
                        事业单位招聘
                    </router-link>
                </template>
                <div class="news-list">
                    <div v-for="(news, index) in (allNews[0] && allNews[0].articleList || []).slice(0, 3)" :key="index"
                        class="news-item">
                        <router-link :to="news.ArticleUrl">
                            <span class="news-title" :title="news.ArticleTitle">· {{ news.ArticleTitle }}</span>
                            <span class="news-date">阅{{ news.ArticleHits || 0 }}</span>
                        </router-link>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane name="news">
                <template #label>
                    <router-link :to="cityStore.getCityPagePath('news')" class="text-inherit text-decoration-none">
                        最新资讯
                    </router-link>
                </template>
                <div class="news-list">
                    <div v-for="(news, index) in (allNews[1] && allNews[1].articleList || []).slice(0, 3)" :key="index"
                        class="news-item">
                        <router-link :to="news.ArticleUrl">
                            <span class="news-title" :title="news.ArticleTitle">· {{ news.ArticleTitle }}</span>
                            <span class="news-date">阅{{ news.ArticleHits || 0 }}</span>
                        </router-link>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane name="live">
                <template #label>
                    <router-link :to="cityStore.getCityPagePath('news')" class="text-inherit text-decoration-none">
                        直播带岗
                    </router-link>
                </template>
                <div class="news-list">
                    <div v-for="(news, index) in (allNews[2] && allNews[2].articleList || []).slice(0, 3)" :key="index"
                        class="news-item">
                        <router-link :to="news.ArticleUrl">
                            <span class="news-title" :title="news.ArticleTitle">· {{ news.ArticleTitle }}</span>
                            <span class="news-date">阅{{ news.ArticleHits || 0 }}</span>
                        </router-link>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>

</template>

<style lang="scss" scoped>
.news-area {
    height: 142px;
    background-color: #fff;
    border-radius: 8px;

    ::v-deep(.el-tabs__header) {
    margin: 0px 12px 0px;
}

::v-deep(.el-tabs__item) {
    width: 192px;
    height: 41px;
    line-height: 41px;
    padding: 0;
}

.news-list {
    padding: 9px 16px 0;
}

.news-item {}

.news-item a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    cursor: pointer;
}

.news-title {
    color: #374151;
    text-decoration: none;
    flex: 1;
    margin-right: 16px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.news-title-icon {
    background: #ECF9FC;
    color: #365EAF;
    padding: 3px 4px;
    border-radius: 2px;
    font-size: 10px;
    margin-right: 4px;
}

.news-title-icon.zonghe {
    background: #ECF9FC;
    color: #365EAF;
}

.news-title-icon.xioayuan {
    background: #EDF8F5;
    color: #48DF9E;
}

.news-title-icon.wangluo {
    background: #EBF4F9;
    color: #17C1DC;
}

.news-title:hover {
    color: #1E58D2;
}

.news-date {
    color: #CBCBCB;
    font-size: 12px;
}


}
</style>
