<script setup lang="ts">
import { ref, computed } from "vue";
import mockData from '../mock'

const carousels = ref([])

const logoClickLog = (logoID: string) => {
    console.log(logoID);
}

onMounted(() => {
    carousels.value = mockData.centerBannerListData
})

</script>

<template>
    <!-- 轮播图 -->
    <div class="carousel-area">
        <template v-if="carousels.length > 1">
            <el-carousel height="180px" :interval="4000">
                <el-carousel-item v-for="(carousel, index) in carousels" :key="index">
                    <a :href="carousel.LinkUrl || '#'" target="_blank" @click="logoClickLog(carousel.LogoID)">
                        <img :src="carousel.LogoSrc" :alt="carousel.LogoName" class="carousel-image" />
                    </a>
                </el-carousel-item>
            </el-carousel>
        </template>
        <template v-else-if="carousels.length === 1">
            <a :href="carousels[0].LinkUrl || '#'" target="_blank" @click="logoClickLog(carousels[0].LogoID)">
                <img :src="carousels[0].LogoSrc" :alt="carousels[0].LogoName" class="carousel-image" />
            </a>
        </template>
    </div>

</template>

<style lang="scss" scoped>
.carousel-area {
    margin-bottom: 16px;

    .carousel-image {
        display: block;
        width: 100%;
        height: 180px;
        border-radius: 8px;
    }

    ::v-deep(.el-carousel) {
        z-index: 1;
    }
/* 轮播图样式 */
::v-deep(.el-carousel__indicators--horizontal) {
    bottom: 0px;
    right: 15px;
    left: auto;
    transform: none;
    text-align: right;
}

::v-deep(.el-carousel__indicator) {
    width: 8px;
    height: 8px;
}

::v-deep(.el-carousel__button) {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
}

::v-deep(.el-carousel__indicator.is-active .el-carousel__button) {
    background-color: #1E58D2;
}
}
</style>
