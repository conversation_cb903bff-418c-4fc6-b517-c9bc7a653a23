<script setup lang="ts">
import { ref, computed } from "vue";

// 行业分类数据结构
interface IndustryItem {
  id: string;
  name: string;
  icon: string;
  className: string;
}

// 行业分类数据
const industries = ref<IndustryItem[]>([
  {
    id: "15614",
    name: "IT互联网",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_1_it.png",
    className: "manage",
  },
  {
    id: "15623",
    name: "能源/环保/农业",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_2_hb.png",
    className: "sale",
  },
  {
    id: "15627",
    name: "电气/电子通信",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_3_dianqi.png",
    className: "computer",
  },
  {
    id: "15624",
    name: "产品",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_4_cp.png",
    className: "manage",
  },
  {
    id: "15621",
    name: "运营/客服",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_5_yy.png",
    className: "finance",
  },
  {
    id: "15632",
    name: "销售",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_6_xs.png",
    className: "production",
  },
  {
    id: "15617",
    name: "行政/人力/法务",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_7_xz.png",
    className: "building",
  },
  {
    id: "15613",
    name: "财务/税务",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_8_cw.png",
    className: "trade",
  },
  {
    id: "15626",
    name: "生产制造",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_9_zz.png",
    className: "chemical",
  },
  {
    id: "15620",
    name: "零售百货/生活服务",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_10_ls.png",
    className: "media",
  },
  {
    id: "15631",
    name: "餐饮",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_11_cy.png",
    className: "manage",
  },
  {
    id: "15606",
    name: "酒店/旅游",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_12_jd.png",
    className: "manage",
  },
  {
    id: "15629",
    name: "教育培训",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_13_jy.png",
    className: "manage",
  },
  {
    id: "15618",
    name: "设计",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_14_sj.png",
    className: "manage",
  },
  {
    id: "15610",
    name: "房地产/建筑",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_15_fdc.png",
    className: "manage",
  },
  {
    id: "15615",
    name: "直播/影视/媒体",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_16_zb.png",
    className: "manage",
  },
  {
    id: "15628",
    name: "市场/公关/广告",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_17_sc.png",
    className: "manage",
  },
  {
    id: "15611",
    name: "物流/仓储/运输",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_18_ys.png",
    className: "manage",
  },
  {
    id: "15630",
    name: "采购/贸易",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_19_cg.png",
    className: "manage",
  },
  {
    id: "15625",
    name: "汽车",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_20_qc.png",
    className: "manage",
  },
  {
    id: "15607",
    name: "医疗健康",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_21_yl.png",
    className: "manage",
  },
  {
    id: "15609",
    name: "金融",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_22_jr.png",
    className: "manage",
  },
  {
    id: "15616",
    name: "咨询/翻译/法律",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_23_zx.png",
    className: "manage",
  },
  {
    id: "15608",
    name: "高级管理",
    icon: "//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_24_gj.png",
    className: "manage",
  },
]);

// 分页相关数据
const currentPage = ref(1);
const pageSize = ref(9);

// 计算属性：当前页的数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return industries.value.slice(start, end);
});

// 总页数
const totalPages = computed(() => Math.ceil(industries.value.length / pageSize.value));



// 上一页（循环）
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  } else {
    // 回到最后一页
    currentPage.value = totalPages.value;
  }
};

// 下一页（循环）
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  } else {
    // 跳回第一页
    currentPage.value = 1;
  }
};

// 点击行业项的事件处理
const handleIndustryClick = (industry: IndustryItem) => {
  console.log("点击行业:", industry);
  // 这里可以添加跳转逻辑或其他业务逻辑
};
</script>

<template>
  <div class="industry-container bg-white">
    <!-- 行业分类列表 -->
    <ul class="industry-list">
      <li
        v-for="industry in currentPageData"
        :key="industry.id"
        :class="['industry-item', industry.className]"
        :id="`posTypeLevel1Nav_${industry.id}`"
        @click="handleIndustryClick(industry)"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <img :src="industry.icon" :alt="industry.name" />
            <span class="pos-type-name">{{ industry.name }}</span>
          </div>
          <span class="iconfont icon-arrowRight"></span>
        </div>
      </li>
    </ul>

    <!-- 分页组件 -->
    <div class="pagination-controls">
        <div class="control">
            <span 
              class="page-control"
              @click="prevPage"
            >
              <img src="//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_left.png" />
            </span>
            <div class="pages">
              <span class="current-page">{{ currentPage }}</span> / {{ totalPages }}
            </div>
            <span 
              class="page-control"
              @click="nextPage"
            >
              <img src="//image.gxrc.com/gxrcsite/rcw/2025/pos_icon_right.png" />
            </span>
        </div>
    </div>


  </div>
</template>

<style lang="scss" scoped>
.industry-container {
  width: 100%;
  max-width: 100%;
  height: 340px;
  border-radius: 8px;
}

/* 行业分类列表 */
.industry-list {
  height: 306px;
  list-style: none;
  margin: 0;
  padding: 10px 0 0 0;
  width: 100%;
  box-sizing: border-box;
}

/* 行业项样式 */
.industry-item {
  cursor: pointer;
  transition: background-color 0.3s ease;
  height: 32px;
  line-height: 32px;
  padding: 0 5px;
  margin: 0 5px;
  color: #707070;
  font-size: 14px;
}

.industry-item:last-child {
  border-bottom: none;
}

.industry-item:hover {
  background-color: #f5f7fa;
}

.industry-item img {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  flex-shrink: 0;
}

/* .pos-type-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
  font-weight: normal;
} */

.iconfont.icon-arrowRight {
  font-size: 12px;
  color:#DADADA;
  transition: color 0.3s ease;
}

.industry-item:hover .iconfont.icon-arrowRight {
  color: #409eff;
}

/* 分页器样式 */
.pagination-controls {
    width: 100%;
    display: flex;
    justify-content: center;
    pointer-events: none;
    z-index: 10;
    border-top: 1px dashed #E9DBDB;
    padding-top: 5px;

.page-control {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 10px;
    cursor: pointer;
    pointer-events: auto;
    background-color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.pages {
  font-weight: 400;
  font-size: 12px;
  color: #8E8E8E;
  .current-page {
    color: #1E58D2;
}
}

.control {
    display: flex;
    align-items: center;
    gap: 8px;
}
}
</style>
