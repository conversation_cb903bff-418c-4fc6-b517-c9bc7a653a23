<script setup lang="ts">
import { computed } from 'vue'
import { useCityStore } from '@/stores/cityStore'
import { useRouter } from 'vue-router'

const cityStore = useCityStore()
const router = useRouter()

// 城市数据 - 直接使用 cityStore 中的配置
const cities = computed(() => {
    // 获取所有激活的城市，保持 CITIES_CONFIG 的原始顺序
    return cityStore.availableCities.map(cityInfo => ({
        code: cityInfo.code,
        name: cityInfo.code === 'gx' ? '全区' : cityInfo.name
    }))
})

// 城市切换处理
const handleCityClick = (cityCode: string) => {
    if (cityCode === 'gx') {
        // 全区跳转到广西首页
        router.push('/')
    } else {
        // 其他城市跳转到对应城市首页
        router.push(`/${cityCode}`)
    }
}

// 判断城市是否激活
const isCityActive = (cityCode: string) => {
    return cityStore.currentCity === cityCode
}
</script>

<template>
    <div class="city-station">
        <div class="city-header">
            <img src="https://image.gxrc.com/thirdParty/gxjy/pc/home/<USER>" alt="">
        </div>
        <div class="city-grid">
            <div v-for="city in cities" :key="city.code" class="city-item"
                :class="{ 'active': isCityActive(city.code) }" @click="handleCityClick(city.code)">
                {{ city.name }}
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.city-station {
    width: 100%;
    height: 132px;
    background: url(https://image.gxrc.com/thirdParty/gxjy/pc/home/<USER>
    background-size: 100% 100%;
    border-radius: 8px;
    padding: 14px 16px 0 16px;
    box-sizing: border-box;
    margin-bottom: 16px;
}

.city-header {
    margin-bottom: 5px;
}

.city-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 4px;
}

.city-item {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
        transform: translateY(-1px);
    }

    &.active {
        background: #00CDE2;
        border-radius: 20px;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
    }
}
</style>
