<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useCityStore } from '@/stores/cityStore';
import { useRouter, useRoute } from 'vue-router';
import { NewsApi, BussDistrict, ApplicationPlatform } from '@/api-services';
import { getPcAPI } from '@/utils/axios-utils';
import type { SydwDto } from '@/api-services';

const router = useRouter();
const route = useRoute();

// API 实例
const newsApi = getPcAPI(NewsApi);

// 城市store
const cityStore = useCityStore();

// 当前城市信息
const currentCityInfo = computed(() => cityStore.cityInfo);
const currentCityName = computed(() => currentCityInfo.value.name);

// 文章详情数据
const articleDetail = ref<SydwDto | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);

// 获取当前城市的地市ID（直接使用 CITIES_CONFIG 中的 id）
const getCurrentDistrictId = (): BussDistrict => {
  return cityStore.cityInfo.id as BussDistrict;
};

// 格式化时间
const formatDateTime = (date: Date | string | null | undefined) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
};

// 获取文章详情
const fetchArticleDetail = async () => {
  const guid = route.query.guid as string;
  if (!guid) {
    error.value = '缺少文章ID参数';
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    const response = await newsApi.apiNewsSydwGet(
      guid,
      getCurrentDistrictId(),
      ApplicationPlatform.PC
    );

    if (response.data.code === 1 && response.data.data) {
      articleDetail.value = response.data.data;
    } else {
      error.value = '文章不存在或已被删除';
    }
  } catch (err) {
    console.error('获取文章详情失败:', err);
    error.value = '获取文章详情失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 返回列表页
const goBack = () => {
  router.push(cityStore.getCityPagePath('sydw'));
};

// 打印页面
const printPage = () => {
  window.print();
};

onMounted(() => {
  fetchArticleDetail();
});
</script>

<template>
  <div class="sydw-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{ currentCityName }}事业单位招聘专栏</h1>

        <!-- 面包屑导航 -->
        <div class="breadcrumb">
          <router-link :to="cityStore.getCityHomePath" class="breadcrumb-link">
            {{ currentCityName }}首页
          </router-link>
          <span class="breadcrumb-separator"> > </span>
          <router-link :to="cityStore.getCityPagePath('sydw')" class="breadcrumb-link">
            事业单位招聘专栏
          </router-link>
          <span class="breadcrumb-separator"> > </span>
          <span class="breadcrumb-current">文章详情</span>
        </div>
      </div>
    </div>

    <!-- 文章内容区域 -->
    <div class="article-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-loading text="加载中..." />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <el-empty :description="error">
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </el-empty>
      </div>

      <!-- 文章详情 -->
      <div v-else-if="articleDetail" class="article-detail">
        <!-- 文章头部信息 -->
        <div class="article-header">
          <h1 class="article-title">{{ articleDetail.articleTitle }}</h1>

          <div class="article-meta">
            <div class="meta-info">
              <span class="meta-item">
                <i class="iconfont icon-time"></i>
                发布时间：{{ formatDateTime(articleDetail.articlePublishTime) }}
              </span>
              <span class="meta-item" v-if="articleDetail.articleFrom">
                <i class="iconfont icon-source"></i>
                来源：{{ articleDetail.articleFrom }}
              </span>
              <span class="meta-item" v-if="articleDetail.articleAuthor">
                <i class="iconfont icon-author"></i>
                作者：{{ articleDetail.articleAuthor }}
              </span>
              <span class="meta-item">
                <i class="iconfont icon-eye"></i>
                阅读：{{ articleDetail.articleHits || 0 }}
              </span>
            </div>

            <div class="article-actions">
              <el-button type="text" @click="printPage">
                <i class="iconfont icon-print"></i>
                打印
              </el-button>
              <el-button type="text" @click="goBack">
                <i class="iconfont icon-back"></i>
                返回
              </el-button>
            </div>
          </div>
        </div>

        <!-- 文章内容 -->
        <div class="article-content">
          <div class="content-body" v-html="articleDetail.articleContent"></div>
        </div>

        <!-- 文章底部 -->
        <div class="article-footer">
          <div class="disclaimer">
            <p><strong>免责声明：</strong></p>
            <p>本网站所发布的事业单位招聘信息均来源于各级政府部门、事业单位官方渠道。请考生及时关注官方最新公告，以官方发布的信息为准。如有疑问，请联系相关招聘单位核实。</p>
          </div>

          <div class="back-to-list">
            <el-button type="primary" size="large" @click="goBack">
              返回事业单位招聘专栏
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sydw-detail-page {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面头部 */
.page-header {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.breadcrumb {
  font-size: 14px;
  color: #666;

  .breadcrumb-link {
    color: #1e58d2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .breadcrumb-separator {
    margin: 0 8px;
    color: #999;
  }

  .breadcrumb-current {
    color: #666;
  }
}

/* 文章容器 */
.article-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #fff;
  border-radius: 8px;
}

/* 文章详情 */
.article-detail {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 文章头部 */
.article-header {
  padding: 30px 40px;
  border-bottom: 1px solid #f0f0f0;
}

.article-title {
  line-height: 1.4;
  margin: 0 0 20px 0;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  text-align: center;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 15px;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .meta-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;

    .iconfont {
      margin-right: 5px;
      color: #999;
    }
  }
}

.article-actions {
  display: flex;
  gap: 10px;

  .el-button {
    padding: 8px 16px;

    .iconfont {
      margin-right: 5px;
    }
  }
}

/* 文章内容 */
.article-content {
  padding: 40px;
}

.content-body {
  font-size: 16px;
  line-height: 1.8;
  color: #333;

  // 内容样式重置和优化
  :deep(p) {
    margin: 0 0 16px 0;
    text-align: justify;
  }

  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    margin: 24px 0 16px 0;
    font-weight: 600;
    line-height: 1.4;
  }

  :deep(h1) {
    font-size: 24px;
  }

  :deep(h2) {
    font-size: 22px;
  }

  :deep(h3) {
    font-size: 20px;
  }

  :deep(h4) {
    font-size: 18px;
  }

  :deep(h5) {
    font-size: 16px;
  }

  :deep(h6) {
    font-size: 14px;
  }

  :deep(ul),
  :deep(ol) {
    margin: 16px 0;
    padding-left: 24px;

    li {
      margin: 8px 0;
    }
  }

  :deep(blockquote) {
    margin: 16px 0;
    padding: 16px 20px;
    background: #f8f9fa;
    border-left: 4px solid #1e58d2;
    font-style: italic;
  }

  :deep(table) {
    width: 100%;
    margin: 16px 0;
    border-collapse: collapse;

    th,
    td {
      padding: 12px;
      border: 1px solid #e5e5e5;
      text-align: left;
    }

    th {
      background: #f8f9fa;
      font-weight: 600;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    margin: 16px 0;
    border-radius: 4px;
  }

  :deep(code) {
    padding: 2px 6px;
    background: #f1f2f3;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
  }

  :deep(pre) {
    margin: 16px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    overflow-x: auto;

    code {
      background: none;
      padding: 0;
    }
  }
}

/* 文章底部 */
.article-footer {
  padding: 30px 40px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.disclaimer {
  margin-bottom: 30px;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;

  p {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #856404;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      font-weight: 600;
    }
  }
}

.back-to-list {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {

  .header-content,
  .article-container {
    padding: 0 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .article-header {
    padding: 20px 20px;
  }

  .article-content {
    padding: 20px;
  }

  .article-footer {
    padding: 20px;
  }

  .article-title {
    font-size: 20px;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .meta-info {
    flex-direction: column;
    gap: 10px;
  }

  .content-body {
    font-size: 15px;
  }
}

/* 打印样式 */
@media print {

  .page-header,
  .article-actions,
  .article-footer {
    display: none !important;
  }

  .article-detail {
    box-shadow: none;
    border: none;
  }

  .article-header {
    border-bottom: 2px solid #333;
  }

  .article-title {
    color: #000;
  }

  .content-body {
    color: #000;
  }
}
</style>
