<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useCityStore } from '@/stores/cityStore';
import { useRouter } from 'vue-router';
import { NewsApi, SydwStatus, BussDistrict, ApplicationPlatform } from '@/api-services';
import { getPcAPI } from '@/utils/axios-utils';
import type { SydwListItemDto } from '@/api-services';
import Pagination from '@/components/table/Pagination.vue';

const router = useRouter();

// API 实例
const newsApi = getPcAPI(NewsApi);

// 城市store
const cityStore = useCityStore();

// 当前城市信息
const currentCityInfo = computed(() => cityStore.cityInfo);
const currentCityName = computed(() => currentCityInfo.value.name);

// 搜索相关
const searchKeyword = ref('');

// 根据当前城市确定选中的地区
const selectedRegion = computed(() => {
  if (cityStore.currentCity === 'gx') {
    return { name: '不限', id: 0 as BussDistrict };
  } else {
    return { name: currentCityName.value, id: cityStore.cityInfo.id as BussDistrict };
  }
});

// 标签页相关
const activeTab = ref('全部');
const tabs = ['全部', '报名', '名单公示', '事项通知'];

// 分页组件引用
const paginationRef = ref(null);

// 分页组件配置
const post = ref({
  page: 1,
  pageSize: 20
});

// 异步搜索参数
const asyncPost = ref({
  keyword: '',
  state: undefined as SydwStatus | undefined,
  districtId: selectedRegion.value.id
});

// 获取标签页对应的状态
const getTabStatus = (tab: string): SydwStatus | undefined => {
  const statusMap: Record<string, SydwStatus> = {
    '报名': SydwStatus.NUMBER_1,        // 报名中 = 1
    '名单公示': SydwStatus.NUMBER_2,    // 名单公示 = 2
    '事项通知': SydwStatus.NUMBER_3     // 事项通知 = 3
  };
  return statusMap[tab];
};

// API 包装函数，适配分页组件格式
const sydwApiFunction = async (data: any) => {
  const response = await newsApi.apiNewsSydwListGet(
    data.keyword || undefined,
    data.state || undefined,
    data.districtId || selectedRegion.value.id,
    ApplicationPlatform.PC,
    data.page || 1,
    data.pageSize || 20
  );
  
  // 返回符合分页组件期望的格式
  return {
    data: {
      code: 1,
      data: {
        items: response.data.data?.items || [],
        totalCount: response.data.data?.totalCount || 0
      }
    }
  };
};

// 地区列表 - 读取 cityStore 中的城市配置
const regions = computed(() => {
  // 如果是广西，显示所有可用城市
  if (cityStore.currentCity === 'gx') {
    const allCities = [{ name: '不限', id: 0 as BussDistrict }];
    // 获取所有激活的城市，排除广西本身
    cityStore.availableCities.forEach(cityInfo => {
      if (cityInfo.code !== 'gx') {
        allCities.push({ name: cityInfo.name, id: cityInfo.id as BussDistrict });
      }
    });
    return allCities;
  }
  
  // 其他城市只显示当前城市和返回全区
  return [
    { name: '不限', id: 0 as BussDistrict },
    { name: currentCityName.value, id: cityStore.cityInfo.id as BussDistrict }
  ];
});

// 搜索处理
const handleSearch = () => {
  asyncPost.value.keyword = searchKeyword.value.trim();
  if (paginationRef.value) {
    (paginationRef.value as any).asyncOptionReset();
  }
};

// 地区选择处理
const handleRegionSelect = (region: { name: string, id: BussDistrict }) => {
  // 如果选择的是"不限"，跳转到广西页面（全区）
  if (region.name === '不限') {
    router.push('/sydw');
    return;
  }
  
  // 根据选择的地区名称找到对应的城市代码
  const targetCity = cityStore.availableCities.find(city => city.name === region.name);
  if (targetCity) {
    // 跳转到对应城市的事业单位页面
    router.push(`/${targetCity.code}/sydw`);
  }
};

// 标签页切换处理
const handleTabChange = (tab: string) => {
  activeTab.value = tab;
  asyncPost.value.state = getTabStatus(tab);
  post.value.page = 1;
  if (paginationRef.value) {
    (paginationRef.value as any).asyncOptionReset();
  }
};

// 公告点击处理
const handleAnnouncementClick = (announcement: SydwListItemDto) => {
  // 跳转到事业单位详情页面
  if (announcement.articleGuid) {
    router.push({
      path: cityStore.getCityPagePath('sydw-detail'),
      query: { guid: announcement.articleGuid }
    });
  }
};

// 根据 API 返回的状态获取显示文本和样式类
const getStatusInfo = (status: string | null | undefined) => {
  const statusMap: Record<string, { text: string, class: string }> = {
    // 直接映射中文状态
    '报名中': { text: '报名中', class: 'registration' },
    '名单公示': { text: '名单公示', class: 'publicity' },
    '事项通知': { text: '事项通知', class: 'notice' },
    '已过期': { text: '已过期', class: 'expired' },
    '调整公告': { text: '调整公告', class: 'adjustment' },
    // 兼容数字格式（如果有的话）
    '1': { text: '报名中', class: 'registration' },
    '2': { text: '名单公示', class: 'publicity' },
    '3': { text: '事项通知', class: 'notice' },
    '4': { text: '已过期', class: 'expired' },
    '5': { text: '调整公告', class: 'adjustment' }
  };
  
  return statusMap[status || ''] || { text: status || '未知', class: 'unknown' };
};

// 格式化日期
const formatDate = (date: Date | string | undefined) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

// 监听城市变化
watch(() => cityStore.currentCity, (newCityCode) => {
  asyncPost.value.districtId = selectedRegion.value.id;
  post.value.page = 1;
  if (paginationRef.value) {
    (paginationRef.value as any).asyncOptionReset();
  }
  console.log(`切换到${newCityCode}，重新获取事业单位数据`);
});

onMounted(() => {
  console.log('事业单位招聘页面加载完成');
  // 初始化异步参数
  asyncPost.value.districtId = selectedRegion.value.id;
});
</script>

<template>
    <div class="sydw-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <p class="sub-title">任何组织和个人不得转载,违者必究</p>

    <!-- 搜索 -->
        <div class="search-input-wrapper">
          <input 
            v-model="searchKeyword"
            type="text" 
            placeholder="请输入文章标题"
            class="search-input"
            @keyup.enter="handleSearch"
          />
          <button class="search-btn" @click="handleSearch">搜索文章</button>
        </div>

        <!-- 地区筛选 -->
        <div class="region-filter">
          <span class="region-label"><i class="iconfont icon-position1"></i>地区：</span>
          <div class="region-buttons">
            <button
              v-for="region in regions"
              :key="region.name"
              :class="['region-btn', { 'active': selectedRegion.name === region.name }]"
              @click="handleRegionSelect(region)"
            >
              {{ region.name }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="page-body-wrapper">
    <!-- 标签页 -->
    <div class="tabs-section">
      <div class="tabs-container">
        <button
          v-for="tab in tabs"
          :key="tab"
          :class="['tab-btn', { 'active': activeTab === tab }]"
          @click="handleTabChange(tab)"
        >
          {{ tab }}
        </button>
      </div>
    </div>

    <!-- 公告列表 -->
    <div class="announcements-section">
      <div class="announcements-container">
        <Pagination 
          ref="paginationRef"
          :allFn="false"
          :ApiFunction="sydwApiFunction"
          :option="post"
          :asyncOption="asyncPost"
          :showAffix="false"
          :fixedPageSize="20"
          layout= 'prev, pager, next, jumper'
        >
          <template #default="{ tableData, loading, empty }">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
              <el-loading text="加载中..." />
            </div>
            
            <!-- 公告列表 -->
            <div class="announcement-list" v-else-if="!empty">
              <div
                v-for="announcement in (tableData as SydwListItemDto[])"
                :key="announcement.articleID"
                class="announcement-item"
                @click="handleAnnouncementClick(announcement)"
              >
                <div class="status-tag" :class="getStatusInfo(announcement.articleState).class">
                  {{ getStatusInfo(announcement.articleState).text }}
                </div>
                <div class="announcement-content">
                  <h3 class="announcement-title">{{ announcement.articleTitle }}</h3>
                  <div class="announcement-meta">
                    <span class="announcement-date">{{ formatDate(announcement.articlePublishTime) }}</span>
                    <span class="announcement-views">阅[{{ announcement.articleHits || 0 }}]</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-else class="empty-state">
              <el-empty description="暂无相关公告" />
            </div>
          </template>
        </Pagination>
      </div>
    </div>
    </div>
    </div>
</template>

<style lang="scss" scoped>
.sydw-page {
  width: 1200px;
  min-height: 100vh;
  padding: 24px 0;
  margin: 0 auto;
}


/* 页面头部 */
.page-header {
  width: 100%;
  background:url(https://image.gxrc.com/thirdParty/gxjy/pc/sydw/banner.png) no-repeat center top;
  height: 320px;
  margin-bottom: 10px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  color: white;
}

.sub-title {
    font-weight: normal;
font-size: 16px;
color: #0865DA;
padding: 140px 0 15px;
}

.search-input-wrapper {
  width: 750px;
  height: 60px;
  display: flex;
  gap: 0px;
  margin:0 auto 20px;
  background: #0A82F9;
  padding: 2px;
  border-radius:40px;
}

.search-input {
  flex: 1;
  height: 56px;
  line-height: 56px;
  outline: none;
  transition: border-color 0.3s ease;
  background: #fff;
  border-radius:40px 0 0 40px;
  padding: 0 20px;

  &:focus {
    border-color: #1e58d2;
  }
  
  &::placeholder {
    color: #999;
  }
}

.search-btn {
  width:134px;
  height: 56px;
  line-height: 56px;
  border: none;
  border-radius: 0;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  background: #0A82F9;
  border-radius: 0 40px 40px 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: #0A82F5;
  }
}


.region-filter {
  display: flex;
  align-items: center;
  gap: 5px;
  width: 1153px;
height: 40px;
background:rgba(255, 255, 255, 0.68);
border-radius: 8px;
margin: 0 auto;
}

.region-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  padding-left: 15px;

  .iconfont{
    color: #87C3EE;
    margin-right: 5px;
  }
}

.region-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.region-btn {
  padding: 3px 15px;
  border-radius: 20px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
  background: white;
  }
  
  &.active {
    background: #2878FF;
    color: white;
  }
}
.page-body-wrapper{
  background: white;
}
/* 标签页 */
.tabs-section {
  height: 63px;
  line-height: 63px;
  padding: 0 200px;
    box-shadow: 0px 5px 8px #f5f5f5;
}

.tabs-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
}

.tab-btn {
  flex:1;
  background: none;
  border: none;
  font-size: 16px;
  color: #757575;
  cursor: pointer;
  position: relative;
  transition: color 0.3s ease;
  
  &:hover {
    color: #1e58d2;
  }
  
  &.active {
    color: #2c71e1;
    font-size: 18px;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: #1e58d2;
      border-radius: 2px 2px 0 0;
    }
  }
}

/* 公告列表 */
.announcements-section {
  padding: 20px 0;
}

.announcements-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.announcement-list{
  padding:0 0 20px;
}

.announcement-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
  

}

.status-tag {
  width: 75px;
    height: 22px;
    line-height: 22px;
    border: 1px solid #339966;
    border-radius: 22px;
    text-align: center;
  background: #fff;
  border: 1px solid #339966;
  margin-right:20px;
  
  &.registration {
    border-color: #339966;
    color: #339966;
  }
  
  &.publicity {
    border-color: #ff6666;
    color: #ff6666;
  }
  
  &.notice {
    border-color: #00cccc;
    color: #00cccc;
  }
  
  &.expired{
    border-color: #999;
    color: #999;
  }
  
  &.adjustment{
    border-color:#ff9933;
    color:#ff9933;
  }
  
  &.unknown{
    border-color: #ccc;
    color: #ccc;
  }
}

.announcement-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-title {
  font-size: 16px;
  color: #535353;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
  max-width: 70%;
  
  &:hover {
    color: #1e58d2;
  }
}

.announcement-meta {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  font-size: 14px;
  color: #999;
}

/* 公告时间和浏览量样式使用父容器的flex布局 */

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding: 20px 0;
}

/* 分页组件自定义样式 */
:deep(.el-pagination) {
  .el-pager li {
    background: white;
    border: 1px solid #dcdfe6;
    margin: 0 2px;
    
    &.active {
      background: #1e58d2;
      border-color: #1e58d2;
      color: white;
    }
    
    &:hover:not(.active) {
      color: #1e58d2;
    }
  }
  
  .btn-prev,
  .btn-next {
    background: white;
    border: 1px solid #dcdfe6;
    
    &:hover {
      color: #1e58d2;
    }
    
    &:disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  }
}
</style>