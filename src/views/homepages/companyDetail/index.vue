<template>
  <div id="companyDetail" class="company-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-content">
        <i class="el-icon-loading" style="font-size: 24px; margin-right: 8px;"></i>
        正在加载企业详情...
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 企业基本信息 -->
      <div class="header-section">
        <div class="header-container w-1200px mx-auto header-clearfix">
          <div class="header-logo header-fl">
            <span class="header-logo-wrapper">
              <img :src="companyData.logo" :alt="companyData.name" class="header-logo-image">
            </span>
          </div>
          <div class="header-info header-fl">
            <h1 class="header-title" :title="companyData.name">
              {{ companyData.name }}
            </h1>
            <p class="header-subtitle">
              {{ companyData.type }}
              <span class="header-separator">|</span>{{ companyData.scale }}
              <span class="header-separator">|</span>{{ companyData.industry }}
            </p>
          </div>

          <div class="header-media header-fr">
            <!-- 企业视频/图片展示区域 -->
            <div v-if="companyData.video" class="header-video-container">
              <video :src="companyData.video" :poster="companyData.videoPoster" controls
                class="header-video-player"></video>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-section">
        <div class="main-container w-1200px mx-auto main-clearfix">
          <!-- 企业介绍 -->
          <div class="intro-section intro-clearfix">
            <div class="intro-content intro-clearfix">
              <pre v-show="!isDescriptionExpanded || companyData.description.length <= 300"
                class="intro-description">{{ truncatedDescription }}</pre>
              <pre v-show="isDescriptionExpanded && companyData.description.length > 300"
                class="intro-description">{{ companyData.description }}</pre>
              <div v-if="companyData.description.length > 300" class="intro-expand-btn" @click="toggleDescription">
                {{ isDescriptionExpanded ? '收起' : '展开全部' }}
              </div>
            </div>

            <!-- 企业图片展示 -->
            <div v-if="companyData.photos && companyData.photos.length" class="gallery-section">
              <div class="gallery-carousel">
                <div class="gallery-main">
                  <img :src="companyData.photos[currentPhotoIndex]" :alt="companyData.name" class="gallery-image"
                    @click="openPhotoModal">
                </div>
                <div v-if="companyData.photos.length > 1" class="gallery-controls">
                  <span class="gallery-counter">{{ currentPhotoIndex + 1 }}/{{ companyData.photos.length }}</span>
                  <div class="gallery-navigation">
                    <div class="gallery-prev" @click="prevPhoto">
                      <i class="i-carbon-chevron-left"></i>
                    </div>
                    <div class="gallery-next" @click="nextPhoto">
                      <i class="i-carbon-chevron-right"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="content-wrapper content-clearfix">
            <!-- 左侧内容 -->
            <div class="content-left content-fl">
              <!-- 公司地址 -->
              <div v-if="companyData.address" class="location-section">
                <div class="section-title">公司地址</div>
                <div class="location-content">
                  <p>
                    <i class="i-carbon-location location-icon"></i>
                    {{ companyData.address }}
                  </p>
                  <!-- 地图组件 -->
                  <div class="location-map">
                    <MapComponent :title="companyData.name" :address="companyData.address"
                      :longitude="companyData.longitude" :latitude="companyData.latitude" width="758px" height="160px"
                      :show-coordinates="true" />
                  </div>
                </div>
              </div>

              <!-- 在招职位 -->
              <div class="positions-section">
                <div class="section-title">
                  在招职位（{{ totalPositions }}）

                  <!-- 筛选功能 -->
                  <div class="filter-container">
                    <div class="filter-widget">
                      <div class="filter-trigger" @click="toggleFilter">
                        <span class="filter-text">按职位类型筛选</span>
                        <i class="i-carbon-filter filter-icon"></i>
                      </div>
                      <div v-show="showFilter" class="filter-dropdown">
                        <ul class="filter-clearfix">
                          <li>
                            <a href="javascript:;" @click="filterByType('')" class="filter-link">
                              <h3>全部</h3>
                            </a>
                          </li>
                          <li v-for="type in positionTypes" :key="type.id">
                            <a href="javascript:;" @click="filterByType(type.id)" class="filter-link">
                              <h3 :title="type.name">{{ type.name }}</h3>
                              <span>{{ type.count }}</span>
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 分公司导航 -->
                <div class="branches-container">
                  <ul class="branches-nav branches-clearfix">
                    <li class="branches-title">总公司</li>
                    <li v-for="branch in branches" :key="branch.id" class="branches-item"
                      :class="{ 'branches-selected': selectedBranch === branch.id }" @click="selectBranch(branch.id)">
                      {{ branch.name }}
                      <div class="branches-indicator"></div>
                    </li>
                  </ul>
                </div>

                <!-- 职位列表 -->
                <div class="positions-content">
                  <div v-if="filteredPositions.length > 0" class="positions-list">
                    <table class="positions-table">
                      <tbody>
                        <template v-for="dept in filteredPositions" :key="dept.name">
                          <thead>
                            <tr>
                              <td colspan="2">
                                <div class="department-header">{{ dept.name }}</div>
                              </td>
                            </tr>
                          </thead>
                          <template v-for="position in dept.positions" :key="position.id">
                            <tr class="position-main">
                              <td colspan="2">
                                <div class="position-title">
                                  <router-link :to="cityStore.getCityPagePath(`jobDetail/${position.id}`)" target="_blank" :title="position.title"
                                    class="position-link">
                                    {{ position.title }}
                                  </router-link>
                                </div>
                                <div class="position-status">
                                  <!-- 职位状态标签 -->
                                </div>
                                <div class="position-salary">
                                  {{ position.salary }}
                                </div>
                              </td>
                            </tr>
                            <tr class="position-details">
                              <td class="position-meta">
                                <span>{{ position.location }}</span>
                                <span v-if="position.experience">{{ position.experience }}</span>
                                <span v-if="position.education">{{ position.education }}</span>
                                <span>招{{ position.recruitNum }}人</span>
                                <span v-for="tag in position.tags" :key="tag" class="position-tag">
                                  {{ tag }}
                                </span>
                              </td>
                              <td class="position-date">{{ position.updateTime }}</td>
                            </tr>
                          </template>
                        </template>
                      </tbody>
                    </table>
                  </div>
                  <div v-else class="no-positions">
                    <p>暂无招聘职位</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧栏 -->
            <div class="sidebar-section sidebar-fr">
              <!-- 联系信息 -->
              <div class="contact-section">
                <div class="section-title">其他信息</div>
                <div class="contact-content">
                  <p class="contact-item">
                    <i class="iconfont icon-icon_people"></i>
                    <label class="contact-label">{{ companyData.contact.name }}</label>
                  </p>
                  <p class="contact-item">
                    <i class="iconfont icon-icon_phone"></i>
                    <label class="contact-label">
                      {{ showPhone ? companyData.contact.phone : hidePhone(companyData.contact.phone) }}
                    </label>
                    <el-button v-if="!showPhone" class="contact-btn" @click="showContactInfo">
                      查看
                    </el-button>
                  </p>
                  <p v-if="companyData.contact.email" class="contact-item">
                    <i class="iconfont icon-icon_email"></i>
                    <label class="contact-label">
                      {{ showEmail ? companyData.contact.email : hideEmail(companyData.contact.email) }}
                    </label>
                    <el-button v-if="!showEmail" class="contact-btn" @click="showContactInfo">
                      查看
                    </el-button>
                  </p>
                </div>
              </div>

              <!-- 工商信息 -->
              <div class="business-section">
                <div class="section-title">工商信息</div>
                <div class="business-content">
                  <p class="business-item">
                    <i class="iconfont icon-icon_company1"></i>
                    <label class="business-label">{{ companyData.businessInfo.legalPerson }}</label>
                  </p>
                  <p class="business-item">
                    <i class="iconfont icon-icon_people"></i>
                    <label class="business-label">{{ companyData.businessInfo.registerDate }}</label>
                  </p>
                  <p class="business-item">
                    <i class="iconfont icon-icon_time1"></i>
                    <label class="business-label">{{ companyData.businessInfo.establishDate }}</label>
                  </p>
                  <div class="business-actions">
                    <el-button class="business-btn" @click="showBusinessInfo">
                      查看全部
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 企业动态 -->
              <div class="dynamics-section">
                <div class="section-title">
                  企业动态
                  <router-link :to="cityStore.getCityPagePath(`socialDynamics/${companyData.id}`)" class="section-more-link">
                    更多&gt;
                  </router-link>
                </div>
                <div class="dynamics-content">
                  <ul v-if="companyData.dynamics && companyData.dynamics.length">
                    <li v-for="dynamic in companyData.dynamics" :key="dynamic.id" class="dynamics-item dynamics-clearfix"
                      @click="viewDynamicDetail(dynamic.id)">
                      <div class="dynamics-position">
                        <img class="dynamics-position-bg" :src="dynamic.image" >
                        <h3>{{ dynamic.positionTitle }}</h3>
                        <p>{{ dynamic.salary }}</p>
                      </div>
                      <div class="dynamics-info">
                        <h3>{{ dynamic.title }}</h3>
                        <p>
                          <span class="dynamics-like">
                            <i class="i-carbon-thumbs-up"></i>{{ dynamic.likeCount }}
                          </span>
                          <span class="dynamics-date">{{ dynamic.publishTime }}</span>
                        </p>
                      </div>
                    </li>
                  </ul>

                  <div v-else class="dynamics-empty">
                    <p>暂无动态</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工商信息详情弹窗 -->
    <el-dialog v-model="showBusinessModal" title="工商信息" width="800px" class="business-modal">
      <table class="business-table">
        <tbody>
          <tr>
            <td class="business-cell-w1">
              <span class="business-field-label">企业法人：</span>
              <span>{{ companyData.businessInfo.legalPerson }}</span>
            </td>
            <td class="business-cell-w2">
              <span class="business-field-label">注册资本：</span>
              <span>{{ companyData.businessInfo.registeredCapital }}</span>
            </td>
            <td class="business-cell-w3">
              <span class="business-field-label">成立日期：</span>
              <span>{{ companyData.businessInfo.establishDate }}</span>
            </td>
          </tr>
          <tr>
            <td colspan="3" class="business-field-label">
              <span class="business-field-label">经营状态：</span>
              <span>{{ companyData.businessInfo.status }}</span>
            </td>
          </tr>
          <tr>
            <td colspan="3" class="business-field-label">
              <span class="business-field-label">注册地址：</span>
              <span>{{ companyData.businessInfo.registeredAddress }}</span>
            </td>
          </tr>
          <tr>
            <td colspan="3" class="business-field-label">
              <span class="business-field-label">统一信用代码：</span>
              <span>{{ companyData.businessInfo.creditCode }}</span>
            </td>
          </tr>
          <tr>
            <td colspan="3" class="business-field-label">
              <span class="business-field-label">经营范围：</span>
              <span>{{ companyData.businessInfo.businessScope }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </el-dialog>

    <!-- 图片查看弹窗 -->
    <el-dialog v-model="showPhotoModal" width="90%" :show-close="false" class="gallery-modal">
      <div class="gallery-viewer">
        <img :src="companyData.photos && companyData.photos[currentPhotoIndex]" :alt="companyData.name"
          class="gallery-fullscreen">
        <div class="gallery-modal-controls">
          <el-button circle @click="prevPhoto" :disabled="currentPhotoIndex === 0">
            <i class="i-carbon-chevron-left"></i>
          </el-button>
          <span class="gallery-modal-counter">
            {{ currentPhotoIndex + 1 }} / {{ companyData.photos?.length || 0 }}
          </span>
          <el-button circle @click="nextPhoto" :disabled="currentPhotoIndex === (companyData.photos?.length || 0) - 1">
            <i class="i-carbon-chevron-right"></i>
          </el-button>
        </div>
        <el-button circle class="gallery-modal-close" @click="showPhotoModal = false">
          <i class="i-carbon-close"></i>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import MapComponent from '@/components/common/MapComponent.vue'
  import { EnterpriseApi, BussDistrict, ApplicationPlatform, EnterpriseClickFrom } from '@/api-services'
  import { getPcAPI } from '@/utils/axios-utils'
  import { useCityStore } from '@/stores/cityStore'

  // 路由参数
  const route = useRoute()
  const companyId = route.params.id

  // 城市状态管理
  const cityStore = useCityStore()

  // API实例
  const enterpriseApi = getPcAPI(EnterpriseApi)

  // 响应式数据
  const searchKeyword = ref('')
  const isLoggedIn = ref(false)
  const showPhone = ref(false)
  const showEmail = ref(false)
  const showFilter = ref(false)
  const showBusinessModal = ref(false)
  const showPhotoModal = ref(false)
  const isDescriptionExpanded = ref(false)
  const currentPhotoIndex = ref(0)
  const selectedBranch = ref('')
  const selectedPositionType = ref('')
  const loading = ref(true)

  // 用户信息
  const userInfo = ref({
    name: '中国广西人才市场/广西人才网',
    avatar: '//image.gxrc.com/EnterpriseMaterial/Logo/53046_59a480cf-5534-4d62-972d-2d1d06d91deb.png'
  })

  // 企业数据 - 从API获取
  const companyData = ref({
    id: companyId,
    name: '',
    type: '',
    scale: '',
    industry: '',
    logo: '',
    address: '',
    longitude: 108.374104,
    latitude: 22.805206,
    description: '',
    photos: [''],
    video: '',
    videoPoster: '',
    contact: {
      name: '',
      phone: '',
      email: ''
    },
    businessInfo: {
      legalPerson: '',
      registeredCapital: '',
      establishDate: '',
      registerDate: '',
      status: '',
      registeredAddress: '',
      creditCode: '',
      businessScope: ''
    },
    dynamics: [{}]
  })

  // 分公司数据
  const branches = ref([
    { id: '1', name: '广西新豪智云技术股份有限公司' }
  ])

  // 职位类型数据 - 从API获取
  const positionTypes = ref([])

  // 职位数据 - 从API获取
  const positions = ref([])
  
  // 当前企业GUID
  const currentEnterpriseGuid = ref('')

  // 获取企业职位列表数据
  const fetchPositionList = async (enterpriseGuid, districtId) => {
    try {
      const response = await enterpriseApi.apiEnterprisePositionListGet(
        enterpriseGuid,                     // guid: 企业GUID
        selectedPositionType.value ? parseInt(selectedPositionType.value) : undefined, // positionType: 按职位类型筛选
        undefined,                          // cityID: 按城市ID筛选
        undefined,                          // paypackage: 按薪资筛选
        districtId,                        // districtId: 请求地市
        ApplicationPlatform.NUMBER_0             // from: 请求来源
      )

      if (response.data?.data) {
        const apiData = response.data.data
        
        // 更新职位类型筛选选项
        if (apiData.positionTypes && Array.isArray(apiData.positionTypes)) {
          positionTypes.value = apiData.positionTypes.map(type => ({
            id: type.value?.toString() || '',
            name: type.name || '',
            count: type.count || 0
          }))
        }
        
        // 映射职位列表数据
        if (apiData.positionList && Array.isArray(apiData.positionList)) {
          positions.value = apiData.positionList.map(dept => ({
            name: dept.departmentName || '未划分部门',
            positions: (dept.positions || []).map(position => ({
              id: position.positionGuid || position.positionID?.toString() || '',
              title: position.positionName || '',
              salary: position.payPackage || '',
              location: position.workPlace || '',
              experience: position.workAgeName || '',
              education: position.degreeName || '',
              recruitNum: position.positionAmount?.toString() || '若干',
              tags: position.positionKeywords?.map(k => k.keywordName || '') || [],
              updateTime: position.publishTime ? new Date(position.publishTime).toLocaleDateString() : '',
              type: position.positionTypeID?.toString() || ''
            }))
          })).filter(dept => dept.positions.length > 0)
        } else {
          positions.value = []
        }
      }
    } catch (error) {
      console.error('获取企业职位列表失败:', error)
      // 职位列表获取失败时显示空数据
      positions.value = []
      positionTypes.value = []
    }
  }

  // 获取企业详情数据
  const fetchCompanyDetail = async () => {
    try {
      loading.value = true
      
      // 获取当前城市对应的BussDistrict
      const currentCityInfo = cityStore.cityInfo
      let districtId = BussDistrict.NUMBER_0 // 默认广西
      
      // 根据城市ID映射到BussDistrict
      switch (currentCityInfo.id) {
        case 0:
          districtId = BussDistrict.NUMBER_0 // 广西
          break
        case 1:
          districtId = BussDistrict.NUMBER_1 // 桂林
          break
        case 2:
          districtId = BussDistrict.NUMBER_2 // 柳州
          break
        case 4:
          districtId = BussDistrict.NUMBER_4 // 梧州
          break
        case 6:
          districtId = BussDistrict.NUMBER_6 // 百色
          break
        case 7:
          districtId = BussDistrict.NUMBER_7 // 钦州
          break
        case 8:
          districtId = BussDistrict.NUMBER_8 // 河池
          break
        case 9:
          districtId = BussDistrict.NUMBER_9 // 北海
          break
        case 11:
          districtId = BussDistrict.NUMBER_11 // 防港
          break
        case 12:
          districtId = BussDistrict.NUMBER_12 // 玉林
          break
        case 13:
          districtId = BussDistrict.NUMBER_13 // 崇左
          break
        case 14:
          districtId = BussDistrict.NUMBER_14 // 贵港
          break
        case 15:
          districtId = BussDistrict.NUMBER_15 // 来宾
          break
        case 18:
          districtId = BussDistrict.NUMBER_18 // 贺州
          break
        case 19:
          districtId = BussDistrict.NUMBER_19 // 南宁
          break
        default:
          districtId = BussDistrict.NUMBER_0
      }

      // 调用企业详情API
      const detailResponse = await enterpriseApi.apiEnterpriseDetailGet(
        companyId,                          // guid: 企业GUID
        districtId,                        // districtId: 请求地市
        ApplicationPlatform.NUMBER_0,            // from: 请求来源
        EnterpriseClickFrom.NUMBER_1       // clickFrom: 点击来源 1=推荐企业
      )

      if (detailResponse.data?.data) {
        const apiData = detailResponse.data.data
        
        // 映射API数据到组件数据结构
        companyData.value = {
          ...companyData.value,
          id: companyId,
          name: apiData.enterpriseName || '',
          type: apiData.enterpriseProperty || '',
          scale: apiData.enterpriseEmployeeNumber || '',
          industry: apiData.enterpriseIndustry || '',
          logo: apiData.enterpriseLogoUrl || '//image.gxrc.com/gxrcsite/vip/headpic_ents_default.png',
          address: apiData.address || '',
          longitude: apiData.baiduLon || 108.374104,
          latitude: apiData.baiduLat || 22.805206,
          description: apiData.description || '',
          photos: apiData.visualImages?.map(img => img.url).filter(url => url) || [],
          video: apiData.visualVideos?.[0]?.origUrl || '',
          videoPoster: apiData.visualVideos?.[0]?.snapshotUrl || '',
          contact: {
            name: apiData.contractPerson || '',
            phone: apiData.enterprisePhone || '',
            email: apiData.enterpriseEmail || ''
          },
          dynamics: []
        }

        // 保存企业GUID用于其他API调用
        currentEnterpriseGuid.value = apiData.enterpriseGuid || ''

        // 获取企业职位列表
        if (currentEnterpriseGuid.value) {
          await fetchPositionList(currentEnterpriseGuid.value, districtId)
        }

        // 如果有工商信息GUID，获取工商信息
        if (apiData.enterpriseGuid) {
          await fetchBusinessInfo(apiData.enterpriseGuid, districtId)
        }
      }
    } catch (error) {
      console.error('获取企业详情失败:', error)
      ElMessage.error('获取企业详情失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 获取企业工商信息
  const fetchBusinessInfo = async (guid, districtId) => {
    try {
      const qccResponse = await enterpriseApi.apiEnterpriseQccGet(
        guid,                              // guid: 企业GUID
        districtId,                       // districtId: 请求地市
        ApplicationPlatform.NUMBER_0            // from: 请求来源
      )

      if (qccResponse.data?.data) {
        const qccData = qccResponse.data.data
        
        // 更新工商信息
        companyData.value.businessInfo = {
          legalPerson: qccData.operName || '',
          registeredCapital: qccData.registCapi || '',
          establishDate: qccData.startDate || '',
          registerDate: qccData.checkDate || qccData.startDate || '',
          status: qccData.status || '',
          registeredAddress: qccData.address || '',
          creditCode: qccData.creditCode || '',
          businessScope: qccData.scope || ''
        }
      }
    } catch (error) {
      console.error('获取企业工商信息失败:', error)
      // 工商信息获取失败不影响主要功能，只记录错误
    }
  }

  // 计算属性
  const truncatedDescription = computed(() => {
    if (companyData.value.description.length <= 300) {
      return companyData.value.description
    }
    return companyData.value.description.substring(0, 300) + '...'
  })

  const totalPositions = computed(() => {
    return positions.value.reduce((total, dept) => total + dept.positions.length, 0)
  })

  const filteredPositions = computed(() => {
    // 现在筛选由API处理，直接返回positions数据
    return positions.value
  })

  // 方法
  const handleSearch = () => {
    // 处理搜索逻辑
    console.log('搜索关键词:', searchKeyword.value)
  }

  const hidePhone = (phone) => {
    if (!phone) return ''
    return phone.replace(/(\d{4})\d{4}(\d+)/, '$1****$2')
  }

  const hideEmail = (email) => {
    if (!email) return ''
    const [username, domain] = email.split('@')
    return `${username.slice(0, 2)}****@${domain}`
  }

  const showContactInfo = () => {
    showPhone.value = true
    showEmail.value = true
    ElMessage.success('联系方式已显示')
  }

  const toggleDescription = () => {
    isDescriptionExpanded.value = !isDescriptionExpanded.value
  }

  const toggleFilter = () => {
    showFilter.value = !showFilter.value
  }

  const filterByType = async (typeId) => {
    selectedPositionType.value = typeId
    showFilter.value = false
    
    // 如果有企业GUID，重新获取筛选后的职位列表
    if (currentEnterpriseGuid.value) {
      // 获取当前城市的districtId
      const currentCityInfo = cityStore.cityInfo
      let districtId = BussDistrict.NUMBER_0
      
      switch (currentCityInfo.id) {
        case 0: districtId = BussDistrict.NUMBER_0; break
        case 1: districtId = BussDistrict.NUMBER_1; break
        case 2: districtId = BussDistrict.NUMBER_2; break
        case 4: districtId = BussDistrict.NUMBER_4; break
        case 6: districtId = BussDistrict.NUMBER_6; break
        case 7: districtId = BussDistrict.NUMBER_7; break
        case 8: districtId = BussDistrict.NUMBER_8; break
        case 9: districtId = BussDistrict.NUMBER_9; break
        case 11: districtId = BussDistrict.NUMBER_11; break
        case 12: districtId = BussDistrict.NUMBER_12; break
        case 13: districtId = BussDistrict.NUMBER_13; break
        case 14: districtId = BussDistrict.NUMBER_14; break
        case 15: districtId = BussDistrict.NUMBER_15; break
        case 18: districtId = BussDistrict.NUMBER_18; break
        case 19: districtId = BussDistrict.NUMBER_19; break
        default: districtId = BussDistrict.NUMBER_0
      }
      
      await fetchPositionList(currentEnterpriseGuid.value, districtId)
    }
  }

  const selectBranch = (branchId) => {
    selectedBranch.value = branchId
  }

  const showBusinessInfo = () => {
    showBusinessModal.value = true
  }

  const openPhotoModal = () => {
    showPhotoModal.value = true
  }

  const prevPhoto = () => {
    if (currentPhotoIndex.value > 0) {
      currentPhotoIndex.value--
    }
  }

  const nextPhoto = () => {
    if (companyData.value.photos && currentPhotoIndex.value < companyData.value.photos.length - 1) {
      currentPhotoIndex.value++
    }
  }

  const viewDynamicDetail = (dynamicId) => {
    // 跳转到动态详情页面
    console.log('查看动态详情:', dynamicId)
  }

  // 页面挂载
  onMounted(async () => {
    console.log('企业详情页面加载，企业ID:', companyId)
    
    // 获取企业详情数据
    await fetchCompanyDetail()
    
    selectedBranch.value = branches.value[0]?.id || ''
  })
</script>

<style scoped lang="scss">
  /* 企业详情页面专用样式 */
  .company-detail {

    /* 基础样式重置 */
    .header-clearfix::after,
    .main-clearfix::after,
    .intro-clearfix::after,
    .content-clearfix::after,
    .filter-clearfix::after,
    .branches-clearfix::after,
    .dynamics-clearfix::after {
      content: "";
      display: table;
      clear: both;
    }

    .header-fl,
    .content-fl {
      float: left;
    }

    .header-fr,
    .sidebar-fr {
      float: right;
    }

    .position-link,
    .filter-link,
    .section-more-link {
      color: #333333;
      text-decoration: none;
      font-weight: bold;

      &:hover {
        text-decoration: underline;
      }
    }

    /* 头部区域样式 */
    .header-section {
      background: #29343C;

      .header-container {
        height: 270px;
        color: #fff;
      }

      .header-logo {
        width: 120px;
        padding-top: 70px;

        .header-logo-image {
          display: block;
          width: 100px;
          height: 100px;
          border-radius: 8px;
          border: 1px solid #EEEEEE;
        }
      }

      .header-info {
        width: 650px;
        padding-top: 85px;

        .header-title {
          line-height: 36px;
          font-size: 28px;
          font-weight: normal;
          padding-bottom: 10px;
          color: #fff;
          margin: 0;
        }

        .header-subtitle {
          line-height: 30px;
          color: #fff;

          .header-separator {
            padding: 0 10px;
            font-size: 12px;
            color: #fff;
          }
        }
      }

      .header-media {
        .header-video-container {
          width: 352px;
          height: 198px;
          margin-top: 20px;
          position: relative;

          .header-video-player {
            width: 352px;
            height: 198px;
            border-radius: 4px;
            outline: none;
          }
        }
      }
    }

    /* 主要内容区域 */
    .main-section {
      background: #F4F5F9;

      .main-container {
        padding-bottom: 25px;
        padding-top: 1px;
      }
    }

    /* 企业介绍区域 */
    .intro-section {
      background: #fff;
      padding: 40px 30px;
      margin-bottom: 25px;
      margin-top: -40px;

      .intro-content {
        float: left;
        width: 60%;

        .intro-description {
          line-height: 32px;
          color: #333;
          white-space: pre-wrap;
          word-wrap: break-word;
          font-family: inherit;
          margin: 0;
          background: transparent;
          border: none;
          padding: 0;
        }

        .intro-expand-btn {
          float: left;
          display: block;
          line-height: 32px;
          color: #457CCF;
          cursor: pointer;
        }
      }

      .gallery-section {
        float: right;
        width: 352px;

        .gallery-carousel {
          position: relative;

          .gallery-image {
            width: 352px;
            height: 237px;
            border-radius: 4px;
            cursor: pointer;
          }

          .gallery-controls {
            position: absolute;
            bottom: 0;
            right: 0;

            .gallery-counter {
              line-height: 40px;
              color: #fff;
              font-size: 12px;
              position: absolute;
              bottom: 0;
              right: 26px;
              width: 60px;
              text-align: center;
              z-index: 3;
            }

            .gallery-navigation {

              .gallery-prev,
              .gallery-next {
                line-height: 40px;
                color: #fff;
                position: absolute;
                bottom: 0;
                cursor: pointer;
                z-index: 4;

                i {
                  font-size: 18px;
                }
              }

              .gallery-prev {
                right: 77px;
              }

              .gallery-next {
                right: 15px;
              }
            }
          }
        }
      }
    }

    /* 内容区域布局 */
    .content-wrapper {
      .content-left {
        width: 822px;
      }

      .sidebar-section {
        width: 354px;
      }
    }

    /* 通用区块样式 */
    .location-section,
    .contact-section,
    .business-section,
    .dynamics-section {
      padding: 30px;
      background: #fff;
      margin-bottom: 25px;
    }


    /* 标题样式 */
    .section-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 20px;
      position: relative;
      background: url('https://www.gxrc.com/Content/Images/company-title-bg.png') no-repeat left bottom;

      .section-more-link {
        float: right;
        font-size: 14px;
        font-weight: normal;
        color: #457CCF;
        text-decoration: none;
      }
    }

    /* 地址区域 */
    .location-section {
      .location-content {
        line-height: 30px;
        color: #4c4c4c;

        p {
          color: #666666;
          padding-bottom: 20px;

          .location-icon {
            color: #398EF2;
            margin-right: 3px;
            font-size: 21px;
          }
        }
      }
    }

    /* 职位区域 */
    .positions-section {
      padding: 30px 0rpx;
      background: #fff;
      margin-bottom: 25px;

      .section-title {
        position: relative;
        margin: 0 30px;
        padding: 30px 0 0 0;

        .filter-container {
          position: absolute;
          top: 15px;
          right: 0px;
          font-size: 14px;
          font-weight: normal;

          .filter-widget {
            .filter-trigger {
              width: 130px;
              height: 50px;
              line-height: 50px;
              color: #457CCF;
              text-align: center;
              cursor: pointer;

              .filter-text {
                display: block;
                float: left;
                width: 100px;
                height: 50px;
                line-height: 50px;
                color: #457CCF;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }

              .filter-icon {
                margin-left: 5px;
              }
            }

            .filter-dropdown {
              width: 450px;
              color: #666;
              background: #fff;
              position: absolute;
              top: 50px;
              right: 0;
              box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
              border-radius: 2px;
              border: 1px solid #EEEEEE;
              z-index: 999;

              ul {
                padding: 10px 15px;
              }

              li {
                float: left;
                width: 210px;
                border-bottom: 1px solid #f5f5f5;

                a {
                  display: block;
                  height: 36px;
                  line-height: 36px;
                  color: #666;
                  cursor: pointer;
                  text-decoration: none;

                  h3 {
                    float: left;
                    max-width: 170px;
                    padding: 0 10px;
                    font-weight: normal;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }

                  span {
                    float: left;
                    padding-right: 10px;
                  }

                  &:hover {

                    h3,
                    span {
                      color: #457CCF;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .branches-container {
        padding: 20px 0 0 30px;

        .branches-nav {
          display: flex;

          .branches-title {
            font-weight: bold;
            color: #333333;
            padding-right: 25px;
            line-height: 30px;
            margin-bottom: 20px;
          }

          .branches-item {
            cursor: pointer;
            line-height: 30px;
            color: #333;
            font-size: 16px;
            margin-bottom: 20px;
            position: relative;

            .branches-indicator {
              width: 32px;
              height: 3px;
              background: #fff;
              margin: 0 auto;
            }

            &.branches-selected {
              color: #457CCF;
              font-weight: bold;

              .branches-indicator {
                background: #457CCF;
              }
            }
          }
        }
      }

      .positions-content {
        .positions-list {
          width: 100%;

          thead th,
          td {
            color: #333;
          }

          .department-header {
            width: 822px;
            height: 43px;
            line-height: 43px;
            text-align: left;
            font-size: 14px;
            color: #666;
            background: #F4F5F9;
            font-weight: 400;
            padding-left: 20px;
          }

          .position-main {
            td {
              padding: 15px 30px 0 30px; 


              .position-title {
                float: left;
                max-width: 560px;
                font-size: 16px;
                color: #333;
                text-align: left;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                font-weight: bold;
              }

              .position-salary {
                float: left;
                font-size: 16px;
                color: #FC5C5B;
                font-weight: bold;
                padding-left: 20px;
              }
            }
          }

          .position-details {
            padding: 10px 30px;
            display: flex;
            border-bottom: 1px solid #F4F4F6;

            td {
              height: 36px;
              line-height: 36px;
              padding-right: 30px;
              padding-bottom: 5px;

            }

            .position-meta {
              flex: 1;

              span {
                display: block;
                float: left;
                height: 26px;
                line-height: 26px;
                padding: 0 10px;
                font-size: 12px;
                color: #8D97AC;
                background: #F5F6FB;
                border-radius: 2px;
                margin: 0px 10px 10px 0;
              }
            }

            .position-date {
              width: 100px;
              text-align: right;
              font-size: 12px;
              color: #999999;
            }
          }
        }
        
        .no-positions {
          padding: 30px;
          text-align: center;
          color: #666;
          font-size: 16px;
        }
      }
    }

    /* 联系信息区域 */
    .contact-section {
      border-bottom: 1px solid #EEEEEE;

      .contact-content {
        line-height: 30px;
        color: #4c4c4c;
      }

      .contact-item {
        line-height: 36px;
        color: #333333;

        .iconfont {
          font-size: 22px;
          color: rgb(214, 221, 229);
          margin-right: 10px;
        }

        .contact-label {
          color: #333;
        }

        .contact-btn {
          width: 52px;
          height: 24px;
          line-height: 22px;
          font-size: 14px;
          color: #457CCF;
          margin-left: 10px;
          background: #fff;
          border: 1px solid #457CCF;
          border-radius: 14px;
        }
      }
    }

    /* 工商信息区域 */
    .business-section {
      border-bottom: 0;

      .business-content {
        line-height: 30px;
        color: #4c4c4c;
      }

      .business-item {
        line-height: 36px;
        color: #333333;

        .iconfont  {
          font-size: 22px;
          color: rgb(214, 221, 229);
          margin-right: 10px;
        }

        .business-label {
          color: #333;
        }
      }

      .business-actions {
        text-align: center;
        padding-top: 20px;

        .business-btn {
          width: 133px;
          height: 40px;
          line-height: 38px;
          border-radius: 4px;
          font-size: 14px;
          background: #F5F6FB;
          color: #969DA7;
          border: 1px solid #F5F6FB;
        }
      }
    }

    /* 企业动态区域 */
    .dynamics-section {
      padding-bottom: 5px;

      .dynamics-content {
        line-height: 30px;
        color: #4c4c4c;
      }

      .dynamics-item {
        padding-bottom: 25px;
        cursor: pointer;

        .dynamics-position {
          float: left;
          width: 100px;
          padding: 10px 10px 0;
          position: relative;

          .dynamics-position-bg {
            position: absolute;
            width: 40px;
            right: 20px;
            bottom: 0;
          }

          h3 {
            font-size: 14px;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            position: relative;
            z-index: 100;
            line-height: 24px;
          }

          p {
            font-size: 12px;
            color: #F65F58;
            padding-top: 5px;
          }
        }

        .dynamics-info {
          float: right;
          width: 158px;

          h3 {
            line-height: 24px;
            font-size: 16px;
            color: #333333;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;

            &:hover {
              color: #457CCF;
            }
          }

          p {
            height: 24px;
            line-height: 24px;
            font-size: 14px;
            color: #999999;
            padding-top: 10px;

            .dynamics-like {
           
              i {
                margin-right: 4px;
              }
            }

            .dynamics-date {
              float: right;
            }
          }
        }
      }

      .dynamics-empty {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;

        p {
          font-size: 14px;
          color: #666666;
        }
      }
    }
  }

  /* 工商信息弹窗样式 */
  .business-modal {
    .business-table {
      width: 100%;

      td {
        line-height: 24px;
        color: #333333;
        padding-bottom: 20px;

        .business-field-label {
          color: #999999;
        }

        &.business-cell-w1 {
          width: 211px;
        }

        &.business-cell-w3 {
          width: 160px;
        }
      }
    }
  }

  /* 图片查看弹窗样式 */
  .gallery-modal {
    .gallery-viewer {
      position: relative;
      text-align: center;

      .gallery-fullscreen {
        max-width: 100%;
        max-height: 80vh;
      }

      .gallery-modal-controls {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        gap: 20px;

        .gallery-modal-counter {
          color: #fff;
          font-size: 16px;
          background: rgba(0, 0, 0, 0.5);
          padding: 5px 10px;
          border-radius: 4px;
        }
      }

      .gallery-modal-close {
        position: absolute;
        top: 20px;
        right: 20px;
      }
    }
  }

  /* 保留用户的全局样式 */
  body,
  pre {
    font: 14px/1.5 Microsoft YaHei, tahoma, arial, Hiragino Sans GB, \\5b8b\4f53, sans-serif;
    color: #333
  }

  body,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  p,
  blockquote,
  dl,
  dt,
  dd,
  ul,
  ol,
  li,
  pre,
  form,
  fieldset,
  legend,
  button,
  input,
  textarea,
  th,
  td {
    margin: 0;
    padding: 0
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: 100%;
    font-weight: bold
  }

  strong {
    font-weight: bold
  }

  address,
  cite,
  dfn,
  em,
  var {
    font-style: normal
  }

  small {
    font-size: 12px
  }

  ul,
  ol,
  li {
    list-style: none
  }

  a {
    color: #333;
    text-decoration: none
  }

  a:hover {
    text-decoration: none
  }

  sup {
    vertical-align: text-top
  }

  sub {
    vertical-align: text-bottom
  }

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  legend {
    color: #000
  }

  fieldset,
  img {
    border: 0
  }

  button,
  input,
  select,
  textarea {
    font-size: 100%;
    outline: none;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0
  }

  em,
  i {
    font-style: normal
  }

  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both
  }

  .clearfix {
    zoom: 1
  }

  .fl {
    float: left
  }

  .fr {
    float: right
  }

  .clear {
    clear: both
  }

  .w960 {
    width: 960px;
    margin: 0 auto
  }

  .w1000 {
    width: 1000px;
    margin: 0 auto
  }

  .w1200 {
    width: 1200px;
    margin: 0 auto
  }

  .none {
    display: none
  }

  .red {
    color: red
  }

  .green {
    color: green
  }

  .orange {
    color: #f60
  }

  .bg-white {
    background: #fff
  }

  .tc {
    text-align: center
  }

  .tl {
    text-align: left
  }

  .tr {
    text-align: right
  }

  .ti {
    text-indent: 2em
  }
  
  /* 加载状态样式 */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: #f5f5f5;
  }
  
  .loading-content {
    display: flex;
    align-items: center;
    padding: 20px 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 16px;
    color: #666;
  }
</style>