<template>
  <div class="consultation-search">
    <!-- 标题 -->
    <h2 class="text-24px font-bold text-#0E2644 mb-20px"> <img 
            src="//image.gxrc.com/thirdParty/gxjy/pc/zyzd/zx-icon.png"
            alt="咨询服务搜索"
            class="w-32px h-32px mr-8px"
          />咨询服务搜索</h2>
    
    <!-- 搜索表单 -->
    <div class="search-form bg-#F1F7FB rounded-8px p-20px mb-20px">
      <el-form ref="searchFormRef" :model="searchForm" inline class="search-form-content">
        <el-form-item label="" prop="startTime">
          <el-date-picker
            v-model="searchForm.startTime"
            type="datetime"
            placeholder="请选择开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="time"
          />
        </el-form-item>
        
        <el-form-item label="" prop="endTime">
          <el-date-picker
            v-model="searchForm.endTime"
            type="datetime"
            placeholder="请选择结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="time"
          />
        </el-form-item>
        
        <el-form-item label="" prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入问题描述关键词"
            class="descri"
          />
        </el-form-item>
        
        <el-form-item>
          <div class="flex space-x-10px">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleClearSearch">清空</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格和分页 -->
    <div class="table-section">
      <el-table 
        :data="currentPageData" 
        stripe 
        class="consultation-table"
        :header-cell-style="{ backgroundColor: '#EEF6FB', borderRadius: '4px' }"
        :row-style="{ height: '50px' }"
        v-loading="loading"
      >
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="description" label="问题描述" min-width="200" />
        <el-table-column prop="consultationTime" label="咨询时间" width="180" />
        <el-table-column prop="guidanceType" label="指导类型" width="120" />
        <el-table-column prop="isReplied" label="是否已答复" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isReplied ? 'success' : 'danger'">
              {{ row.isReplied ? '已答复' : '未答复' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="replyTime" label="回复时间" width="180" />
        <el-table-column prop="expert" label="回复咨询专家" width="150" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 空状态 -->
      <el-empty v-if="filteredData.length === 0 && !loading" description="暂无数据" />
      
      <!-- 分页 -->
      <div class="pagination-wrapper mt-20px flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredData.length"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 搜索表单引用
const searchFormRef = ref()

// 搜索表单数据
const searchForm = reactive({
  startTime: '',
  endTime: '',
  keyword: ''
})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)

// 模拟数据
const mockData = [
  {
    id: 1,
    description: '关于职业规划指导的咨询',
    consultationTime: '2025-01-15 10:30:00',
    guidanceType: '职业规划指导',
    isReplied: true,
    replyTime: '2025-01-16 14:20:00',
    expert: '张专家'
  },
  {
    id: 2,
    description: '创业政策相关问题咨询',
    consultationTime: '2025-01-14 15:45:00',
    guidanceType: '创业指导',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 3,
    description: '求职技能培训相关咨询',
    consultationTime: '2025-01-13 09:15:00',
    guidanceType: '求职技能指导',
    isReplied: true,
    replyTime: '2025-01-14 11:30:00',
    expert: '李专家'
  },
  {
    id: 4,
    description: '社会保险政策解读',
    consultationTime: '2025-01-12 16:20:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2025-01-13 10:15:00',
    expert: '王专家'
  },
  {
    id: 5,
    description: '劳动关系争议处理咨询',
    consultationTime: '2025-01-11 11:00:00',
    guidanceType: '其他咨询',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 6,
    description: '劳动法相关咨询',
    consultationTime: '2025-01-10 14:20:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2025-01-11 10:30:00',
    expert: '刘专家'
  },
  {
    id: 7,
    description: '技能培训补贴申请',
    consultationTime: '2025-01-09 11:45:00',
    guidanceType: '求职技能指导',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 8,
    description: '失业保险金申领流程',
    consultationTime: '2025-01-08 16:30:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2025-01-09 09:15:00',
    expert: '王专家'
  },
  {
    id: 9,
    description: '创业担保贷款咨询',
    consultationTime: '2025-01-07 13:20:00',
    guidanceType: '创业指导',
    isReplied: true,
    replyTime: '2025-01-08 11:40:00',
    expert: '赵专家'
  },
  {
    id: 10,
    description: '求职面试技巧指导',
    consultationTime: '2025-01-06 10:10:00',
    guidanceType: '求职技能指导',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 11,
    description: '简历制作指导',
    consultationTime: '2025-01-05 15:30:00',
    guidanceType: '求职技能指导',
    isReplied: true,
    replyTime: '2025-01-06 14:20:00',
    expert: '李专家'
  },
  {
    id: 12,
    description: '退休后再就业政策',
    consultationTime: '2025-01-04 09:45:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2025-01-05 11:15:00',
    expert: '张专家'
  },
  {
    id: 13,
    description: '工伤保险赔偿标准咨询',
    consultationTime: '2025-01-03 14:30:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2025-01-04 10:20:00',
    expert: '王专家'
  },
  {
    id: 14,
    description: '大学生就业补贴政策',
    consultationTime: '2025-01-02 11:15:00',
    guidanceType: '政策解读',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 15,
    description: '职业技能鉴定流程',
    consultationTime: '2025-01-01 16:45:00',
    guidanceType: '求职技能指导',
    isReplied: true,
    replyTime: '2025-01-02 09:30:00',
    expert: '李专家'
  },
  {
    id: 16,
    description: '创业孵化基地申请',
    consultationTime: '2024-12-31 13:20:00',
    guidanceType: '创业指导',
    isReplied: true,
    replyTime: '2025-01-01 11:10:00',
    expert: '赵专家'
  },
  {
    id: 17,
    description: '灵活就业人员社保缴费',
    consultationTime: '2024-12-30 10:30:00',
    guidanceType: '政策解读',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 18,
    description: '职场沟通技巧培训',
    consultationTime: '2024-12-29 15:20:00',
    guidanceType: '求职技能指导',
    isReplied: true,
    replyTime: '2024-12-30 14:15:00',
    expert: '张专家'
  },
  {
    id: 19,
    description: '企业裁员补偿标准',
    consultationTime: '2024-12-28 09:40:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2024-12-29 11:25:00',
    expert: '王专家'
  },
  {
    id: 20,
    description: '网络创业指导咨询',
    consultationTime: '2024-12-27 14:50:00',
    guidanceType: '创业指导',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 21,
    description: '职业转换期心理辅导',
    consultationTime: '2024-12-26 11:30:00',
    guidanceType: '职业规划指导',
    isReplied: true,
    replyTime: '2024-12-27 10:45:00',
    expert: '刘专家'
  },
  {
    id: 22,
    description: '外地务工人员权益保障',
    consultationTime: '2024-12-25 16:15:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2024-12-26 09:20:00',
    expert: '李专家'
  },
  {
    id: 23,
    description: '电商创业扶持政策',
    consultationTime: '2024-12-24 13:40:00',
    guidanceType: '创业指导',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 24,
    description: '职业病防护知识咨询',
    consultationTime: '2024-12-23 10:25:00',
    guidanceType: '其他咨询',
    isReplied: true,
    replyTime: '2024-12-24 14:30:00',
    expert: '王专家'
  },
  {
    id: 25,
    description: '中年转岗就业指导',
    consultationTime: '2024-12-22 15:10:00',
    guidanceType: '职业规划指导',
    isReplied: true,
    replyTime: '2024-12-23 11:15:00',
    expert: '张专家'
  },
  {
    id: 26,
    description: '残疾人就业扶持政策',
    consultationTime: '2024-12-21 09:55:00',
    guidanceType: '政策解读',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 27,
    description: '职业技能竞赛参赛指导',
    consultationTime: '2024-12-20 14:20:00',
    guidanceType: '求职技能指导',
    isReplied: true,
    replyTime: '2024-12-21 10:40:00',
    expert: '李专家'
  },
  {
    id: 28,
    description: '个体工商户注册流程',
    consultationTime: '2024-12-19 11:45:00',
    guidanceType: '创业指导',
    isReplied: true,
    replyTime: '2024-12-20 13:25:00',
    expert: '赵专家'
  },
  {
    id: 29,
    description: '年龄歧视就业维权',
    consultationTime: '2024-12-18 16:30:00',
    guidanceType: '其他咨询',
    isReplied: false,
    replyTime: '',
    expert: ''
  },
  {
    id: 30,
    description: '远程办公政策解读',
    consultationTime: '2024-12-17 12:15:00',
    guidanceType: '政策解读',
    isReplied: true,
    replyTime: '2024-12-18 09:50:00',
    expert: '刘专家'
  }
]

// 过滤后的数据
const filteredData = computed(() => {
  let data = [...mockData]
  
  if (searchForm.keyword) {
    data = data.filter(item => 
      item.description.includes(searchForm.keyword) || 
      item.guidanceType.includes(searchForm.keyword) ||
      item.expert.includes(searchForm.keyword)
    )
  }
  
  if (searchForm.startTime) {
    data = data.filter(item => 
      new Date(item.consultationTime) >= new Date(searchForm.startTime)
    )
  }
  
  if (searchForm.endTime) {
    data = data.filter(item => 
      new Date(item.consultationTime) <= new Date(searchForm.endTime)
    )
  }
  
  return data
})

// 当前页数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 搜索功能
const handleSearch = () => {
  loading.value = true
  // 模拟API延迟
  setTimeout(() => {
    currentPage.value = 1 // 搜索后重置到第一页
    loading.value = false
    ElMessage.success(`搜索完成，找到 ${filteredData.value.length} 条记录`)
  }, 300)
}

// 清空搜索
const handleClearSearch = () => {
  if (!searchFormRef.value) return
  searchFormRef.value.resetFields()
  currentPage.value = 1
  ElMessage.info('已清空搜索条件')
}

// 查看详情
const viewDetail = (row) => {
  ElMessage.info(`查看详情：${row.description}`)
  // 这里可以打开详情弹窗或跳转到详情页面
}

// 分页大小改变
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
}

// 当前页改变
const handleCurrentChange = (page) => {
  currentPage.value = page
}
</script>

<style scoped lang="scss">
.consultation-search {
  .search-form {
    .search-form-content {
      .el-form-item {
        margin-bottom: 10px;
      }
      .time{
        width: 240px;
      }
      .descri{
        width: 300px;
      }
    }
  }
  
  .consultation-table {
    .el-table__body tr {
      height: 50px;
      
      td {
        font-size: 14px;
        color: #333333;
        border-bottom: 1px solid #E9E9E9;
      }
    }
    
    .el-table__header {
      th {
        background-color: #EEF6FB !important;
        border-radius: 4px;
        font-weight: bold;
        color: #333333;
        font-size: 14px;
      }
    }
  }
  
  .pagination-wrapper {
    :deep(.el-pagination) {
      .el-pager li {
        background-color: #fff;
        border-radius: 6px;
      }
      
      .el-pager li.is-active {
        background: var(--el-color-primary);
      }
      
      button {
        border-radius: 6px;
        background: #fff;
      }
    }
  }
}
</style> 