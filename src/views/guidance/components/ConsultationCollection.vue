<template>
  <div class="consultation-collection">
    <!-- 广告模块 -->
    <div class="ad-section mb-20px">
      <img 
      src="//image.gxrc.com/thirdParty/gxjy/pc/zyzd/zx-banner.png"
        alt="广告图片"
        class="w-full max-w-1157px h-300px rounded-8px object-cover mx-auto block"
      />
    </div>

    <!-- 咨询服务采集表单 -->
    <div class="form-section">
     
      <h2 class="text-24px font-bold text-#0E2644 mb-20px flex items-center">
        <img 
          src="//image.gxrc.com/thirdParty/gxjy/pc/zyzd/cj-icon.png"
          alt="咨询服务采集"
          class="w-32px h-32px mr-8px"
        />
        咨询服务采集
      </h2>
      
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="180px"
        class="consultation-form"
      >
        <!-- 第一行：咨询经办机构 + 指导类型 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="咨询经办机构" prop="agency" required>
              <el-select 
                v-model="formData.agency" 
                placeholder="请选择咨询经办机构"
                class="w-full"
              >
                <el-option
                  v-for="agency in agencyOptions"
                  :key="agency.value"
                  :label="agency.label"
                  :value="agency.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指导类型" prop="guidanceType" required>
              <el-select 
                v-model="formData.guidanceType" 
                placeholder="请选择指导类型"
                class="w-full"
              >
                <el-option
                  v-for="type in guidanceTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：咨询人姓名/单位名称 + 邮箱 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="咨询人姓名/单位名称" prop="consultantName" required>
              <el-input 
                v-model="formData.consultantName" 
                placeholder="请输入咨询人姓名或单位名称"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input 
                v-model="formData.email" 
                placeholder="请输入邮箱地址"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：咨询时间 + 咨询服务类型 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="咨询时间" prop="consultationTime" required>
              <el-date-picker
                v-model="formData.consultationTime"
                type="datetime"
                placeholder="请选择咨询时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询服务类型" prop="serviceType" required>
              <el-select 
                v-model="formData.serviceType" 
                placeholder="请选择咨询服务类型"
                class="w-full"
              >
                <el-option
                  v-for="service in serviceTypes"
                  :key="service.value"
                  :label="service.label"
                  :value="service.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：咨询问题描述（单独一行） -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="咨询问题描述" prop="description" required>
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="6"
                placeholder="请输入咨询问题描述，限制2000字"
                maxlength="2000"
                show-word-limit
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 按钮行（居中） -->
        <el-row>
          <el-col :span="24">
            <el-form-item class="button-group">
              <div class="flex justify-center space-x-20px">
                <el-button 
                  type="primary" 
                  @click="handleSubmit"
                  class="save-btn"
                >
                  保存
                </el-button>
                <el-button 
                  @click="handleClear"
                  class="clear-btn"
                >
                  清空
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 成功提交弹窗 -->
    <div 
      v-if="showSuccessModal" 
      class="success-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="closeSuccessModal"
    >
      <div class="w-273px h-139px bg-white rounded-8px flex items-center justify-center">
        <p class="text-center text-16px">保存提交成功！</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  agency: '',
  guidanceType: '',
  consultantName: '',
  email: '',
  consultationTime: '',
  serviceType: '',
  description: ''
})

// 成功弹窗状态
const showSuccessModal = ref(false)

// 模拟数据 - 咨询经办机构
const agencyOptions = ref([
  { label: '广西壮族自治区人力资源和社会保障厅', value: 'gxhrss' },
  { label: '南宁市人力资源和社会保障局', value: 'nnhrss' },
  { label: '桂林市人力资源和社会保障局', value: 'glhrss' },
  { label: '柳州市人力资源和社会保障局', value: 'lzhrss' },
  { label: '梧州市人力资源和社会保障局', value: 'wzhrss' },
  { label: '北海市人力资源和社会保障局', value: 'bhhrss' }
])

// 模拟数据 - 指导类型
const guidanceTypes = ref([
  { label: '职业规划指导', value: 'career_planning' },
  { label: '求职技能指导', value: 'job_skills' },
  { label: '创业指导', value: 'entrepreneurship' },
  { label: '职业培训咨询', value: 'training_consultation' },
  { label: '政策解读', value: 'policy_interpretation' },
  { label: '其他咨询', value: 'other' }
])

// 模拟数据 - 咨询服务类型
const serviceTypes = ref([
  { label: '就业政策咨询', value: 'employment_policy' },
  { label: '职业指导服务', value: 'career_guidance' },
  { label: '创业服务指导', value: 'startup_guidance' },
  { label: '技能培训咨询', value: 'skill_training' },
  { label: '劳动关系咨询', value: 'labor_relations' },
  { label: '社会保险咨询', value: 'social_insurance' }
])

// 表单验证规则
const formRules: FormRules = {
  agency: [
    { required: true, message: '请选择咨询经办机构', trigger: 'change' }
  ],
  guidanceType: [
    { required: true, message: '请选择指导类型', trigger: 'change' }
  ],
  consultantName: [
    { required: true, message: '请输入咨询人姓名或单位名称', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  consultationTime: [
    { required: true, message: '请选择咨询时间', trigger: 'change' }
  ],
  serviceType: [
    { required: true, message: '请选择咨询服务类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入咨询问题描述', trigger: 'blur' },
    { max: 2000, message: '咨询问题描述不能超过2000字', trigger: 'blur' }
  ]
}

// 判断是否登录
const isUserLoggedIn = () => {
  // 这里应该从用户状态或token中判断是否登录
  // 暂时返回false模拟未登录状态
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!isUserLoggedIn()) {
    // 创建自定义登录弹窗
    const modal = document.createElement('div')
    modal.className = 'login-modal-overlay'
    modal.innerHTML = `
      <div class="login-modal-content">
        <p>请登录后再保存提交！</p>
        <button class="login-btn">去登录</button>
      </div>
    `
    
    // 添加样式
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    `
    
    const content = modal.querySelector('.login-modal-content') as HTMLElement
    content.style.cssText = `
      width: 273px;
      height: 139px;
      background: white;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
    `
    
    const button = modal.querySelector('.login-btn') as HTMLElement
    button.style.cssText = `
      padding: 8px 16px;
      background: #2878FF;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    `
    
    // 添加事件监听
    button.addEventListener('click', () => {
      // 跳转到登录页面，暂时跳转到首页
      window.location.href = '/'
    })
    
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal)
      }
    })
    
    document.body.appendChild(modal)
    return
  }

  if (!formRef.value) return

  await formRef.value.validate((valid) => {
    if (valid) {
      // 打印表单数据（临时处理，后续替换为API调用）
      console.log('表单提交数据:', formData)
      
      // 显示成功弹窗
      showSuccessModal.value = true
      
      // 3秒后自动关闭弹窗
      setTimeout(() => {
        showSuccessModal.value = false
      }, 3000)
    }
  })
}

// 清空表单
const handleClear = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

// 关闭成功弹窗
const closeSuccessModal = () => {
  showSuccessModal.value = false
}
</script>

<style scoped lang="scss">
.consultation-collection {
  .consultation-form {
    max-width: 1000px;
    
    .button-group {
      margin-top: 20px;
      
      :deep(.el-form-item__content) {
        justify-content: center;
      }
    }
  }

  .save-btn {
    width: 123px;
    height: 40px;
    border-radius: 4px;
    background-color: #2878FF;
    color: #FFFFFF;
    font-size: 14px;
    border: none;
    
    &:hover {
      background-color: #1c5dd4;
    }
  }

  .clear-btn {
    width: 123px;
    height: 40px;
    border-radius: 4px;
    background-color: #FFFFFF;
    color: #757575;
    font-size: 14px;
    border: 1px solid #BBBBBB;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }

  .success-modal {
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
</style> 