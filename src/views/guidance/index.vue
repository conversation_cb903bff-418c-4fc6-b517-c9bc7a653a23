<template>
  <div class="bg-white w-1200px mx-auto p-30px">
    <div class="guidance-tabs mb-20px">
      <div class="flex justify-center items-center space-x-40px">
        <div 
          class="tab-item flex items-center justify-center cursor-pointer w-180px"
          :class="{ 'active': activeTab === 'collection' }"
          @click="switchTab('collection')"
        >
          <img 
            src="//image.gxrc.com/thirdParty/gxjy/pc/zyzd/cj-icon.png"
            alt="咨询服务采集"
            class="w-32px h-32px mr-8px"
          />
          <span class="text-16px font-bold">咨询服务采集</span>
        </div>
        <div 
          class="tab-item flex items-center justify-center cursor-pointer w-180px"
          :class="{ 'active': activeTab === 'search' }"
          @click="handleSearchTabClick"
        >
          <img 
            src="//image.gxrc.com/thirdParty/gxjy/pc/zyzd/zx-icon.png"
            alt="咨询服务搜索"
            class="w-32px h-32px mr-8px"
          />
          <span class="text-16px font-bold">咨询服务搜索</span>
        </div>
      </div>
      <div class="tab-line mt-10px"></div>
    </div>
    
    <div class="guidance-content">
      <ConsultationCollection v-if="activeTab === 'collection'" />
      <ConsultationSearch v-if="activeTab === 'search'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ConsultationCollection from './components/ConsultationCollection.vue'
import ConsultationSearch from './components/ConsultationSearch.vue'

// 标签页状态
const activeTab = ref('collection')

// 判断是否登录的模拟函数
const isUserLoggedIn = () => {
  // 这里应该从用户状态或token中判断是否登录
  // 暂时返回false模拟未登录状态
  return true
}

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
}

// 处理搜索标签点击
const handleSearchTabClick = () => {
  if (isUserLoggedIn()) {
    switchTab('search')
  } else {
    showLoginModal()
  }
}

// 显示登录提示弹窗
const showLoginModal = () => {
  // 创建自定义弹窗
  const modal = document.createElement('div')
  modal.className = 'login-modal-overlay'
  modal.innerHTML = `
    <div class="login-modal-content">
      <p>请登录后再查看记录！</p>
      <button class="login-btn">去登录</button>
    </div>
  `
  
  // 添加样式
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  `
  
  const content = modal.querySelector('.login-modal-content') as HTMLElement
  content.style.cssText = `
    width: 273px;
    height: 139px;
    background: white;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
  `
  
  const button = modal.querySelector('.login-btn') as HTMLElement
  button.style.cssText = `
    padding: 8px 16px;
    background: #2878FF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  `
  
  // 添加事件监听
  button.addEventListener('click', () => {
    // 跳转到登录页面，暂时跳转到首页
    window.location.href = '/'
  })
  
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      document.body.removeChild(modal)
    }
  })
  
  document.body.appendChild(modal)
}
</script>

<style scoped lang="scss">
.guidance-tabs {
  .tab-item {
    color: #333333;
    height: 50px;
    position: relative;
    transition: color 0.3s ease;
    margin: 0 100px;
    
    &.active {
      color: #2C71E1;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        height: 3px;
        background-color: #2C71E1;
        border-radius: 2px 2px 0 0;
      }
    }
  }
  
  .tab-line {
    height: 1px;
    background-color: #e0e0e0;
  }
}
</style>