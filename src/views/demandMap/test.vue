<template>
    <div class="more-list">

        <div class="table_box">
            <p>html高亮</p>
            <div class="tabs">

                <el-tabs v-model="post.highlight" @change="changeTab">
                    <el-tab-pane v-for="item in tabsList" :key="item.value" :label="item.name" :name="item.value" />
                </el-tabs>
            </div>
            <div style="margin-bottom: 10px;">
                <el-input v-model="asyncPost.keywords" placeholder="请输入关键词" style="width: 200px;" class="search-input" />
                <el-button @click="search" style="margin-left: 10px;">搜索</el-button>
                <el-button @click="clear">清空搜索项</el-button>
            </div>
            <Pagination :showAffix="false"  ref="paginationRef" :ApiFunction="apiFunction" :option="post"
                :asyncOption="asyncPost">
                <template #default="{ tableData, loading, empty }">
                    <el-table :data="tableData as SocialDynamicsInfoOutput[]" tooltip-effect="dark" style="width: 100%">
                        <el-table-column prop="title" label="标题">
                            <template #default="scope">
                                <div v-html="scope.row.title"></div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="content" label="内容" />
                        <el-table-column prop="senderGuid" label="发送者GUID" />
                        <el-table-column prop="senderName" label="发送者名称" />
                        <el-table-column prop="senderAvatar" label="发送者头像" />
                        <el-table-column prop="senderGuid" label="发送者GUID" />
                        <el-table-column prop="senderName" label="发送者名称" />
                        <el-table-column prop="senderAvatarUrl" label="发送者头像" >
                            <template #default="scope">
                                <el-image :src="scope.row.senderAvatarUrl" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- <el-empty :image-size="200" :description="'暂无数据'" v-if="empty" /> 自定义空状态 --> 
                </template>
            </Pagination>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import Pagination from '@/components/table/Pagination.vue';
import { getPcAPI } from '@/utils/axios-utils';
import { SocialdynamicsApi } from '@/api-services/apis/socialdynamics-api'
import { SocialDynamicsInfoOutput } from '@/api-services/models/social-dynamics-info-output';

const changeTab = (value: string) => {
    console.log(value)
}

const tabsList = [
    { name: '高亮', value: true },
    { name: '不高亮', value: false },
];

const apiFunction = (post: any) => {
    //get方法受传参顺序限制，需要手动对齐签名
    return getPcAPI(SocialdynamicsApi).apiSocialdynamicsDataSearchbypositionGet(post.keywords, post.page, post.pageSize,post.highlight)

}


const paginationRef = ref(null)

const search = () => {
    paginationRef.value.asyncOptionReset()
}

const clear = () => {
    const { asyncOption, post } = paginationRef.value.getOriginalOption()
    for (let key in asyncPost.value) {
        asyncPost.value[key] = asyncOption[key]
    }
    paginationRef.value.asyncOptionReset()
}

const post = ref({
    highlight: true
})

const asyncPost = ref({
    keywords: ''
})


onMounted(()=>{
   
})


</script>
<style lang="scss" scoped>
.more-list {
    width: 100%;
    background-color: #fff;
    padding: 20px;
    box-sizing: border-box;
    margin-top: 20px;

    .table_box {
    }
}
</style>