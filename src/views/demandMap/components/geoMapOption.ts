import geoJson from "../json/map.gx.json";

export const getOption = (json: typeof geoJson, data: typeof mock) => {
  return {
    geo: {
      map: "iceland",
      roam: true,
      aspectScale: 1,
      zoom:1.2,
      // nameProperty: 'name_en', // If using en name.
      itemStyle: {
        areaColor: "#006bd5",
      },
      emphasis: {
        label: { show: false },
      },
      selectedMode: true,
      select: {
        disabled: false,
        label: {
          show: false,
        },
        itemStyle: {
          areaColor: "rgb(9 133 255)",
        },
      },
      tooltip: {
        show: true,
        backgroundColor: "none",
        padding:0,
        borderColor:'none',
        borderWidth:0,
        formatter: function (params: any) {
          const name = params.name;
          const cityData = data.find((item) => item.name === name);
          if (!cityData) return "";
          return `<div class="tooltip_8_15">
            <div class="title">${name}</div>
            <div class="line"></div>
            <div class="content">
                <div class="content-item">
                   <div class="dot"></div>
                   <div class="text">
                    单位数:<span class="text-value">${cityData.data.dws}</span>家
                   </div>
                </div>
                <div class="content-item zws">
                   <div class="dot"></div>
                   <div class="text">
                    职位数:<span class="text-value">${cityData.data.zws}</span>个
                   </div>
                </div>
                <div class="content-item zprs">
                   <div class="dot"></div>
                   <div class="text">
                    招聘人数:<span class="text-value">${cityData.data.zprs}</span>人
                   </div>
                </div>
                <div class="content-item jls">
                   <div class="dot"></div>
                   <div class="text">
                    简历数:<span class="text-value">${cityData.data.jlis}</span>份
                   </div>
                </div>
            </div>
        </div>`;
        },
      },
    },
    tooltip: {
      show: false,
    },
    series: {
      type: "effectScatter",
      coordinateSystem: "geo",
      geoIndex: 0,
      symbolSize: function (params) {
        return (params[2] / 100) * 15 + 5;
      },
      label: {
        show: true,
        color: "#fff",
        position: "top",
        formatter: "{@[3]}",
      },
      itemStyle: {
        color: "#fff",
      },
      data: [
        ...json.features.map((item) => {
          return [
            item.properties.center[0],
            item.properties.center[1],
            20,
            item.properties.name,
          ];
        }),
      ],
    },
  };
};

export const getRegionData = (json: typeof geoJson) => {
  const regionArr = json.features.map((item, index) => {
    return {
      code: item.properties.adcode,
      name: item.properties.name,
      jsonIndex: index,
    };
  });

  return {
    regionList: regionArr,
    regionMap: new Map(regionArr.map((item) => [item.code, item])),
  };
};

export const mock = [
  {
    id: 450100,
    name: "南宁市",
    data: {
      dws: "100",
      zws: "110",
      zprs: "120",
      jlis: "130",
    },
  },
  {
    id: 450200,
    name: "柳州市",
    data: {
      dws: "140",
      zws: "150",
      zprs: "160",
      jlis: "170",
    },
  },
  {
    id: 450300,
    name: "桂林市",
    data: {
      dws: "180",
      zws: "190",
      zprs: "200",
      jlis: "210",
    },
  },
  {
    id: 450400,
    name: "梧州市",
    data: {
      dws: "220",
      zws: "230",
      zprs: "240",
      jlis: "250",
    },
  },
  {
    id: 450500,
    name: "北海市",
    data: {
      dws: "260",
      zws: "270",
      zprs: "280",
      jlis: "290",
    },
  },
  {
    id: 450600,
    name: "防城港市",
    data: {
      dws: "300",
      zws: "310",
      zprs: "320",
      jlis: "330",
    },
  },
  {
    id: 450700,
    name: "钦州市",
    data: {
      dws: "340",
      zws: "350",
      zprs: "360",
      jlis: "370",
    },
  },
  {
    id: 450800,
    name: "贵港市",
    data: {
      dws: "380",
      zws: "390",
      zprs: "400",
      jlis: "410",
    },
  },
  {
    id: 450900,
    name: "玉林市",
    data: {
      dws: "420",
      zws: "430",
      zprs: "440",
      jlis: "450",
    },
  },
  {
    id: 451000,
    name: "百色市",
    data: {
      dws: "460",
      zws: "470",
      zprs: "480",
      jlis: "490",
    },
  },
  {
    id: 451100,
    name: "贺州市",
    data: {
      dws: "500",
      zws: "510",
      zprs: "520",
      jlis: "530",
    },
  },
  {
    id: 451200,
    name: "河池市",
    data: {
      dws: "540",
      zws: "550",
      zprs: "560",
      jlis: "570",
    },
  },
  {
    id: 451300,
    name: "来宾市",
    data: {
      dws: "580",
      zws: "590",
      zprs: "600",
      jlis: "610",
    },
  },
  {
    id: 451400,
    name: "崇左市",
    data: {
      dws: "620",
      zws: "630",
      zprs: "640",
      jlis: "650",
    },
  },
];
