<template>
    <div class="map-over-view">
        <div class="left_map">
            <GeoMap />
        </div>
        <div class="right_postions_list">
            <Pagination :fixedPageSize="10" :changePageToTop="false" :layout="`total, prev, pager, next`"
                :showAffix="false" :allFn="false" ref="paginationRef" :ApiFunction="apiFunction" :option="post">
                <template #default="{ tableData, loading, empty }">
                    <el-table :header-row-class-name="headerRowClassName" :row-class-name="tableRowClassName"
                        :data="tableData as SocialDynamicsInfoOutput[]" tooltip-effect="dark"
                        style="width: 100%; height: 363px" v-loading="loading">
                        <el-table-column prop="title" label="职位名称">
                            <template #default="scope">
                                <p class="position-name">{{ scope.row.title }}</p>
                            </template>
                        </el-table-column>
                        <el-table-column prop="content" label="薪资" align="center" width="120">
                            <template #default="scope">
                                <p class="salary">{{ '8000-12000' }}</p>
                            </template>
                        </el-table-column>
                        <el-table-column prop="senderAvatarUrl" align="center" width="100" label="操作">
                            <template #default="scope">
                                <el-button type="primary" text @click="handleDetail(scope.row)">详情</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </Pagination>
        </div>
    </div>
</template>

<script setup lang="ts">
import GeoMap from "../components/geoMap.vue";
import { ref } from "vue";
import Pagination from "@/components/table/Pagination.vue";
import { getPcAPI } from "@/utils/axios-utils";
import { SocialdynamicsApi } from "@/api-services/apis/socialdynamics-api";
import { SocialDynamicsInfoOutput } from "@/api-services/models/social-dynamics-info-output";

const post = ref({
    keywords: "",
});

const headerRowClassName = () => {
    return "header-row";
};

const tableRowClassName = ({
    row,
    rowIndex,
}: {
    row: SocialDynamicsInfoOutput;
    rowIndex: number;
}) => {
    if (rowIndex % 2 === 0) {
        return "even-row";
    }
    return "odd-row";
};

const apiFunction = (post: any) => {
    //get方法受传参顺序限制，需要手动对齐签名
    return getPcAPI(SocialdynamicsApi).apiSocialdynamicsDataSearchbypositionGet(
        post.keywords,
        post.page,
        post.pageSize
    );
};
</script>

<style scoped lang="scss">
.map-over-view {
    width: 100%;
    height: 435px;
    padding: 16px;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    gap: 21px;
    margin-top: 24px;

    .left_map {
        width: 638px;
        height: 100%;
        background: #000036;
    }

    .right_postions_list {
        flex: 1;
        height: 100%;

        .position-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }

        .salary {
            font-family: Microsoft YaHei, Microsoft YaHei;
            color: #FF6666;
        }

        :deep(.el-table .even-row) {
            --el-table-tr-bg-color: #fff;

            .el-table__cell {
                padding: 0;
                border: none;
            }
        }

        :deep(.el-table .odd-row) {
            --el-table-tr-bg-color: #f5f9fc;

            .el-table__cell {
                padding: 0;
                border: none;
            }
        }

        :deep(.el-table .header-row) {
            --el-table-header-bg-color: #007bf6;

            .cell {
                color: #fff;
            }

            .el-table__cell {
                // padding:4px 0;
            }
        }

        :deep(.el-table__header-wrapper) {
            border-radius: 4px 4px 0 0 !important;
        }

        :deep(.el-table__inner-wrapper:before) {
            display: none;
        }
    }
}
</style>
