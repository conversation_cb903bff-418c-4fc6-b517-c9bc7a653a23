export const initMapvgl = (mapvgl: any, map: any) => {
  let view = new mapvgl.View({
    map: map,
  });
  return view;
};

export const initIconLayerMill = (mapvgl: any, view: any ,event:{onClick:(e:any)=>void,onDblClick:(e:any)=>void,onRightClick:(e:any)=>void}) => {
  var layer = new mapvgl.IconLayer({
    width: 20,
    height: 20,
    offset: [0, 0],
    opacity: 0.8,
    icon: "https://image.gxrc.com/gxrcsite/vip/icon_position2.png",
    enablePicked: true, // 是否可以拾取
    selectedIndex: -1, // 选中项
    selectedColor: "#ff0000", // 选中项颜色
    autoSelect: true, // 根据鼠标位置来自动设置选中项
    onClick: (e) => {
      // 点击事件
      event.onClick(e);
    },
    onDblClick: (e) => {
      event.onDblClick(e);
    },
    onRightClick: (e) => {
      event.onRightClick(e);
    },
  });
  view.addLayer(layer);
  return layer;
};

export const setIconLayer = (layer:any,data:any) => {
    layer.setData(data)
}

export const clearIconLayer = (layer:any) => {
    layer.clear()
}


export const getMockData = (mapv:any) => {
    var data = [];

    var citys = [
      "南宁",
      "柳州",
      "桂林",
      "梧州",
      "北海",
      "防城港",
      "钦州",
      "贵港",
      "玉林",
      "百色",
      "贺州",
      "河池",
      "来宾",
      "崇左",

    ];

    // const arr = [
    //   {
    //     geometry: {
    //       type: "Point",
    //       data: {
    //         name: 123,
    //       },
    //       coordinates: [125.029106094463, 42.44239205072378],
    //     },
    //   },
    // ];

    var randomCount = 1000;

    // 构造数据
    while (randomCount--) {
      var cityCenter = mapv.utilCityCenter.getCenterByCityName(
        citys[parseInt(Math.random() * citys.length, 10)]
      );
      data.push({
        geometry: {
          type: "Point",
          data: {
            name: 123,
          },
          coordinates: [
            cityCenter.lng - 2 + Math.random() * 4,
            cityCenter.lat - 2 + Math.random() * 4,
          ],
        },
      });
    } 
 return new Promise((resolve,reject)=>{
    setTimeout(()=>{
        resolve(data)
    },3000)
  })
}