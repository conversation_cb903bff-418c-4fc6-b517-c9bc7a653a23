export const initMap = (BMapGL:any, options:{id:string}) => {

    var map = new BMapGL.Map(options.id, {
        restrictCenter: false,
        style: { styleJson: [] }
    });
    map.enableKeyboard();
    map.enableScrollWheelZoom();
    map.enableInertialDragging();
    map.enableContinuousZoom();

    map.setDisplayOptions({
        indoor: false,
        poi: true,
        skyColors:  [
            // 地面颜色
            'rgba(226, 237, 248, 0)',
            // 天空颜色
            'rgba(186, 211, 252, 1)'
        ]
    });
    map.addControl(new BMapGL.ZoomControl()); //添加控件

    map.addControl(new BMapGL.ScaleControl()); //添加控件
    map.setTilt(0);
    map.setHeading(0);
    return map;
}

export const setCenter = (BMapGL:any, map:any, options:{center:number[],zoom:number}) => {
    let center = new BMapGL.Point(options.center[0], options.center[1])
    map.centerAndZoom(center, options.zoom);

    var pt = center;
    var mk = new BMapGL.Marker(pt);
    map.addOverlay(mk);
}


export const setCircle = (BMapGL:any, map:any, options:{center:number[],radius:number,zoom:number}) => {
    setCenter(BMapGL, map, options);
    const circleInstance = new BMapGL.Circle(
        new BMapGL.Point(options.center[0], options.center[1]),
        options.radius,
        {
            strokeColor: "#a3b7ff",
            strokeWeight: 1,
            strokeOpacity: 0.8,
            fillColor: "#0b83fb",
            fillOpacity: 0.1,
        }
    );
    map.addOverlay(circleInstance);
}


export const clearCircle = (map:any) => {
    map.clearOverlays();
}