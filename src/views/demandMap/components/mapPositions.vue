<template>
    <div class="map-positions">
        <div class="tips">
            温馨提示：输入中心地标后，可按范围大小查询职位，但工作区域选择需要在所选范围内。
        </div>
        <div class="top_map_select">
            <div class="left">
                <mapSelectBox />
            </div>
            <div class="right">
                <glMap />
            </div>
        </div>
        <div class="bootom_table">
            <Pagination :fixedPageSize="10" :changePageToTop="false"
                :showAffix="false" :allFn="false" ref="paginationRef" :ApiFunction="apiFunction" :option="post">
                <template #other="{ allData }">
                    <div class="search_result">
                        <p>搜索结果：（企业数：<span>{{ allData?.totalCount }}</span> 家，职位数：<span>{{ allData?.totalCount }}</span>
                            个，招聘人数：<span>{{ allData?.totalCount }}</span> 人）</p>
                    </div>
                </template>
                <template #default="{ tableData, loading, empty }">
                    <div class="position_list">

                        <div class="position_item" v-for="item in tableData as SocialDynamicsInfoOutput[]" :key="item.positionID">
                              <PositionCard  />
                        </div>
                    </div>
                  

                </template>
            </Pagination>
        </div>
    </div>
</template>

<script setup lang="ts">
import mapSelectBox from './mapSelectBox.vue';
import glMap from './glMap.vue';
import Pagination from "@/components/table/Pagination.vue";
import { getPcAPI } from "@/utils/axios-utils";
import { SocialdynamicsApi } from "@/api-services/apis/socialdynamics-api";
import { SocialDynamicsInfoOutput } from "@/api-services/models/social-dynamics-info-output";
import PositionCard from './positionCard.vue';

const post = ref({
    keywords: '',
})


const apiFunction = (post: any) => {
    //get方法受传参顺序限制，需要手动对齐签名
    return getPcAPI(SocialdynamicsApi).apiSocialdynamicsDataSearchbypositionGet(
        post.keywords,
        post.page,
        post.pageSize
    );
};
</script>

<style scoped lang="scss">
.map-positions {
    padding: 16px;
    background: #fff;
    margin-top: 24px;

    .bootom_table {
        width: 100%;
        margin-top: 24px;
        .search_result{
            width: 100%;
height: 36px;
background: #EBF1F5;
border-radius: 0px 0px 0px 0px;
padding: 0 27px;
p{
font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 14px;
color: #333333;
line-height: 36px;
span{
    color: #2878FF;
    font-weight: bold;
}
}
        }
    }

    .top_map_select {
        margin-top: 12px;
        display: flex;
        gap: 17px;
        height: 612px;

        .left {
            width: 302px;
            height: 100%;
            background: skyblue;
        }

        .right {
            flex: 1;
            height: 100%;
            border-radius: 0px 0px 0px 0px;
        }




    }

    .tips {

        width: 100%;
        height: 37px;
        background: #FFF8E4;
        border-radius: 8px 8px 8px 8px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #BE5F17;
        line-height: 37px;
        margin-bottom: 10px;
    }
}
</style>