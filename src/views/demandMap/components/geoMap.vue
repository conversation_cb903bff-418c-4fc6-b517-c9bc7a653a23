<template>
    <div class="geo-map">

        <div ref="mapDom" id="main-map"></div>
    </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted } from "vue";
import geoJson from "../json/map.gx.json";
import { getOption, getRegionData, mock } from "./geoMapOption";

import { ref } from "vue";

const mapDom = ref<HTMLElement | null>(null);

const myChartRef = ref<any>(null);

const regionData = getRegionData(geoJson);


onMounted(() => {
    initMap()
});

const initMap = async () => {
    if (!mapDom.value) return;

    const chartDom = mapDom.value
    const myChart = echarts.init(chartDom);
    myChart.showLoading();
    const data = await statisticalData()
    const option = getOption(geoJson, data);
    echarts.registerMap("iceland", geoJson);
    myChart.hideLoading();
    myChart.setOption(option);
    option && myChart.setOption(option);
    myChartRef.value = myChart;
    registerEvent(myChart)
}

const statisticalData = () => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve(mock)
        }, 1000)
    })
}

const currentSelectRegionName = ref('')
function clearSelect() {
    myChartRef.value.dispatchAction({
        type: "geoUnSelect",
        geoIndex: 0,
        name: currentSelectRegionName.value,
    });
    currentSelectRegionName.value = ''
}

let time:any = null
const registerEvent = (myChart) => {
    myChart.on("click", function (params) {
        console.log(params);
        currentSelectRegionName.value = params.name
    });
    myChart.on("mousemove", function (params) {
        stopRotate(myChart)
    });
    myChart.on("globalout", function (params) {
        rotate(myChart)
    });
    rotate(myChart)
}

const rotateCurrentRegionName = ref('')
const rotate = async (myChart) => {
    if(time) return
    let i = regionData.regionList.findIndex(item => {
        return item.name === rotateCurrentRegionName.value
    })
    if(i === -1){
        i = 0
        rotateCurrentRegionName.value = regionData.regionList[i].name
    }
    time = setInterval(() => {
        clearRotate(myChart)
        const currentRegion = regionData.regionList[i]
        rotateCurrentRegionName.value = currentRegion.name
        myChart.dispatchAction({
            type: 'highlight',
            geoIndex: 0,
            name: rotateCurrentRegionName.value,
        })
        myChart.dispatchAction({
            type: 'showTip',
            geoIndex: 0,
            name: rotateCurrentRegionName.value,
            position:'left'
        })
        i++
        if(i === regionData.regionList.length) i = 0
    }, 3000);
}

const stopRotate = (myChart) => {
    if(!time) return
    clearInterval(time)
    clearRotate(myChart)
    time = null
}
const clearRotate = (myChart) => {
    if(!rotateCurrentRegionName.value) return
    myChart.dispatchAction({
        type: 'downplay',
        geoIndex: 0,
        name: rotateCurrentRegionName.value,
    })
    myChart.dispatchAction({
        type: 'hideTip',
        geoIndex: 0,
        name: rotateCurrentRegionName.value,
    })
}


defineExpose({
    clearSelect
})

</script>

<style lang="scss" scoped>
.geo-map {
    width: 100%;
    height:100%;
    #main-map {
        width: 100%;
        height:100%;
    }

}
</style>
<style lang="scss">
.tooltip_8_15 {
    width: 250px;
    height: 160px;
    background-color: #023a83f3;
    box-sizing: border-box;
    border-radius: 10px;
    border: 1px solid #0e71f3;
    padding: 10px;

    .title {
        font-size: 16px;
        color: #fff;
        font-weight: bold;
    }

    .line {
        width: 100%;
        height: 1px;
        background-color: #d8d8d8;
        margin: 10px 0;
    }

    .content {
        .content-item {
            display: flex;
            align-items: center;

            .dot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: #0e71f3;
                margin-right: 10px;
            }

            .text {
                font-size: 16px;
                color: #fff;

                .text-value {
                    color: #0e71f3;
                    margin: 0 5px;
                }
            }
        }

        .zws {
            .dot {
                background-color: #e9131d;
            }

            .text {
                .text-value {
                    color: #e9131d;
                }
            }
        }

        .zprs {
            .dot {
                background-color: #0ad457;
            }

            .text {
                .text-value {
                    color: #0ad457;
                }
            }
        }

        .jls {
            .dot {
                background-color: #c123ff;
            }

            .text {
                .text-value {
                    color: #c123ff;
                }
            }
        }
    }
}
</style>