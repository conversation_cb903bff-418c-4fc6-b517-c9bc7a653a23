<template>
    <div class="position-card">
        <div class="top">
            <div class="top_left">
                <div class="name">
                    <div class="text">
                        <p>{{ position.positionName }}</p>
                    </div>
                    <div class="location">{{ position.workPlace }}</div>
                </div>
                <div class="other_info">
                    <div class="salary">{{ position.payPackage }}</div>
                    <div class="work_age">{{ position.workAge }}</div>
                    <div class="degree">{{ position.degreeName }}</div>
                    <div class="online_status" v-if="position.isOnline">
                        <div class="dot"></div>
                        在线
                    </div>
                    <div class="chat_btn">
                        <el-icon color="#F5B318">
                            <ChatLineRound />
                        </el-icon>
                        在线直聊
                    </div>
                </div>
            </div>
            <div class="top_right">
                <div class="company_info">
                    <div class="company_logo">
                        <img :src="position.enterpriseLogoUrl" alt="company_logo" />
                    </div>
                    <div class="company_content">
                        <div class="company_name">{{ position.enterpriseName }}</div>
                        <div class="company_other_info">
                            <div class="company_tag" v-html="setLine(
                                { type: 'html', tag: 'span', join: ' | ' },
                                '国有单位',
                                '51-100人',
                                '保险'
                            )
                                "></div>
                        </div>
                    </div>
                </div>
                <div class="company_redundant_info">
                    <div class="date">2024-04-08</div>
                    <div class="park_info">
                        <div class="park_box">
                            <el-icon>
                                <Location />
                            </el-icon>
                            xxxx园区
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom">
            <div class="bottom_left">
                <div class="bottom_left_item" v-html="setLine(
                    {
                        type: 'html',
                        tag: 'span',
                        join: `<span class='line'>|</span>`,
                    },
                    '全职',
                    '销售管理',
                    '业务拓展',
                    '新开店管理商铺租赁'
                )
                    "></div>
            </div>
            <div class="bottom_right">
                <div class="bottom_right_item" v-html="setLine(
                    { type: 'string', tag: '', join: ' , ' },
                    '周末双休',
                    '五险',
                    '住房公积金',
                    '带薪年假',
                    '年终奖金'
                )
                    "></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ChatLineRound, Location } from "@element-plus/icons-vue";
// 职位数据接口
interface PositionData {
    positionName?: string;
    workPlace?: string;
    enterpriseName?: string;
    enterpriseLogoUrl?: string;
    publishTime?: Date | string;
    payPackage?: string;
    workAge?: string;
    degreeName?: string;
    positionKeywords?: string[];
    positionWelfare?: string[];
    // 添加更多字段支持
    isOnline?: boolean;
    chatStatus?: number;
    emergencyRrecruitmentFlag?: boolean;
    isReceiveGraduate?: boolean;
}

// Props定义
interface Props {
    position: PositionData;
}

const props = withDefaults(defineProps<Props>(), {
    position: () => ({
        positionName: "科技事务管理部部长啊大苏打实打实大苏打",
        workPlace: "【南宁·青秀区·民族大道】",
        enterpriseName: "中国人寿保险股份有限公司南宁分公司明秀",
        enterpriseLogoUrl:
            "https://avatar.gxrc.com/photo/2025/6a1f6c22-e3f6-46a7-9b97-ef4a56bf4c85.jpg",
        publishTime: "2024-04-08",
        payPackage: "6-8K",
        workAge: "3-6年",
        degreeName: "本科",
        positionKeywords: ["全职", "销售管理", "业务拓展", "新开店管理商铺租赁"],
        positionWelfare: ["周末双休", "五险", "住房公积金", "带薪年假", "年终奖金"],
        isOnline: true,
        chatStatus: 2,
        emergencyRrecruitmentFlag: true,
        isReceiveGraduate: false,
    }),
});

function setLine(
    type = { type: "string", tag: "", join: " | " },
    ...str: string[]
) {
    let arr = str.filter((item) => item && item.trim());

    if (type.type == "string") {
        return arr.join(type.join);
    } else {
        return "<" + type.tag + ">" + arr.join(type.join) + "</" + type.tag + ">";
    }
}
</script>

<style scoped lang="scss">
.position-card {
    width: 100%;
    padding: 24px 0 0 0;
    transition: all 0.3s ease;

    .bottom {
        width: 100%;
        height: 37px;
        background: #f6f9ff;
        border-radius: 0px 0px 8px 8px;
        margin-top: 27px;
        display: flex;
        gap: 170px;
        padding: 0 20px;
        box-sizing: border-box;
        align-items: center;

        .bottom_left {
            width: 387px;

            .bottom_left_item {
                font-family: Microsoft YaHei, Microsoft YaHei;
                font-weight: 400;
                font-size: 14px;
                color: #666666;

                :deep(.line) {
                    margin: 0 7px;
                    color: #e1e1e1;
                }
            }
        }

        .bottom_right {
            .bottom_right_item {
                font-family: Microsoft YaHei, Microsoft YaHei;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
            }
        }
    }

    .top {
        display: flex;
        gap: 170px;
        padding: 0 20px;

        .top_right {
            display: flex;
            gap: 30px;

            .company_redundant_info {
                flex: 1;
                gap: 30px;
                display: flex;

                .date {
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                }

                .park_info {
                    display: flex;
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                    align-items: flex-start;

                    .park_box {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 4px;
                    }
                }
            }

            .company_info {
                width: 330px;
                display: flex;
                gap: 9px;

                .company_logo {
                    width: 44px;
                    height: 44px;
                    border-radius: 4px 4px 4px 4px;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .company_content {
                    flex: 1;
                    width: 0;

                    .company_name {
                        font-family: Microsoft YaHei, Microsoft YaHei;
                        font-weight: bold;
                        font-size: 14px;
                        color: #333333;
                        text-align: left;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .company_other_info {
                        margin-top: 7px;

                        .company_tag {
                            font-family: Microsoft YaHei, Microsoft YaHei;
                            font-weight: 400;
                            font-size: 12px;
                            color: #8d97ac;
                        }
                    }
                }
            }
        }

        .top_left {
            width: 387px;

            .other_info {
                display: flex;
                gap: 12px;
                margin-top: 4px;

                .salary {
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: bold;
                    font-size: 16px;
                    color: #ff6666;
                }

                .work_age {
                    padding: 2px 6px;
                    background: #f5f6fb;
                    border-radius: 2px 2px 2px 2px;
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 12px;
                    color: #8d97ac;
                    text-align: center;
                }

                .degree {
                    padding: 2px 6px;
                    background: #f5f6fb;
                    border-radius: 2px 2px 2px 2px;
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 12px;
                    color: #8d97ac;
                    text-align: center;
                }

                .online_status {
                    padding: 2px 6px;
                    background: #effcff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 4px;

                    .dot {
                        width: 5px;
                        height: 5px;
                        background: #3b86f6;
                        border-radius: 50%;
                    }

                    font-family: Microsoft YaHei,
                    Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    color: #3b86f6;
                }

                .chat_btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 4px;
                    padding: 2px 6px;
                    background: #fcf5e2;
                    border-radius: 2px 2px 2px 2px;
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    color: #f5b318;
                    cursor: pointer;
                }
            }

            .name {
                display: flex;
                gap: 10px;

                .text {
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 16px;
                    // flex: 1;
                    overflow: hidden;

                    p {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-family: Microsoft YaHei, Microsoft YaHei;
                        font-weight: 400;
                        font-size: 18px;
                        color: #333333;
                    }
                }

                .location {
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 14px;
                    white-space: nowrap;
                    font-family: Microsoft YaHei, Microsoft YaHei;
                    font-weight: 400;
                    font-size: 18px;
                    color: #333333;
                }
            }
        }
    }
}

.position-card:hover {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px) scale(1.02);
}
</style>
