<template>
  <div class="map-select-box">
    <div class="title">快速筛选</div>
    <div class="content">
      <p class="tips">按输入的区域搜索</p>

      <el-form :label-position="'top'" label-width="auto" :model="formLabelAlign" style="width: 100%">
        <el-form-item label="中心地标（输入后需在选项中选择）：">

          <el-autocomplete v-model="formLabelAlign.centerMarkText" :disabled="Boolean(formLabelAlign.workArea)"
            :fetch-suggestions="querySearchAsync" placeholder="请输入地点" @select="handleSelect" />
        </el-form-item>
        <el-form-item label="搜索范围：">

          <el-select v-model="formLabelAlign.range" placeholder="请选择范围" style="width: 100%">
            <el-option v-for="item in rangeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作区域：">
          <el-select v-model="formLabelAlign.workArea" :disabled="Boolean(formLabelAlign.centerMarkText)"
            placeholder="请选择工作区域" style="width: 100%">
            <el-option v-for="item in rangeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-model="formLabelAlign.keyword" placeholder="请输入单位或职位关键字" style="margin-top: 20px;" />
        </el-form-item>
        <el-form-item label="职业类别：">
          <div class="filter-select">
            <Cascader v-model="formLabelAlign.positionType" :clearVisible="true" :deepFlex="3"
              :options="positionTypeOptions" :valueId="valueId" :placeholderSelect="'请选择职业'" :asyncCommit="true"
              :placeholder="'选择职位'"  :multiple="true":label="label" :title="'职业类别'" :height="30" :children="children"
              :width="262" @changeValue="handlePositionChange" />
          </div>

        </el-form-item>
        <el-form-item label="行业类别：">
          <Cascader v-model="formLabelAlign.industryValue" :deepFlex="3" :options="industryOptions" :valueId="valueId"
            :placeholderSelect="'请选择行业'" :clearVisible="true" :asyncCommit="true" :placeholder="'行业类别'"  :multiple="true"
            :label="label" :title="'行业类别'" :children="children" :height="30"  :width="262"
            @changeValue="handleIndustryChange" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" style="width: 100%;">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Cascader } from 'gxrcw-ui'
import { reactive,onMounted,computed } from "vue";
import { useDictionaryStore } from '@/stores/dictionary'
const dictionaryStore = useDictionaryStore()

onMounted(() => {
  dictionaryStore.getIndustryOptions();
  dictionaryStore.getPositionTypeOptions();
});
const formLabelAlign = reactive({
  centerMarkText: "",
  range: 5,
  workArea: "",
  keyword: "",
  positionType:  [],
  industryValue: []
});
const industryOptions = computed(() => {
    return dictionaryStore.industryTreeOptions
})

const positionTypeOptions = computed(() => {
    return dictionaryStore.positionTypeOptions
})

const label = 'keywordName'
const children = 'children'
const valueId = 'keywordID'
const handlePositionChange = (value: any) => {
  // 模拟数据更新
}
const handleIndustryChange = (value: any) => {
  // 模拟数据更新
}
const querySearchAsync = async (queryString: string, cb: (arg: any) => void) => {
  console.log(queryString);
  await new Promise((resolve) => {
    setTimeout(() => {
      resolve(queryString);
    }, 1000);
  });
  cb([
    {
      value: "选项1",
      label: "选项1",
    },
    {
      value: "选项2",
      label: "选项2",
    },
    {
      value: "选项3",
      label: "选项3",
    },
  ]);
};

const handleSelect = (item: any) => {
  console.log(item);
};

const rangeOptions = [
  {
    value: 5,
    label: "5公里",
  },
  {
    value: 10,
    label: "10公里",
  },
  {
    value: 20,
    label: "20公里",
  },
  {
    value: 50,
    label: "50公里",
  },
];
</script>

<style scoped lang="scss">
.map-select-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .filter-select {
    :deep(.gxrc-input) {

      input::placeholder {
        color: #a8abb2;
      }

      // input {
      //   color: #333;
      //   font-size: 14px;
      // }

      // .gxrc-input__suffix {
      //   color: #666;
      // }
    }
  }

  .content {
    width: 100%;
    flex: 1;
    background: #ffffff;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #a2d1ff;
    box-sizing: border-box;
    border-top: none;
    padding: 19px 19px 15px 19px;

    .tips {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      text-align: left;
      margin: 0;
      margin-bottom: 15px;
    }
  }

  .title {
    width: 100%;
    height: 36px;
    background: linear-gradient(180deg, #007bf6 0%, #2a96ff 100%);
    border-radius: 8px 8px 0px 0px;

    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #ffffff;
    line-height: 36px;
    text-align: left;
    padding: 0 13px;
    box-sizing: border-box;
  }
}
</style>
