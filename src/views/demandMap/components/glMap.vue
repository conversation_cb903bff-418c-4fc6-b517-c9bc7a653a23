<template>
  <div class="gl-map-box" v-loading="loading">
    <div id="map_container"></div>
  </div>
</template>

<script setup lang="ts">
import { loadBaiduMap, loadMapVgl, loadMapV } from "@/utils/baiduMap";
import { ref } from "vue";
import { initMap, setCircle,clearCircle } from "./glOptions";
import { initMapvgl, initIconLayerMill, getMockData,setIconLayer,clearIconLayer } from "./glMapV";

const loading = ref(false);

onMounted(async () => {
  loading.value = true;
  let baiduSdk = await loadBaiduMap();
  if (baiduSdk && Object.keys(BMapGL).length > 0) {
    await loadMapV();
    await loadMapVgl();
    let map = initMap(BMapGL, {
      id: "map_container",
    });

    setCircle(BMapGL, map, {
      center: [108.51592855059243, 22.760248519767295],
      radius: 5 * 1000,
      zoom: 12,
    });

    const data = await getMockData(mapv);

    console.log(data);

    const view = initMapvgl(mapvgl, map);

    const layer = initIconLayerMill(mapvgl, view, {
      onClick: (e) => {
        console.log("click", e);
      },
      onDblClick: (e) => {
        console.log("double click", e);
      },
      onRightClick: (e) => {
        console.log("right click", e);
      },
    });

    setIconLayer(layer,data);

    layerRef.value = layer;
    mapRef.value = map;
    map.setDefaultCursor("default");
    loading.value = false;
  }
});

const layerRef = ref<any>(null);
const mapRef = ref<any>(null);

const test = () => {
  console.log("test");
  clearIconLayer(layerRef.value)
  clearCircle(mapRef.value)
}
</script>

<style scoped lang="scss">
.gl-map-box {
  width: 100%;
  height: 100%;

  #map_container {
    width: 100%;
    height: 100%;
    margin: 0;
  }
}
</style>
