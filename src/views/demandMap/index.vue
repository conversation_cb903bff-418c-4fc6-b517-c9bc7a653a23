<template>
  <div class="demand-map">
    <div class="container-lg">
      <div class="geo-map">
        <MapOverView />
      </div>
      <div class="gl-map">
        <MapPositions />
      </div>
 
   

    </div>
  </div>
</template>

<script setup lang="ts">
import MapOverView from './components/mapOverView.vue';
import GlMap from './components/glMap.vue';
import MapPositions from './components/mapPositions.vue';
</script>

<style scoped lang="scss">
.gl-map {
  
}
.geo-map{
  
}
</style>