<template>
  <div class="recruitment-detail-page">
    <div class="container w-1200px mx-auto">
      <!-- 信息模块 -->
      <div class="info-section">
        <div class="info-container">
          <!-- 右上角参会单位 -->
          <div class="participants-badge">
            <div class="badge-left">参会单位</div>
            <div class="badge-right"> <span>{{ recruitmentDetail.participantCount }}</span> 家</div>
          </div>
          
          <!-- 主要信息 -->
          <div class="main-info">
            <h1 class="title">{{ recruitmentDetail.title }}</h1>
            
            <div class="info-grid">
              <div class="info-item">
                <span class="label">召开时间：</span>
                <span class="value">{{ recruitmentDetail.timeRange }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">场所：</span>
                <span class="value">{{ recruitmentDetail.venue }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">主办单位：</span>
                <span class="value">{{ recruitmentDetail.organizer }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">承办单位：</span>
                <span class="value">{{ recruitmentDetail.coOrganizer }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">协办单位：</span>
                <span class="value">{{ recruitmentDetail.supporter }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ recruitmentDetail.contactPhone }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细文章模块 -->
      <div class="detail-section">
        <!-- 左侧内容区 -->
        <div class="left-content">
          <!-- Tab菜单 -->
          <div class="tab-menu">
            <div 
              class="tab-item"
              :class="{ active: activeTab === 'description' }"
              @click="switchTab('description')"
            >
              招聘会说明
            </div>
            <div 
              class="tab-item"
              :class="{ active: activeTab === 'participants' }"
              @click="switchTab('participants')"
            >
              参会单位
            </div>
          </div>
          
          <!-- 内容区 -->
          <div class="content-area">
            <!-- 招聘会说明 -->
            <div v-if="activeTab === 'description'" class="description-content">
              <div v-html="recruitmentDetail.description"></div>
            </div>
            
            <!-- 参会单位 -->
            <div v-if="activeTab === 'participants'" class="participants-content">
              <div class="company-grid">
                <div 
                  v-for="(company, index) in displayedCompanies" 
                  :key="company.id"
                  class="company-card"
                >
                  <div class="company-name" @click="goToCompanyDetail(company)">{{ company.name }}</div>
                  <div class="position-count">
                    在招职位<span class="count">{{ company.positionCount }}</span>个
                  </div>
                  <div class="position-tags">
                    <span 
                      v-for="position in company.positions.slice(0, 3)" 
                      :key="position"
                      class="position-tag"
                      @click="goToPositionDetail(company, position)"
                    >
                      {{ position }}
                    </span>
                    <span 
                      v-if="company.positions.length > 3"
                      class="more-positions-btn"
                      @click="goToCompanyDetail(company)"
                    >
                      ... 更多
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- 展开按钮 -->
              <div v-if="!showAllCompanies && participantCompanies.length > 8" class="expand-btn-wrapper">
                <span class="expand-btn" @click="toggleShowAll">展开</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧其他招聘会 -->
        <div class="right-sidebar">
          <div class="sidebar-header">
            <h3>本周其他招聘会</h3>
          </div>
          
          <div class="other-fairs-list">
            <div 
              v-for="item in otherRecruitmentFairs" 
              :key="item.id"
              class="fair-item"
            >
              <!-- 左侧日期 -->
              <div class="date-section">
                <div class="date-top">
                  <el-icon class="date-icon"><Calendar /></el-icon>
                  <span class="date-text">{{ formatDate(item.startDate) }}</span>
                </div>
                <div class="date-bottom">
                  <span class="weekday">{{ formatWeekday(item.startDate) }}</span>
                </div>
              </div>
              
              <!-- 右侧信息 -->
              <div class="fair-info">
                <div class="fair-title">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Calendar } from '@element-plus/icons-vue'
import { useCityStore } from '@/stores/cityStore'

const route = useRoute()
const activeTab = ref('description')
const showAllCompanies = ref(false)
const router = useRouter()
const cityStore = useCityStore()

// 招聘会详情数据
const recruitmentDetail = ref({
  id: 1,
  title: '攻坚促就业 奋进创未来——2025年广西高校毕业生等青年就业服务攻坚行动网络招聘会',
  timeRange: '2025-08-01至 2025-08-15 09：00 - 12：00',
  venue: '隆林各族自治县藏城区社区委员会会场',
  organizer: '隆林各族自治县人力资源和社会保障局、隆林各族自治县对口协作服务中心',
  coOrganizer: '隆林各族自治县发展和改革局隆林各族自治县工商业联合会等',
  supporter: '隆林壮族合作人力资源开发有限公司、隆林各族自治县藏城新区社区居民委员会',
  contactPhone: '0771-3397612/3397615',
  participantCount: 58,
  description: `
    <p>欢迎大家积极报名参会~~谢谢！</p>
    <p>本次招聘会是为了促进高校毕业生就业，搭建用人单位与求职者的交流平台。</p>
    <h3>招聘会特色：</h3>
    <ul>
      <li>汇聚优质企业，提供多样化岗位选择</li>
      <li>现场面试，高效便捷</li>
      <li>政策咨询，就业指导</li>
      <li>免费参与，服务贴心</li>
    </ul>
    <p>期待您的参与！</p>
  `
})

// 参会单位数据
const participantCompanies = ref([
  {
    id: 1,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理', '客户经理']
  },
  {
    id: 2,
    name: '中国人寿保险股份有限公司南宁市会展路支公司中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理','行政主管', '行政助理']
  },
  {
    id: 3,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 4,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 5,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 6,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 7,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 8,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 9,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  },
  {
    id: 10,
    name: '中国人寿保险股份有限公司南宁市会展路支公司',
    positionCount: 4,
    positions: ['市场营销', '团队主管', '行政主管', '行政助理']
  }
])

// 其他招聘会数据
const otherRecruitmentFairs = ref([
  {
    id: 2,
    title: '攻坚促就业 奋进创未来——2025年广西高校毕业生等青年就业服务攻坚行动网络招聘会',
    startDate: '2025-07-24'
  },
  {
    id: 3,
    title: '广西财经学院大数据与人工智能学院2025届高校未来职业生线上双选会',
    startDate: '2025-07-25'
  },
  {
    id: 4,
    title: '攻坚促就业 奋进创未来——2025年广西高校毕业生等青年就业服务攻坚行动网络招聘会',
    startDate: '2025-07-26'
  }
])

// 显示的公司列表
const displayedCompanies = computed(() => {
  return showAllCompanies.value ? participantCompanies.value : participantCompanies.value.slice(0, 8)
})

// Tab切换
const switchTab = (tab) => {
  activeTab.value = tab
}

// 展开/收起公司列表
const toggleShowAll = () => {
  showAllCompanies.value = !showAllCompanies.value
}

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${month}-${day}`
}

// 格式化星期
const formatWeekday = (dateStr) => {
  const date = new Date(dateStr)
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekdays[date.getDay()]
}

// 跳转到企业详情页
const goToCompanyDetail = (company) => {
  // 使用项目的多城市路由系统跳转到企业详情页
  const companyDetailPath = cityStore.getCityPagePath(`company/${company.id}`)
  console.log('跳转到企业详情页:', company.name, companyDetailPath)
  router.push(companyDetailPath)
}

// 跳转到职位详情页
const goToPositionDetail = (company, position) => {
  // 这里需要根据实际情况构建职位详情页路由
  // 如果有独立的职位详情页，可以跳转到职位详情
  // 如果没有，可以跳转到企业详情页并高亮对应职位
  console.log('跳转到职位详情页:', company.name, position)
  
  // 方案1: 跳转到职位详情页（如果存在）
  // const positionDetailPath = cityStore.getCityPagePath(`jobDetail/${positionId}`)
  // router.push(positionDetailPath)
  
  // 方案2: 跳转到企业详情页（目前采用这种方案）
  const companyDetailPath = cityStore.getCityPagePath(`company/${company.id}`)
  router.push(companyDetailPath)
}

onMounted(() => {
  // 根据路由参数获取招聘会详情
  const fairId = route.params.id
  console.log('招聘会详情页已加载，ID:', fairId)
})
</script>

<style scoped lang="scss">
.recruitment-detail-page {
  padding: 20px 0;
  min-height: 100vh;
  background: #f5f5f5;
}

/* 信息模块 */
.info-section {
  margin-bottom: 20px;
}

.info-container {
  background: linear-gradient(135deg, #FFFFFF 0%, #EEFFF9 50%, #DEF7FF 100%);
  border-radius: 8px;
  padding: 40px;
  position: relative;
  min-height: 200px;
}

.participants-badge {
  position: absolute;
  top: 35px;
  right: 39px;
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge-left {
  background: linear-gradient(135deg, #00EBC3, #00B0FF);
  color: white;
  padding: 8px 16px;
  font-size: 14px;
  width: 100px;
  text-align: center;
  line-height: 21px;
}

.badge-right {
  background: white;
  width: 77px;
  height: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
  span{
    color: #2878FF;
  font-size: 24px;
  font-weight: bold;
  padding-right: 3px;
  }
}

.main-info {
  max-width: 800px;
}

.title {
  font-size: 20px;
  color: #000000;
  font-weight: bold;
  margin: 0 0 30px 0;
  line-height: 1.4;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  
  .label {
    font-size: 14px;
    color: #989898;
    min-width: 80px;
    flex-shrink: 0;
  }
  
  .value {
    font-size: 14px;
    color: #333333;
    flex: 1;
  }
}

/* 详细文章模块 */
.detail-section {
  display: flex;
  gap: 20px;
}

.left-content {
  width: 856px;
  background: white;
  border-radius: 8px;
  min-height: 710px;
  overflow: hidden;
}

.right-sidebar {
  width: 325px;
  background: white;
  border-radius: 8px;
  min-height: 710px;
}

/* Tab菜单 */
.tab-menu {
  height: 65px;
  display: flex;
  border-bottom: 1px solid #DFDFDF;
  
  .tab-item {
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #333333;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    
    &:hover {
      color: #0192FB;
    }
    
    &.active {
      color: #0192FB;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 3px;
        background: linear-gradient(90deg, #00EBC3, #00B0FF);
      }
    }
  }
}

/* 内容区 */
.content-area {
 
}

.description-content {
  padding: 30px;
  :deep(p) {
    margin-bottom: 16px;
    line-height: 1.6;
    color: #333;
  }
  
  :deep(h3) {
    margin: 20px 0 12px 0;
    color: #333;
    font-size: 16px;
  }
  
  :deep(ul) {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.6;
      color: #333;
    }
  }
}

.participants-content {
  .company-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px; 
    margin: 16px 16px;
  }
  
  .company-card {
   
    height: 129px;
    padding: 20px;
    border: 1px solid #E9E9E9;
    border-radius: 8px;
    
  }
  
  .company-name {
    font-size: 16px;
    color: #333333;
    font-weight: 500;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 350px;
    cursor: pointer;
    transition: color 0.3s ease;
    
    &:hover {
      color: #2878FF;
    }
  }
  
  .position-count {
    padding: 5px 0 10px 0;
    font-size: 14px;
    color: #FF6666;
  }
  
  .position-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .position-tag {
    padding: 7px 13px;
    background: white;
    color: #2878FF;
    font-size: 14px;
    border: 1px solid #2878FF;
    border-radius: 8px;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #2878FF;
      color: white;
    }
  }
  
  .more-positions-btn {
    color:#2878FF;
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.3s ease;
    line-height: 36px;
  }

  
  .expand-btn-wrapper {
    text-align: right;
    padding: 0 30px;
    .expand-btn {
      color: #2878FF;
      font-size: 14px;
      cursor: pointer;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* 右侧边栏 */
.sidebar-header {
  height: 65px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  border-bottom: 1px solid #DFDFDF;
  
  h3 {
    font-size: 16px;
    color: #333333;
    font-weight: 500;
    margin: 0;
  }
}

.other-fairs-list {
  padding: 18px 24px;
}

.fair-item {
  display: flex;
  gap: 16px;
  padding: 18px 0;
  border-bottom: 1px solid #DFDFDF;
  
  &:last-child {
    border-bottom: none;
  }
}

.date-section {
  width: 60px;
  flex-shrink: 0;
  
  .date-top {
    height: 30px;
    background: #2DD2B6;
    border-radius: 6px 6px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    
    .date-icon {
      color: white;
      font-size: 12px;
    }
    
    .date-text {
      font-size: 12px;
      color: #FFFFFF;
      font-weight: 500;
    }
  }
  
  .date-bottom {
    height: 30px;
    background: white;
    border: 1px solid #2DD2B6;
    border-top: none;
    border-radius: 0 0 6px 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .weekday {
      font-size: 12px;
      color: #666666;
    }
  }
}

.fair-info {
  flex: 1;
  
  .fair-title {
    font-size: 14px;
    color: #333333;
    line-height: 1.4;
  }
}
</style> 