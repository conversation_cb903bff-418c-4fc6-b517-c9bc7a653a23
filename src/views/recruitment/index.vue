<template>
  <div class="recruitment-fair-page">
    <div class="container w-1200px mx-auto px-20px">
      <!-- Tab切换菜单 -->
      <div class="tab-menu-section">
        <div class="tab-menu">
          <div 
            class="tab-item"
            :class="{ active: activeTab === 'available' }"
            @click="switchTab('available')"
          >
            可预定的招聘会
          </div>
          <div 
            class="tab-item"
            :class="{ active: activeTab === 'booked' }"
            @click="switchTab('booked')"
          >
            已预定的招聘会
          </div>
        </div>
      </div>

      <!-- 条件搜索模块 -->
      <div class="search-section">
        <div class="search-form">
          <!-- 第一行搜索条件 -->
          <div class="search-row-first">
            <el-form :model="searchForm" inline>
              <el-form-item>
                <el-select 
                  v-model="searchForm.type" 
                  placeholder="请选择招聘会类型" 
                  class="search-select"
                  clearable
                >
                  <el-option label="专场招聘会" value="专场招聘会" />
                  <el-option label="综合招聘会" value="综合招聘会" />
                  <el-option label="校园招聘会" value="校园招聘会" />
                  <el-option label="网络招聘会" value="网络招聘会" />
                </el-select>
              </el-form-item>
              
              <el-form-item>
                <el-date-picker
                  v-model="searchForm.date"
                  type="date"
                  placeholder="请选择召开日期"
                  class="search-date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              
              <el-form-item>
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入招聘会名称"
                  class="search-input"
                  clearable
                />
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  class="search-btn"
                  @click="handleSearch"
                  :icon="Search"
                >
                  搜索
                </el-button>
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  class="clear-btn"
                  @click="handleClear"
                >
                  清空
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 第二行地区选择 -->
          <div class="search-row-second">
            <div class="location-selector">
              <div class="location-label">
                <el-icon class="location-icon"><Location /></el-icon>
                <span>地区：</span>
              </div>
              <div class="location-tags">
                <div
                  v-for="city in cities"
                  :key="city.value"
                  class="city-tag"
                  :class="{ active: selectedCities.includes(city.value) }"
                  @click="toggleCity(city.value)"
                >
                  {{ city.label }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表模块 -->
      <div class="list-section">
        <!-- 招聘会列表 -->
        <div class="fair-list">
          <div 
            v-for="item in currentPageData" 
            :key="item.id" 
            class="fair-item"
          >
            <!-- 左侧日期 -->
            <div class="date-section">
              <div class="date-top">
                <el-icon class="date-icon"><Calendar /></el-icon>
                <span class="date-text">{{ formatDate(item.startDate) }}</span>
              </div>
              <div class="date-bottom">
                <span class="weekday">{{ formatWeekday(item.startDate) }}</span>
              </div>
            </div>
            
            <!-- 中间内容 -->
            <div class="content-section" @click="goToDetail(item)">
              <!-- 上部分 -->
              <div class="content-top">
                <span class="status-tag">{{ getStatusText(item.status) }}</span>
                <span class="type-tag">【{{ item.type }}】</span>
                <span class="title">{{ item.title }}</span>
              </div>
              
              <!-- 中部分 -->
              <div class="content-middle">
                <span class="label">举办时间：</span>
                <span class="value">{{ item.timeRange }}</span>
              </div>
              
              <!-- 下部分 -->
              <div class="content-bottom">
                <span class="label">举办场所：</span>
                <span class="value">{{ item.venue }}</span>
                <span class="label ml-20px">主办单位：</span>
                <span class="value">{{ item.organizer }}</span>
              </div>
            </div>
            
            <!-- 右侧操作 -->
            <div class="action-section">
              <div class="booth-info">
                <span class="remaining">剩余展位数量：</span>
                <div class="count">
                  <span class="remaining-count">{{ item.remainingBooths }}</span>
                  <span class="total-count">/{{ item.totalBooths }}</span>
                </div>
              </div>
              <el-button 
                class="action-btn"
                :class="getButtonClass(item.buttonStatus)"
                @click="handleAction(item)"
              >
                {{ getButtonText(item.buttonStatus) }}
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 分页组件 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="filteredData.length"
            layout="prev, pager, next, sizes, total, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            prev-text="上一页"
            next-text="下一页"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Location, Calendar } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useCityStore } from '@/stores/cityStore'

// Tab状态
const activeTab = ref('available')
const router = useRouter()
const cityStore = useCityStore()

// 搜索表单
const searchForm = reactive({
  type: '',
  date: '',
  name: ''
})

// 地区数据
const cities = [
  { label: '不限', value: 'unlimited' },
  { label: '南宁市', value: 'nanning' },
  { label: '柳州市', value: 'liuzhou' },
  { label: '桂林市', value: 'guilin' },
  { label: '北海市', value: 'beihai' },
  { label: '梧州市', value: 'wuzhou' },
  { label: '防城港市', value: 'fangchenggang' },
  { label: '钦州市', value: 'qinzhou' },
  { label: '贵港市', value: 'guigang' },
  { label: '玉林市', value: 'yulin' },
  { label: '百色市', value: 'baise' },
  { label: '贺州市', value: 'hezhou' },
  { label: '河池市', value: 'hechi' },
  { label: '来宾市', value: 'laibin' },
  { label: '崇左市', value: 'chongzuo' }
]

// 选中的城市
const selectedCities = ref(['unlimited'])

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)

// 模拟数据
const mockData = ref([
  {
    id: 1,
    title: '2025年广西"就业暖心 · 桂在行动"专项服务活动',
    type: '专场招聘会',
    status: 'booking',
    startDate: '2025-07-23',
    timeRange: '2025-08-01至 2025-08-15 09：00 - 12：00',
    venue: '南宁市星光大道6号南宁市人才市场',
    organizer: '南宁市人才服务管理办公室',
    remainingBooths: 50,
    totalBooths: 60,
    buttonStatus: 1, // 1-我要定展 2-已预定待审核 3-预定审核未通过 4-已结束预定 5-我要定展(灰色)
    city: 'nanning'
  },
  {
    id: 2,
    title: '2025年春季大型综合招聘会',
    type: '综合招聘会',
    status: 'booking',
    startDate: '2025-08-15',
    timeRange: '2025-08-15至 2025-08-16 08：30 - 17：00',
    venue: '柳州市会展中心',
    organizer: '柳州市人力资源和社会保障局',
    remainingBooths: 30,
    totalBooths: 80,
    buttonStatus: 2,
    city: 'liuzhou'
  },
  {
    id: 3,
    title: '2025年高校毕业生专场招聘会',
    type: '校园招聘会',
    status: 'booking',
    startDate: '2025-08-20',
    timeRange: '2025-08-20至 2025-08-21 09：00 - 16：00',
    venue: '桂林市体育馆',
    organizer: '桂林市教育局',
    remainingBooths: 25,
    totalBooths: 50,
    buttonStatus: 3,
    city: 'guilin'
  },
  {
    id: 4,
    title: '2025年新能源汽车行业专场招聘会',
    type: '专场招聘会',
    status: 'booking',
    startDate: '2025-09-01',
    timeRange: '2025-09-01至 2025-09-02 09：00 - 17：00',
    venue: '北海市国际会展中心',
    organizer: '北海市工业和信息化局',
    remainingBooths: 40,
    totalBooths: 60,
    buttonStatus: 4,
    city: 'beihai'
  },
  {
    id: 5,
    title: '2025年电子商务行业招聘会',
    type: '网络招聘会',
    status: 'booking',
    startDate: '2025-09-10',
    timeRange: '2025-09-10至 2025-09-15 全天在线',
    venue: '线上平台',
    organizer: '广西电商协会',
    remainingBooths: 0,
    totalBooths: 100,
    buttonStatus: 5,
    city: 'unlimited'
  },
  {
    id: 6,
    title: '2025年金融行业专场招聘会',
    type: '专场招聘会',
    status: 'booking',
    startDate: '2025-08-05',
    timeRange: '2025-08-05至 2025-08-06 09：00 - 17：00',
    venue: '南宁市会展中心',
    organizer: '广西金融协会',
    remainingBooths: 35,
    totalBooths: 45,
    buttonStatus: 1,
    city: 'nanning'
  },
  {
    id: 7,
    title: '2025年医疗健康行业招聘会',
    type: '专场招聘会',
    status: 'booking',
    startDate: '2025-07-30',
    timeRange: '2025-07-30至 2025-07-31 08：30 - 17：30',
    venue: '梧州市体育中心',
    organizer: '广西医疗协会',
    remainingBooths: 20,
    totalBooths: 40,
    buttonStatus: 2,
    city: 'wuzhou'
  },
  {
    id: 8,
    title: '2025年教育培训行业招聘会',
    type: '校园招聘会',
    status: 'booking',
    startDate: '2025-08-25',
    timeRange: '2025-08-25至 2025-08-26 09：00 - 16：00',
    venue: '百色市教育园区',
    organizer: '广西教育厅',
    remainingBooths: 15,
    totalBooths: 30,
    buttonStatus: 3,
    city: 'baise'
  },
  {
    id: 9,
    title: '2025年科技创新企业专场招聘会',
    type: '专场招聘会',
    status: 'booking',
    startDate: '2025-08-28',
    timeRange: '2025-08-28至 2025-08-29 09：00 - 17：00',
    venue: '南宁市高新技术开发区',
    organizer: '南宁市科技局',
    remainingBooths: 12,
    totalBooths: 25,
    buttonStatus: 6,
    city: 'nanning'
  }
])

// 根据Tab和搜索条件过滤的数据
const filteredData = computed(() => {
  let data = mockData.value.filter(item => {
    // 根据Tab过滤
    if (activeTab.value === 'booked') {
      return [2, 3, 4, 6].includes(item.buttonStatus) // 已预定相关状态，包括预定成功
    } else {
      return [1, 5].includes(item.buttonStatus) // 可预定状态
    }
  })
  
  // 搜索条件过滤
  if (searchForm.type) {
    data = data.filter(item => item.type === searchForm.type)
  }
  
  if (searchForm.date) {
    data = data.filter(item => item.startDate >= searchForm.date)
  }
  
  if (searchForm.name) {
    data = data.filter(item => item.title.includes(searchForm.name))
  }
  
  // 地区过滤
  if (!selectedCities.value.includes('unlimited') && selectedCities.value.length > 0) {
    data = data.filter(item => selectedCities.value.includes(item.city))
  }
  
  return data
})

// 当前页数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// Tab切换
const switchTab = (tab) => {
  activeTab.value = tab
  currentPage.value = 1
}

// 城市选择
const toggleCity = (cityValue) => {
  if (cityValue === 'unlimited') {
    selectedCities.value = ['unlimited']
  } else {
    const index = selectedCities.value.indexOf('unlimited')
    if (index > -1) {
      selectedCities.value.splice(index, 1)
    }
    
    const cityIndex = selectedCities.value.indexOf(cityValue)
    if (cityIndex > -1) {
      selectedCities.value.splice(cityIndex, 1)
      if (selectedCities.value.length === 0) {
        selectedCities.value = ['unlimited']
      }
    } else {
      selectedCities.value.push(cityValue)
    }
  }
  currentPage.value = 1
}

// 搜索
const handleSearch = () => {
  console.log('搜索参数:', {
    tab: activeTab.value,
    type: searchForm.type,
    date: searchForm.date,
    name: searchForm.name,
    cities: selectedCities.value
  })
  currentPage.value = 1
  ElMessage.success('搜索完成')
}

// 清空
const handleClear = () => {
  searchForm.type = ''
  searchForm.date = ''
  searchForm.name = ''
  selectedCities.value = ['unlimited']
  currentPage.value = 1
  ElMessage.info('已清空搜索条件')
}

// 分页
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${month}-${day}`
}

// 格式化星期
const formatWeekday = (dateStr) => {
  const date = new Date(dateStr)
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekdays[date.getDay()]
}

// 获取状态文字
const getStatusText = (status) => {
  return status === 'booking' ? '预定中' : '已结束'
}

// 获取按钮文字
const getButtonText = (status) => {
  const textMap = {
    1: '我要定展',
    2: '已预定待审核',
    3: '预定审核未通过',
    4: '已结束预定',
    5: '我要定展',
    6: '预定成功'
  }
  return textMap[status] || '我要定展'
}

// 获取按钮样式类
const getButtonClass = (status) => {
  const classMap = {
    1: 'btn-primary',
    2: 'btn-pending',
    3: 'btn-rejected',
    4: 'btn-ended',
    5: 'btn-disabled',
    6: 'btn-success'
  }
  return classMap[status] || 'btn-primary'
}

// 按钮点击
const handleAction = (item) => {
  console.log('操作按钮点击:', item)
  ElMessage.info(`点击了"${getButtonText(item.buttonStatus)}"`)
}

// 跳转到详情页
const goToDetail = (item) => {
  console.log('跳转到详情页:', item)
  // 跳转到招聘会详情页面，支持多城市路由
  const detailPath = cityStore.getCityPagePath(`recruitment-fair/${item.id}`)
  router.push(detailPath)
}

onMounted(() => {
  console.log('招聘会页面已加载')
})
</script>

<style scoped lang="scss">
.recruitment-fair-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;
}

.container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* Tab切换菜单 */
.tab-menu-section {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -20px;
    right: -20px;
    height: 8px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);
    pointer-events: none;
  }
}

.tab-menu {
  display: flex;
  
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20px 0;
    font-size: 16px;
    color: #333333;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    
    &:hover {
      color: #2C71E1;
    }
    
    &.active {
      color: #2C71E1;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 180px;
        height: 3px;
        background: #2C71E1;
      }
    }
  }
}

/* 搜索模块 */
.search-section {
  padding: 20px;
}

.search-form {
  background: #F1F7FB;
  border-radius: 8px;
  padding: 18px 20px;
}

.search-row-first {
  margin-bottom: 16px;
  
  :deep(.el-form) {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
  }
  
  :deep(.el-form-item) {
    margin: 0;
  }
  
  .search-select,
  .search-input {
    width: 300px;
    
    :deep(.el-input__wrapper) {
      border: 1px solid #E5E5E5;
      border-radius: 4px;
    }
  }
  
  .search-date {
    width: 200px;
    
    :deep(.el-input__wrapper) {
      border: 1px solid #E5E5E5;
      border-radius: 4px;
    }
  }
  
  .search-btn {
    width: 94px;
    height: 36px;
    background: #2878FF;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    color: white;
    
    &:hover {
      background: #1f65d9;
    }
  }
  
  .clear-btn {
    width: 94px;
    height: 36px;
    background: white;
    border: 1px solid #C4C4C4;
    border-radius: 4px;
    font-size: 14px;
    color: #6D6D6D;
    
    &:hover {
      border-color: #999;
    }
  }
}

.search-row-second {
  .location-selector {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .location-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333333;
    font-size: 14px;
    white-space: nowrap;
    
    .location-icon {
      color: #666;
    }
  }
  
  .location-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    flex: 1;
  }
  
  .city-tag {
   
    font-size: 14px;
    color: #333333;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    line-height: 24px;
    
    &:hover {
      color: #2878FF;
    }
    
    &.active {
      padding: 0px 16px;
      border-radius: 20px;
      background: #2878FF;
      color: #FFFFFF;
      border-color: #2878FF;
    }
  }
}

/* 列表模块 */
.list-section {
  padding: 20px;
}

.fair-list {
  .fair-item {
    display: flex;
    align-items: stretch;
    padding: 20px 0;
    border-bottom: 1px solid #EBEBEB;
    gap: 20px;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

/* 日期部分 */
.date-section {
  width: 92px;
  flex-shrink: 0;
  
  .date-top {
    height: 40px;
    background: #2DD2B6;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    
    .date-icon {
      color: white;
      font-size: 14px;
    }
    
    .date-text {
      font-size: 16px;
      color: #FFFFFF;
      font-weight: 500;
    }
  }
  
  .date-bottom {
    height: 40px;
    background: white;
    border: 1px solid #2DD2B6;
    border-top: none;
    border-radius: 0 0 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .weekday {
      font-size: 16px;
      color: #666666;
    }
  }
}

/* 内容部分 */
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 4px;
  
  &:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
  }
  
  .content-top {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .status-tag {
      background: #F1FFF5;
      color: #3CC974;
      font-size: 14px;
      padding: 3px 10px;
      border-radius: 8px;
    }
    
    .type-tag {
      color: #2878FF;
      font-size: 14px;
    }
    
    .title {
      font-size: 18px;
      color: #333333;
      font-weight: 500;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: bold;
    }
  }
  
  .content-middle,
  .content-bottom {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
    
    .label {
      font-size: 14px;
      color: #9A9A9A;
    }
    
    .value {
      font-size: 14px;
      color: #333333;
    }
  }
}

/* 操作部分 */
.action-section {
  width: 120px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  
  .booth-info {
    text-align: center;
    
    .remaining {
      font-size: 14px;
      color: #B8B8B8;
      display: block;
    }
    
    .count {
      font-size: 14px;
      
      .remaining-count {
        color: #3CC974;
      }
      
      .total-count {
        color: #333333;
      }
    }
  }
  
  .action-btn {
    width: 145px;
    height: 40px;
    font-size: 14px;
    border-radius: 4px;
    border: none;
    padding: 0;
    
    &.btn-primary {
      background: #2878FF;
      color: #FFFFFF;
      
      &:hover {
        background: #1f65d9;
      }
    }
    
    &.btn-pending {
      background: #FF8426;
      color: #FFFFFF;
    }
    
    &.btn-rejected {
      background: #8EA4B1;
      color: #FFFFFF;
    }
    
    &.btn-ended {
      background: #C4C4C4;
      color: #FFFFFF;
    }
    
    &.btn-disabled {
      background: #C4C4C4;
      color: #FFFFFF;
      cursor: not-allowed;
    }
    
    &.btn-success {
      background: #29B56F;
      color: #FFFFFF;
      
      &:hover {
        background: #238c5e;
      }
    }
  }
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  
  :deep(.el-pagination) {
    .el-pager li {
      background-color: #fff;
      border-radius: 6px;
    }
    
    .el-pager li.is-active {
      background: #2878FF;
    }
    
    button {
      border-radius: 6px;
      background: #fff;
    }
  }
}
</style> 