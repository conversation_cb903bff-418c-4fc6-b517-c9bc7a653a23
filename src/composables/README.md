# usePositionFavorite 职位收藏功能

一个用于处理职位收藏和取消收藏的 Vue 3 Composable，包含完整的用户状态检查和错误处理。

## 功能特性

- ✅ 收藏和取消收藏职位
- ✅ 自动检查用户是否为求职者
- ✅ 401 错误自动跳转登录页面
- ✅ 支持自定义返回地址 (returnUrl)
- ✅ 完整的 TypeScript 类型支持
- ✅ 加载状态管理
- ✅ 统一的错误处理和用户提示

## 基本使用

```vue
<template>
  <button 
    @click="handleToggleFavorite" 
    :disabled="loading"
    :class="{ 'favorited': isFavorited }"
  >
    {{ loading ? '处理中...' : (isFavorited ? '已收藏' : '收藏') }}
  </button>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { usePositionFavorite } from '@/composables'

// 职位数据（假设从 props 或其他地方获取）
const positionGuid = '12345-abcde-67890'
const isFavorited = ref(false) // 当前收藏状态

// 使用 composable
const { loading, toggleFavorite } = usePositionFavorite()

// 切换收藏状态
const handleToggleFavorite = async () => {
  const result = await toggleFavorite(positionGuid, isFavorited.value)
  
  if (result.success) {
    isFavorited.value = !isFavorited.value
  }
}
</script>
```

## API 参考

### usePositionFavorite()

返回以下方法和状态：

#### 状态
- `loading: Ref<boolean>` - 是否正在处理请求

#### 方法

##### favoritePosition(positionGuid, districtId?, from?, returnUrl?)
收藏职位

**参数：**
- `positionGuid: string` - 职位GUID（必需）
- `districtId?: BussDistrict` - 地市ID（可选）
- `from?: ApplicationPlatform` - 请求来源平台，默认为 PC(0)
- `returnUrl?: string` - 登录后返回地址，默认为当前页面

**返回：**
```typescript
Promise<{
  success: boolean
  data?: any
  error?: string
}>
```

##### cancelFavoritePosition(positionGuid, districtId?, from?, returnUrl?)
取消收藏职位

参数和返回值与 `favoritePosition` 相同。

##### toggleFavorite(positionGuid, isFavorited, districtId?, from?, returnUrl?)
切换收藏状态

**参数：**
- `positionGuid: string` - 职位GUID（必需）
- `isFavorited: boolean` - 当前是否已收藏（必需）
- `districtId?: BussDistrict` - 地市ID（可选）
- `from?: ApplicationPlatform` - 请求来源平台，默认为 PC(0)
- `returnUrl?: string` - 登录后返回地址，默认为当前页面

##### checkUserAndRedirect(returnUrl?)
检查用户状态并在需要时跳转到登录页面

**参数：**
- `returnUrl?: string` - 登录后返回地址，默认为当前页面

**返回：**
- `boolean` - 如果用户是求职者返回 true，否则跳转到登录页面并返回 false

## 高级使用

### 自定义返回地址
```typescript
const handleFavorite = async () => {
  // 指定登录成功后返回到特定页面
  const result = await favoritePosition(
    positionGuid, 
    undefined, // districtId
    0,         // PC 平台
    '/job-list' // 自定义返回地址
  )
}
```

### 带地市ID的收藏
```typescript
import { BussDistrict } from '@/api-services/models'

const handleFavorite = async () => {
  const result = await favoritePosition(
    positionGuid,
    BussDistrict.NUMBER_1, // 桂林
    0 // PC 平台
  )
}
```

### 错误处理
```typescript
const handleFavorite = async () => {
  const result = await favoritePosition(positionGuid)
  
  if (!result.success) {
    console.error('收藏失败:', result.error)
    // 可以根据错误信息进行特殊处理
  }
}
```

## 自动功能

### 用户状态检查
- 自动检查当前用户是否为求职者（personal 类型）
- 如果不是求职者或未登录，自动跳转到个人登录页面

### 401 错误处理
- 接口返回 401 状态码时，自动跳转到登录页面
- 登录成功后会返回到指定的页面

### 用户提示
- 成功操作时显示成功提示
- 失败时显示具体的错误信息

## 环境变量

确保在 `.env` 文件中配置了个人登录页面地址：

```env
# 个人登录页面地址
VITE_PERSONAL_LOGIN_URL=http://gxjy-my.tgxrc.com
```