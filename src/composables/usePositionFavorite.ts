import { ref } from 'vue'
import { useUserStore } from '@/stores/userStore'
import { UserApi } from '@/api-services'
import { getPcAPI } from '@/utils/axios-utils'
import type { FavoritePositionQuery, BussDistrict, ApplicationPlatform } from '@/api-services/models'
import { ElMessage } from 'element-plus'

/**
 * 职位收藏功能 composable
 * 处理职位收藏和取消收藏，包含登录状态检查和401错误处理
 */
export function usePositionFavorite() {
  const userStore = useUserStore()
  const loading = ref(false)

  /**
   * 检查用户是否为求职者，如果不是则跳转到登录页面
   * @param returnUrl 登录成功后返回的地址，默认为当前页面
   */
  const checkUserAndRedirect = (returnUrl?: string) => {
    if (userStore.userType !== 'personal') {
      const currentUrl = returnUrl || window.location.href
      const loginUrl = import.meta.env.VITE_PERSONAL_LOGIN_URL
      const redirectUrl = `${loginUrl}?returnUrl=${encodeURIComponent(currentUrl)}`
      window.location.href = redirectUrl
      return false
    }
    return true
  }

  /**
   * 收藏职位
   * @param positionGuid 职位GUID
   * @param districtId 地市ID，可选
   * @param from 请求来源平台，默认为PC(0)
   * @param returnUrl 如需跳转登录页面时的返回地址
   */
  const favoritePosition = async (
    positionGuid: string,
    districtId?: BussDistrict,
    from: ApplicationPlatform = 0, // PC平台
    returnUrl?: string
  ) => {
    // 检查用户状态
    if (!checkUserAndRedirect(returnUrl)) {
      return { success: false, error: '用户未登录或非求职者' }
    }

    loading.value = true
    
    try {
      const userApi = getPcAPI(UserApi)
      const body: FavoritePositionQuery = { positionGuid }
      
      const response = await userApi.apiUserFavoritePositionPost(body, districtId, from)
      
      ElMessage.success('收藏成功')
      return { success: true, data: response.data }
    } catch (error: any) {
      // 处理401错误
      if (error?.response?.status === 401) {
        const currentUrl = returnUrl || window.location.href
        const loginUrl = import.meta.env.VITE_PERSONAL_LOGIN_URL
        const redirectUrl = `${loginUrl}?returnUrl=${encodeURIComponent(currentUrl)}`
        window.location.href = redirectUrl
        return { success: false, error: '登录已过期，请重新登录' }
      }
      
      const errorMsg = error?.response?.data?.message || error?.message || '收藏失败'
      ElMessage.error(errorMsg)
      return { success: false, error: errorMsg }
    } finally {
      loading.value = false
    }
  }

  /**
   * 取消收藏职位
   * @param positionGuid 职位GUID
   * @param districtId 地市ID，可选
   * @param from 请求来源平台，默认为PC(0)
   * @param returnUrl 如需跳转登录页面时的返回地址
   */
  const cancelFavoritePosition = async (
    positionGuid: string,
    districtId?: BussDistrict,
    from: ApplicationPlatform = 0, // PC平台
    returnUrl?: string
  ) => {
    // 检查用户状态
    if (!checkUserAndRedirect(returnUrl)) {
      return { success: false, error: '用户未登录或非求职者' }
    }

    loading.value = true
    
    try {
      const userApi = getPcAPI(UserApi)
      const body: FavoritePositionQuery = { positionGuid }
      
      const response = await userApi.apiUserCancelFavoritePositionPost(body, districtId, from)
      
      ElMessage.success('已取消收藏')
      return { success: true, data: response.data }
    } catch (error: any) {
      // 处理401错误
      if (error?.response?.status === 401) {
        const currentUrl = returnUrl || window.location.href
        const loginUrl = import.meta.env.VITE_PERSONAL_LOGIN_URL
        const redirectUrl = `${loginUrl}?returnUrl=${encodeURIComponent(currentUrl)}`
        window.location.href = redirectUrl
        return { success: false, error: '登录已过期，请重新登录' }
      }
      
      const errorMsg = error?.response?.data?.message || error?.message || '取消收藏失败'
      //ElMessage.error(errorMsg)
      return { success: false, error: errorMsg }
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换收藏状态
   * @param positionGuid 职位GUID
   * @param isFavorited 当前是否已收藏
   * @param districtId 地市ID，可选
   * @param from 请求来源平台，默认为PC(0)
   * @param returnUrl 如需跳转登录页面时的返回地址
   */
  const toggleFavorite = async (
    positionGuid: string,
    isFavorited: boolean,
    districtId?: BussDistrict,
    from: ApplicationPlatform = 0,
    returnUrl?: string
  ) => {
    if (isFavorited) {
      return await cancelFavoritePosition(positionGuid, districtId, from, returnUrl)
    } else {
      return await favoritePosition(positionGuid, districtId, from, returnUrl)
    }
  }

  return {
    loading,
    favoritePosition,
    cancelFavoritePosition,
    toggleFavorite,
    checkUserAndRedirect
  }
}