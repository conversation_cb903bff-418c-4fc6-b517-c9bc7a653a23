import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import router from './router'
import App from './App.vue'

// 样式导入
import './assets/styles/reset.scss';
import '@unocss/reset/normalize.css'
import './assets/styles/main.scss'
import 'uno.css'
import gxrcw from 'gxrcw-ui';
// Element Plus 自动导入样式
import 'element-plus/dist/index.css'

// 创建应用实例
const app = createApp(App)

// 创建 Pinia 实例
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 注册插件
app.use(pinia)
app.use(router)
app.use(gxrcw)
// 全局错误处理
app.config.errorHandler = (error, _instance, info) => {
  console.error('Global error:', error, info)
}



// 全局属性配置
app.config.globalProperties.$version = '1.0.0'
app.config.globalProperties.$buildTime = new Date().toISOString()

// 挂载应用
app.mount('#app')



export default app
