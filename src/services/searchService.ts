import { getPcAPI, feature } from '@/utils/axios-utils';
import { PositionApi, SortingSets } from '@/api-services';
import type { PositionRequest } from '@/api-services/models/position-request';
import { BussDistrict } from '@/api-services/models/buss-district';
import { ApplicationPlatform } from '@/api-services/models/application-platform';
import type { BaseSearchParams, SearchConfig } from '@/types/search';

export class SearchService {
  /**
   * 创建适用于分页组件的API函数
   * 直接返回原始API响应，无需额外封装
   */
  static createPaginationApiFunction<T extends BaseSearchParams>(
    searchType: SearchConfig<T>['searchType'],
    defaultParams: Record<string, any>,
    searchParams: () => Partial<T>
  ) {
    return async (paginationParams: { page: number; pageSize?: number; num?: number }) => {
      
      
      // 合并所有参数
      const currentSearchParams = searchParams();
      const requestBody: PositionRequest = {
        // 基础搜索参数
        keyword: currentSearchParams.keyword || "",
        workProperty: currentSearchParams.workProperty?.length ? currentSearchParams.workProperty : [],
        enterpriseProperty: currentSearchParams.entProp?.length ? currentSearchParams.entProp : null,
        welfare: currentSearchParams.welfare?.length ? currentSearchParams.welfare : [],
        requirementOfEducationDegree: currentSearchParams.edu?.length ? currentSearchParams.edu : [],
        online: currentSearchParams.online || false,
        emergency: currentSearchParams.emergency || false,
        businessDistinct: currentSearchParams.areaBusiness || [],
        payment: currentSearchParams.salary && currentSearchParams.salary > 0 ? [currentSearchParams.salary.toString()] : null,
        enterpriseEmployeeNumber: currentSearchParams.firmSize?.length ? currentSearchParams.firmSize : null,
        firstPublishDate: currentSearchParams.firstDay ? currentSearchParams.firstDay + "Day" : "",
        positionCaree: currentSearchParams.positionType || [],
        positionIndustry: currentSearchParams.industryType || [],
        orderBy: (currentSearchParams.sort as unknown as SortingSets) || 0,
        
        // 分页参数
        page: paginationParams.page,
        pageSize:  20,
        highlight: 1, // 默认高亮
        distance: "",
        // 默认参数
        ...defaultParams
      };

      // 处理特定搜索类型的特殊参数
      if (searchType === 'park' && 'parkId' in currentSearchParams && currentSearchParams.parkId) {
        requestBody.selectedParkIds = [parseInt(currentSearchParams.parkId as string)];
      }

      

      // 调用API并直接返回结果
      const apiCall = this.getApiCall(searchType);
      const [err, res] = await feature(apiCall(requestBody, BussDistrict.NUMBER_0, ApplicationPlatform.PC));
      
      if (err) {
        throw new Error(err.message || '网络请求失败');
      }
      
      // 直接返回原始响应，无需额外封装
      return res;
    };
  }

  /**
   * 获取对应搜索类型的API调用方法
   */
  private static getApiCall(searchType: SearchConfig['searchType']) {
    const positionApi = getPcAPI(PositionApi);
    
    switch (searchType) {
      case 'oddJob':
      case 'elderly':
      case 'park':
      default:
        // 所有搜索类型都使用同一个API
        return (body: PositionRequest, districtId: BussDistrict, from: ApplicationPlatform) => 
          positionApi.apiPositionSearchWithPushPost(body, districtId, from);
    }
  }
}