import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { CityInfo, CityCode } from '@/types'

// 城市配置数据
const CITIES_CONFIG: Record<CityCode, CityInfo> = {
  gx: {
    code: 'gx',
    name: '广西',
    nameEn: 'gx',
    description: '广西壮族自治区门户网站',
    isActive: true,
    id: 0,
    cityId: 1
  },
  nn: {
    code: 'nn',
    name: '南宁',
    nameEn: 'Nanning',
    description: '南宁市，广西壮族自治区首府，北部湾城市群核心城市',
    isActive: true,
    id: 19,
    cityId: 2
  },
  gl: {
    code: 'gl',
    name: '桂林',
    nameEn: 'Guilin',
    description: '桂林市门户网站，山水甲天下',
    isActive: true,
    id: 1,
    cityId: 3
  },
  lz: {
    code: 'lz',
    name: '柳州',
    nameEn: 'Liuzhou',
    description: '柳州市门户网站，工业重镇',
    isActive: true,
    id: 2,
    cityId: 4
  },
  wz: {
    code: 'wz',
    name: '梧州',
    nameEn: 'Wuzhou',
    description: '梧州市门户网站，两广枢纽',
    isActive: true,
    id: 4,
    cityId: 69
  },
  bs: {
    code: 'bs',
    name: '百色',
    nameEn: 'Baise',
    description: '百色市门户网站，革命老区',
    isActive: true,
    id: 6,
    cityId: 13
  },
  qz: {
    code: 'qz',
    name: '钦州',
    nameEn: 'Qinzhou',
    description: '钦州市门户网站，北部湾明珠',
    isActive: true,
    id: 7,
    cityId: 72
  },
  hc: {
    code: 'hc',
    name: '河池',
    nameEn: 'Hechi',
    description: '河池市门户网站，世界长寿之乡',
    isActive: true,
    id: 8,
    cityId: 10
  },
  bh: {
    code: 'bh',
    name: '北海',
    nameEn: 'Beihai',
    description: '北海市门户网站，滨海旅游城市',
    isActive: true,
    id: 9,
    cityId: 70
  },
  fg: {
    code: 'fcg',
    name: '防城港',
    nameEn: 'Fangchenggang',
    description: '防城港市门户网站，边境港口城市',
    isActive: true,
    id: 11,
    cityId: 71
  },
  yl: {
    code: 'yl',
    name: '玉林',
    nameEn: 'Yulin',
    description: '玉林市门户网站，岭南都会',
    isActive: true,
    id: 12,
    cityId: 78
  },
  cz: {
    code: 'cz',
    name: '崇左',
    nameEn: 'Chongzuo',
    description: '崇左市门户网站，边境口岸城市',
    isActive: true,
    id: 13,
    cityId: 77
  },
  gg: {
    code: 'gg',
    name: '贵港',
    nameEn: 'Guigang',
    description: '贵港市门户网站，西江明珠',
    isActive: true,
    id: 14,
    cityId: 12
  },
  lb: {
    code: 'lb',
    name: '来宾',
    nameEn: 'Laibin',
    description: '来宾市门户网站，世界瑶都',
    isActive: true,
    id: 15,
    cityId: 75
  },
  hz: {
    code: 'hz',
    name: '贺州',
    nameEn: 'Hezhou',
    description: '贺州市门户网站，粤港澳后花园',
    isActive: true,
    id: 18,
    cityId: 74
  }
}

export const useCityStore = defineStore('city', () => {
  // 状态
  const currentCity = ref<CityCode>('gx')
  const cityInfo = ref<CityInfo>(CITIES_CONFIG.gx)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const availableCities = computed(() => {
    return Object.values(CITIES_CONFIG).filter(city => city.isActive)
  })


  const isSupportedCity = computed(() => (cityCode: string): boolean => {
    return cityCode in CITIES_CONFIG
  })

  const getCityByCode = computed(() => (cityCode: CityCode): CityInfo | undefined => {
    return CITIES_CONFIG[cityCode]
  })

  // 统一的路径生成方法
  const getCityHomePath = computed(() => {
    return currentCity.value === 'gx' ? '/' : `/${currentCity.value}`
  })

  const getCityPagePath = computed(() => (page: string = '') => {
    if (currentCity.value === 'gx') {
      return page ? `/${page}` : '/'
    }
    return page ? `/${currentCity.value}/${page}` : `/${currentCity.value}`
  })

  const isCurrentPath = computed(() => (path: string) => {
    // 这个方法需要在组件中调用，所以暂时保留为函数
    // 实际使用时会传入当前路由路径
    return (currentPath: string) => {
      if (currentCity.value === 'gx') {
        if (path === '') {
          return currentPath === '/' || currentPath === ''
        }
        return currentPath.startsWith(path.startsWith('/') ? path : `/${path}`)
      } else {
        const cityPath = `/${currentCity.value}`
        if (path === '') {
          return currentPath === cityPath || currentPath === cityPath + '/'
        }
        return currentPath.startsWith(`${cityPath}${path.startsWith('/') ? path : `/${path}`}`)
      }
    }
  })

  // Actions
  const setCurrentCity = (cityCode: CityCode): boolean => {
    if (!isSupportedCity.value(cityCode)) {
      error.value = `不支持的城市代码: ${cityCode}`
      return false
    }

    try {
      isLoading.value = true
      error.value = null

      const newCityInfo = CITIES_CONFIG[cityCode]
      if (!newCityInfo.isActive) {
        error.value = `城市 ${newCityInfo.name} 暂未开放`
        return false
      }

      currentCity.value = cityCode
      cityInfo.value = newCityInfo

      
      
      
      
      console.log(`切换到城市: ${newCityInfo.name}`)
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '切换城市失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  

  const initializeFromRoute = (cityCode: string): boolean => {
    if (!isSupportedCity.value(cityCode)) {
      return setCurrentCity('gx')
    }
    return setCurrentCity(cityCode as CityCode)
  }

  const clearError = () => {
    error.value = null
  }

  const refreshCityData = async (): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null
      
      // 这里可以添加从 API 获取城市配置的逻辑
      // const response = await api.getCityConfig(currentCity.value)
      // cityInfo.value = response.data
      
      console.log(`刷新城市数据: ${cityInfo.value.name}`)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刷新数据失败'
    } finally {
      isLoading.value = false
    }
  }

  

  return {
    // State
    currentCity: readonly(currentCity),
    cityInfo: readonly(cityInfo),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    availableCities,
    isSupportedCity,
    getCityByCode,

    // 路径生成方法
    getCityHomePath,
    getCityPagePath,
    isCurrentPath,

    // Actions
    setCurrentCity,
    initializeFromRoute,
    clearError,
    refreshCityData
  }
}, {
  persist: {
    key: 'city-store',
    storage: localStorage,
    paths: ['currentCity']
  }
})