import { defineStore } from "pinia";
import { ref } from "vue";
import { getPcAPI } from "@/utils/axios-utils";
import { DataApi } from "@/api-services/apis/data-api";
import { KeywordItemDto } from "@/api-services/models";
import { KeywordItemBuildKeywordTreeItemDto, KeywordTreeItemDto } from "@/utils/array-function";


export const useDictionaryStore = defineStore(
  "dictionary",
  () => {
    const industryTreeOptions = ref<KeywordTreeItemDto[]>([]);
    const positionTypeOptions = ref<KeywordTreeItemDto[]>([]);
    // 方法
    const setIndustryOptions = (options: KeywordItemDto[]) => {
      // 同时构建树状结构
      industryTreeOptions.value = KeywordItemBuildKeywordTreeItemDto(options);

      return industryTreeOptions.value;
    };

    const setPositionTypeOptions = (options: KeywordItemDto[]) => {
      positionTypeOptions.value = KeywordItemBuildKeywordTreeItemDto(options);

      return positionTypeOptions.value;
    };

    const getIndustryOptions = async (coerce = false) => {
      if (industryTreeOptions.value.length > 0 && !coerce) {
        return industryTreeOptions.value;
      }
      try {
        getPcAPI(DataApi)
          .apiDataIndustryGet()
          .then((res) => {
            if (res?.data?.data) {
              return setIndustryOptions(res.data.data);
            }
          });
      } catch (error) {
        console.log(error);
      }
    };

    const getPositionTypeOptions = async (coerce = false) => {
      if (positionTypeOptions.value.length > 0 && !coerce) {
        return positionTypeOptions.value;
      }
      try {
        getPcAPI(DataApi)
        .apiDataPositionGet()
        .then((res) => {
          if (res?.data?.data) {
            return setPositionTypeOptions(res.data.data);
          }
        });
      } catch (error) {
        console.log(error);
      }
     
    };

    return {
      industryTreeOptions,
      getIndustryOptions,
      positionTypeOptions,
      getPositionTypeOptions
    };
  },
  {
    persist: {
      key: "dictionary-store",
      storage: localStorage,
      paths: [],
    },
  }
);
