import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { AccountApi, ApplicationPlatform, EnumLoginFrom } from '@/api-services'
import { getPcAPI } from '@/utils/axios-utils'
import type { JobSeekerIdentityModel, EnterpriseIdentity } from '@/api-services/models'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 登录状态枚举
export enum LoginStatus {
  NOT_LOGGED_IN = 0, // 都没登录
  ENTERPRISE = 1,    // 企业登录
  PERSONAL = 2,      // 求职者登录
  ALL = 3           // 所有都登录（优先企业）
}

// 用户类型
export type UserType = 'enterprise' | 'personal' | 'none'

// 菜单项接口
export interface MenuItem {
  label: string
  path?: string
  action?: string
  icon?: string
  divided?: boolean
  external?: boolean  // 是否为外部链接
}

const host = import.meta.env.VITE_PERSONAL_LOGIN_URL;
const enterpriseHost = import.meta.env.VITE_ENTERPRISE_LOGIN_URL;

// 个人用户菜单配置
const PERSONAL_MENU_ITEMS: MenuItem[] = [
  { label: '我的简历', path: `${host}/resume`, external: true },
  { label: '谁看过我', path: `${host}/viewed`, external: true },
  { label: '投递记录', path: `${host}/delivery`, external: true },
  { label: '退出登录', action: 'logout' }
]

// 企业用户菜单配置
const ENTERPRISE_MENU_ITEMS: MenuItem[] = [
  { label: '职位管理', path: `${enterpriseHost}/Position/PositionManagement`, external: true },
  { label: '简历搜索', path: `${enterpriseHost}/Resume/ResumeReceived`, external: true },
  { label: '人才推荐', path: `${enterpriseHost}/FindPersonnel/recommendPersonnel`, external: true },
  { label: '人才搜索', path: `${enterpriseHost}/ManageSearch`, external: true },
  { label: '聊一聊', path: `${enterpriseHost}/chat`, external: true },
  { label: '退出登录', action: 'logout' }
]

export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const loginStatus = ref<LoginStatus>(LoginStatus.NOT_LOGGED_IN)
    const personalInfo = ref<JobSeekerIdentityModel | null>(null)
    const enterpriseInfo = ref<EnterpriseIdentity | null>(null)
    const isLoading = ref(false)

    // 计算属性
    const isLoggedIn = computed(() => loginStatus.value !== LoginStatus.NOT_LOGGED_IN)
    
    const userType = computed<UserType>(() => {
      if (loginStatus.value === LoginStatus.ENTERPRISE || loginStatus.value === LoginStatus.ALL) {
        return 'enterprise'
      }
      if (loginStatus.value === LoginStatus.PERSONAL) {
        return 'personal'
      }
      return 'none'
    })

    const displayName = computed(() => {
      if (userType.value === 'enterprise' && enterpriseInfo.value) {
        return enterpriseInfo.value.name || '企业用户'
      }
      if (userType.value === 'personal' && personalInfo.value) {
        return personalInfo.value.jobSeekerName || '个人用户'
      }
      return ''
    })

    const menuItems = computed(() => {
      if (userType.value === 'enterprise') {
        return ENTERPRISE_MENU_ITEMS
      }
      if (userType.value === 'personal') {
        return PERSONAL_MENU_ITEMS
      }
      return []
    })

    // 方法
    const fetchLoginStatus = async () => {
      if (isLoading.value) return
      
      isLoading.value = true
      try {
        const accountApi = getPcAPI(AccountApi)
        const response = await accountApi.apiAccountLoginStatusGet()
        
        if (response.data?.data) {
          const data = response.data.data
          loginStatus.value = data.loginStatus ?? LoginStatus.NOT_LOGGED_IN
          personalInfo.value = data.jobseeker ?? null
          enterpriseInfo.value = data.enterprise ?? null
        }
      } catch (error) {
        console.warn('获取登录状态失败:', error)
        // 不显示错误提示，保持静默失败
      } finally {
        isLoading.value = false
      }
    }

    const logout = async () => {
      try {
        const accountApi = getPcAPI(AccountApi)
        console.log(11);
        await accountApi.apiAccountLogOutPost({
          device: EnumLoginFrom.NUMBER_0,
          devToken: null
        })
        
        // 清空状态
        loginStatus.value = LoginStatus.NOT_LOGGED_IN
        personalInfo.value = null
        enterpriseInfo.value = null
        
        ElMessage.success('退出登录成功')
        
        // 刷新页面或跳转到首页
        router.push('/')
      } catch (error) {
        console.error('退出登录失败:', error)
        //ElMessage.error('退出登录失败')
      }
    }

    const handleMenuClick = (item: MenuItem) => {
      if (item.action === 'logout') {
        logout()
      } else if (item.path) {
        if (item.external) {
          // 外部链接，新窗口打开
          window.open(item.path, '_blank')
        } else {
          // 内部路由，使用 router 跳转
          router.push(item.path)
        }
      }
    }

    // 初始化时获取登录状态
    fetchLoginStatus()

    return {
      // 状态
      loginStatus,
      personalInfo,
      enterpriseInfo,
      isLoading,
      
      // 计算属性
      isLoggedIn,
      userType,
      displayName,
      menuItems,
      
      // 方法
      fetchLoginStatus,
      logout,
      handleMenuClick
    }
  },
  {
    persist: {
      key: 'user-store',
      storage: localStorage,
      paths: ['loginStatus', 'personalInfo', 'enterpriseInfo']
    }
  }
)