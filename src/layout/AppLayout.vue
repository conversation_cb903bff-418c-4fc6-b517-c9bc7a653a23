<template>
  <div class="app-layout min-h-screen flex flex-col bg-gray-50">
    <!-- 顶部栏 - 始终显示 -->
    <TopBar class="top-bar-sticky" />

    <!-- Header -->
    <HeaderComponent v-if="showHeader" class="header-sticky" />

    <!-- Main Content -->
    <main class="flex-1 relative">
      <router-view v-slot="{ Component, route }">
        <!-- <Transition name="page">
          <component 
            :is="Component" 
            :key="route.path"
            class="min-h-full"
          />
        </Transition> -->
        <component :is="Component" :key="route.path" class="min-h-full" />
      </router-view>
    </main>

    <!-- Footer -->
    <CityFooter v-if="showFooter" />

    <!-- Global Loading -->
    <Transition name="fade">
      <div
        v-if="isLoading"
        id="app-loading"
        class="fixed inset-0 bg-white flex-center z-50"
      >
        <div class="text-center">
          <div class="loading mb-4"></div>
          <p class="text-gray-600">加载中...</p>
        </div>
      </div>
    </Transition>

    <!-- Back to Top -->
    <Transition name="fade">
      <button
        v-show="showBackToTop"
        @click="scrollToTop"
        class="fixed bottom-6 right-6 w-12 h-12 bg-primary-500 text-white rounded-full flex-center shadow-lg hover:bg-primary-600 transition-all z-40"
        title="返回顶部"
      >
        <i class="i-ep-arrow-up text-xl"></i>
      </button>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import HeaderComponent from "@/components/layout/HeaderComponent.vue";
import CityFooter from "@/components/layout/CityFooter.vue";
import TopBar from "@/components/layout/TopBar.vue";

// 组件状态
const showBackToTop = ref(false);
const isLoading = ref(false);

// 路由信息
const route = useRoute();

// 显示逻辑说明：
// TopBar（包含城市选择器等）始终显示
// HeaderComponent（搜索框和导航菜单）根据路由meta控制显示/隐藏
const showHeader = computed(() => route.meta?.showHeader !== false);
const showFooter = computed(() => route.meta?.showFooter !== false);

// 滚动处理
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300;
};

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

// 生命周期
onMounted(() => {
  window.addEventListener("scroll", handleScroll, { passive: true });

  // 隐藏初始加载状态
  nextTick(() => {
    isLoading.value = false;
  });
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped lang="scss">
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: url(https://image.gxrc.com/thirdParty/gxjy/pc/home/<USER>
    no-repeat center 38px;
}

.top-bar-sticky {
  position: sticky;
  top: 0;
  z-index: 101; // 比 header-sticky 高一层
  backdrop-filter: blur(8px);
}

.header-sticky {
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  // background-color: rgba(255, 255, 255, 0.95);
}

// 页面过渡动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease-in-out;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// Loading spinner 居中样式
.loading {
  margin: 0 auto;
}
</style>
