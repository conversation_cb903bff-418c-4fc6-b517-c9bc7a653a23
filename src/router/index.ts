import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useCityStore } from '@/stores/cityStore'
import type { CityCode } from '@/types'
import Homepage from '@/views/homepages/gx/HomePage.vue'
import AppLayout from '@/layout/AppLayout.vue'
// 懒加载组件

const NotFoundPage = () => import('@/views/NotFoundPage.vue')


// 动态导入城市首页组件
const getCityHomePage = (cityCode: string) => {
  return () => import(`@/views/homepages/${cityCode}/HomePage.vue`).catch(() => {
    console.warn(`城市 ${cityCode} 的首页组件不存在，使用默认首页`)
    return import('@/views/homepages/gx/HomePage.vue')
  })
}

// 路由配置
const routes: RouteRecordRaw[] = [
  // 统一路由：支持可选的城市参数
  {
    path: '/:city(gl|lz|wz|bs|qz|hc|bh|fg|yl|cz|gg|lb|hz|nn)?',
    component: AppLayout,
    beforeEnter: (to, _from, next) => {
      const cityStore = useCityStore()
      const cityCode = (to.params.city as string) || 'gx' // 默认使用广西

      if (cityCode !== 'gx' && !cityStore.isSupportedCity(cityCode)) {
        console.warn(`不支持的城市代码: ${cityCode}，重定向到广西`)
        next('/')
        return
      }

      // 初始化城市状态
      const success = cityStore.initializeFromRoute(cityCode)
      if (!success) {
        next('/404')
        return
      }

      next()
    },
    children: [
      {
        path: '',
        name: 'Home',
        component: () => {
          // 从window.location获取当前路径
          const currentPath = window.location.pathname
          const pathSegments = currentPath.split('/').filter(segment => segment)
          const cityCode = pathSegments[0] || 'gx'
          
          if (cityCode === 'gx' || !pathSegments.length) {
            return Promise.resolve(Homepage) // 直接返回广西首页组件
          } else {
            return getCityHomePage(cityCode)() // 返回动态城市组件
          }
        },
        meta: {
          title: '首页'
        }
      },
      {
        path: 'oddjob',
        name: 'oddJob',
        component: () => import('@/views/oddJob/index.vue')
      },
      {
        path: 'demandMap',
        name: 'demandMap',
        component: () => import('@/views/demandMap/index.vue')
      },
      {
        path: 'serviceInstitution',
        name: 'serviceInstitution',
        component: () => import('@/views/serviceInstitution/index.vue')
      },
      {
        path: 'training-guidance',
        name: 'guidance',
        component: () => import('@/views/guidance/index.vue')
      },
      {
        path: 'recruitment-fair',
        name: 'RecruitmentFair',
        component: () => import('@/views/recruitment/index.vue'),
        meta: {
          title: '招聘会'
        }
      },
      {
        path: 'recruitment-fair/:id',
        name: 'RecruitmentFairDetail',
        component: () => import('@/views/recruitment/detail.vue'),
        meta: {
          title: '招聘会详情'
        }
      },
      {
        path: 'jobDetail/:id',
        name: 'JobDetail',
        component: () => import('@/views/homepages/jobDetail/index.vue'),
        meta: {
          title: '职位详情'
        }
      },
      {
        path: 'company/:id',
        name: 'CompanyDetail',
        component: () => import('@/views/homepages/companyDetail/index.vue'),
        meta: {
          title: '企业详情'
        }
      },
      {
        path: 'sydw',
        name: 'sydw',
        component: () => import('@/views/homepages/sydw/index.vue'),
        meta: {
          title: '事业单位招聘专栏'
        }
      },
      {
        path: 'search',
        name: 'search',
        component: () => import('@/views/homepages/search/index.vue'),
        meta: {
          title: '大龄劳动者专区'
        }
      },
      {
        path: 'parkRecruitment',
        name: 'parkRecruitment',
        component: () => import('@/views/parkRecruitment/index.vue'),
        meta: {
          title: '园区招聘专栏'
        }
      },
      {
        path: 'sydw-detail',
        name: 'sydwDetail',
        component: () => import('@/views/homepages/sydw/detail.vue'),
        meta: {
          title: '事业单位招聘详情'
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFoundPage,
    meta: {
      title: '页面未找到'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  //const cityStore = useCityStore()

  try {
    // 更新页面标题
    //const cityInfo = cityStore.cityInfo
    // let title = '广西壮族自治区“数智人社”—广西就业平台'
    
    // if (to.meta.title) {
    //   if (to.params.city && cityInfo) {
    //     title = `${to.meta.title} - ${cityInfo.name} - 广西就业门户网站`
    //   } else {
    //     title = `${to.meta.title} - 广西就业门户网站`
    //   }
    // }
    // document.title = title

    // 更新页面 meta 信息
    //updatePageMeta(to)

    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    next('/404')
  }
})

// 全局后置钩子
router.afterEach(() => {
  
})

// 更新页面 meta 信息
function updatePageMeta(route: any) {
  // 更新 meta description
  const descriptionMeta = document.querySelector('meta[name="description"]')
  if (descriptionMeta && route.meta.description) {
    descriptionMeta.setAttribute('content', route.meta.description)
  }

  // 更新 meta keywords
  const keywordsMeta = document.querySelector('meta[name="keywords"]')
  if (keywordsMeta && route.meta.keywords) {
    const keywords = Array.isArray(route.meta.keywords) 
      ? route.meta.keywords.join(',') 
      : route.meta.keywords
    keywordsMeta.setAttribute('content', keywords)
  }
}

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  
  // 可以在这里添加错误上报逻辑
  // errorReport(error)
})

export default router

// 导出路由相关的工具函数
export const goToCity = (cityCode: string, page: string = '') => {
  const cityStore = useCityStore()
  
  if (!cityStore.isSupportedCity(cityCode)) {
    console.warn(`不支持的城市代码: ${cityCode}`)
    return false
  }

  // 先更新store状态
  const success = cityStore.setCurrentCity(cityCode as CityCode)
  if (!success) {
    console.error(`更新城市状态失败: ${cityCode}`)
    return false
  }

  // 根据城市代码生成路径
  let path = ''
  if (cityCode === 'gx') {
    // 广西使用根路径
    path = page ? `/${page}` : '/'
  } else {
    // 其他城市使用缩写路径
    path = page ? `/${cityCode}/${page}` : `/${cityCode}`
  }
  
  router.push(path)
  return true
}

export const goToPage = (page: string) => {
  const cityStore = useCityStore()
  router.push(cityStore.getCityPagePath(page))
}

export const isCurrentCity = (cityCode: string): boolean => {
  const cityStore = useCityStore()
  return cityStore.currentCity === cityCode
}

export const getCurrentRoute = () => {
  return router.currentRoute.value
}