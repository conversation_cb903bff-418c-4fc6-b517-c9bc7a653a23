import { KeywordItemDto } from "@/api-services/models/keyword-item-dto";

// 扩展接口，添加 children 属性
export interface KeywordTreeItemDto extends KeywordItemDto {
    children?: KeywordTreeItemDto[];
  }
export const KeywordItemBuildKeywordTreeItemDto = (
    items: KeywordItemDto[]
  ): KeywordTreeItemDto[] => {
    const itemMap = new Map<number, KeywordTreeItemDto>();
    const rootItems: KeywordTreeItemDto[] = [];

    items.forEach((item) => {
      const treeItem: KeywordTreeItemDto = {
        ...item,
        children: [],
      };
      // if(treeItem.parentID && item.hasNext && item.keywordID !== undefined){
      //   let allItem = Object.assign(item,{hasNext:false,keywordName:'全部',parentID:item.keywordID})
      //   itemMap.set(item.keywordID,allItem)
      // }
      if (item.keywordID !== undefined) {
        itemMap.set(item.keywordID, treeItem);
      }
     
    });

    // 构建树状结构
    items.forEach((item) => {
      if (item.keywordID !== undefined) {
        const treeItem = itemMap.get(item.keywordID);
        if (treeItem) {
          // 如果有父级ID且不为0，则添加到父级的children中
          if (item.parentID && item.parentID !== 0) {
            const parent = itemMap.get(item.parentID);
            if (parent) {
              parent.children = parent.children || [];
              if(parent.children.length === 0&&parent.parentID && parent.parentID !== 0){
                const allItem = Object.assign(item,{hasNext:false,keywordName:'全部',parentID:parent.keywordID,keywordID:parent.keywordID,children:[]})
                parent.children.push(allItem)
              }
              parent.children.push(treeItem);
            } else {
              // 如果找不到父级，作为根节点处理
              rootItems.push(treeItem);
            }
          } else {
            // 没有父级ID或父级ID为0，作为根节点
            rootItems.push(treeItem);
          }
        }
      }
    });
    return rootItems;
  };