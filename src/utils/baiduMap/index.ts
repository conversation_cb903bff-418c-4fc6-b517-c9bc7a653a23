export const AK = "UYtLV32nFeSEa4MDimyzGhdGjaWuRPmM";
export const loadBaiduMap = () => {
  //动态引入百度地图js，一个页面尽量只调用一次，包括页面下的子组件如果能用到，在这个方法回调时再v-if

  const BMap_URL =
    "//api.map.baidu.com/api?type=webgl&v=1.0&ak=" +
    AK +
    "&s=1&callback=onBMapCallback";

  return new Promise(function (resolve, reject) {
    let timeout = 0;
    let time = null;
    if (typeof BMapGL !== "undefined") {
      resolve(BMapGL);
      return true;
    } else {
    }
    // 百度地图异步加载回调处理
    window.onBMapCallback = function () {
      // console.log('百度地图脚本初始化成功...');
      resolve(BMapGL);
    };
    // 插入script脚本
    let scriptNode = document.createElement("script");
    scriptNode.setAttribute("type", "text/javascript");
    scriptNode.setAttribute("src", BMap_URL);
    document.body.appendChild(scriptNode);
    time = setInterval(() => {
      timeout++;
      if (timeout > 5) {
        clearInterval(time);
        reject(false);
      }
    }, 1000);
  });
};

export const loadMapVgl = (standby: boolean = false) => {
  const BMap_URL ='https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.189/dist/mapvgl.min.js' 
  const BMap_URL_standby = `/js/mapvgl.js`

  return new Promise(function (resolve, reject) {
    let timeout = 0;
    let time = null;
    if (typeof mapvgl !== "undefined") {
      resolve(mapvgl);
      return true;
    } else {
    }
    // 百度地图异步加载回调处理

    // 插入script脚本
    let scriptNode = document.createElement("script");
    scriptNode.setAttribute("type", "text/javascript");
    scriptNode.setAttribute("src", standby ? BMap_URL_standby : BMap_URL);

    document.body.appendChild(scriptNode);

    if (window.ActiveXObject || "ActiveXObject" in window) {
      //判断是否是ie
      if (scriptNode.readyState) {
        //判断是否支持readyState
        scriptNode.onreadystatechange = function () {
          if (this.readyState == "loaded" || this.readyState == "complete") {
            resolve(mapvgl);
          }
        };
      } else {
        scriptNode.onload = function () {
          resolve(mapvgl);
        };
      }
    } else {
      //不是ie
      scriptNode.onload = function () {
        resolve(mapvgl);
      };
      scriptNode.onerror = async function () {
        console.log("mapvgl.js加载失败");
        await loadMapVgl(true);
        if (typeof mapvgl !== "undefined") {
          resolve(mapvgl);
        } else {
          reject(false);
        }
      };
    }

    time = setInterval(async () => {
      timeout++;
      if (timeout > 5) {
        clearInterval(time);
        if (!standby) {
          await loadMapVgl(true);
          if (typeof mapvgl !== "undefined") {
            resolve(mapvgl);
          } else {
            reject(false);
          }
        } else {
          reject(false);
        }
      }
    }, 1000);
  });
};

export const loadMapV = (standby: boolean = false) => {
  const BMap_URL = "https://mapv.baidu.com/build/mapv.min.js";

  const BMap_URL_standby = `/js/mapv.js`;

  return new Promise(function (resolve, reject) {
    let timeout = 0;
    let time = null;
    if (typeof mapv !== "undefined") {
      resolve(mapv);
      return true;
    } else {
    }
    // 百度地图异步加载回调处理

    // 插入script脚本
    let scriptNode = document.createElement("script");
    scriptNode.setAttribute("type", "text/javascript");
    scriptNode.setAttribute("src", standby ? BMap_URL_standby : BMap_URL);

    document.body.appendChild(scriptNode);

    if (window.ActiveXObject || "ActiveXObject" in window) {
      //判断是否是ie
      if (scriptNode.readyState) {
        //判断是否支持readyState
        scriptNode.onreadystatechange = function () {
          if (this.readyState == "loaded" || this.readyState == "complete") {
            resolve(mapv);
          }
        };
      } else {
        scriptNode.onload = function () {
          resolve(mapv);
        };
        scriptNode.onerror = function () {
          console.log("mapv.js加载失败");
        };
      }
    } else {
      //不是ie
      scriptNode.onload = function () {
        resolve(mapv);
      };
      scriptNode.onerror = async function () {
        await loadMapV(true);
        if (typeof mapv !== "undefined") {
          resolve(mapv);
        } else {
          reject(false);
        }
      };
    }

    time = setInterval(async () => {
      timeout++;
      if (timeout > 5) {
        clearInterval(time);
        if (!standby) {
          await loadMapV(true);
          if (typeof mapv !== "undefined") {
            resolve(mapv);
          } else {
            reject(false);
          }
        } else {
          reject(false);
        }
      }
    }, 1000);
  });
};
