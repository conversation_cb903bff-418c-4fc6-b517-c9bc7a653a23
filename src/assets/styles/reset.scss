
/* CSS Reset - 清除默认样式 */

/* 1. 基础重置 */
* {
    box-sizing: border-box;
  }
  
  *::before,
  *::after {
    box-sizing: border-box;
  }
  
  /* 2. HTML和Body */
  html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
  }
  
  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
  }
  
  /* 3. 标题元素 */
  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    font-weight: normal;
    font-size: inherit;
  }
  
  /* 4. 文本元素 */
  p, blockquote, pre {
    margin: 0;
    padding: 0;
  }
  
  /* 5. 链接 */
  a {
    color: #333;
    text-decoration: none;
    background-color: transparent;
  }
  
  a:hover {
    text-decoration: none;
    color: #1677ff;
  }
  
  a:active,
  a:hover {
    outline: 0;
  }
  
  /* 6. 列表 */
  ul, ol, li {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  
  dl, dt, dd {
    margin: 0;
    padding: 0;
  }
  
  /* 7. 表格 */
  table {
    border-collapse: collapse;
    border-spacing: 0;
  }
  
  th, td {
    margin: 0;
    padding: 0;
    text-align: left;
    vertical-align: top;
  }
  
  /* 8. 表单元素 */
  form, fieldset, legend {
    margin: 0;
    padding: 0;
    border: 0;
  }
  
  button, input, textarea, select {
    margin: 0;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    border: 0;
    outline: 0;
  }
  
  button {
    background: transparent;
    cursor: pointer;
  }
  
  input, textarea {
    background: transparent;
  }
  
  /* 9. 图片和媒体 */
  img {
    max-width: 100%;
    height: auto;
    border: 0;
    vertical-align: middle;
  }
  
  svg {
    fill: currentColor;
  }
  
  /* 10. 其他元素 */
  hr {
    margin: 0;
    padding: 0;
    border: 0;
    height: 1px;
    background: #eee;
  }
  
  /* 11. 隐藏元素 */
  [hidden] {
    display: none !important;
  }
  
  /* 12. 辅助功能 */
  :focus {
    outline: 2px solid #1677ff;
    outline-offset: 2px;
  }
  
  :focus:not(:focus-visible) {
    outline: none;
  }
  
  /* 13. 选择文本样式 */
  ::selection {
    background-color: #1677ff;
    color: #fff;
  }
  
  ::-moz-selection {
    background-color: #1677ff;
    color: #fff;
  }
  