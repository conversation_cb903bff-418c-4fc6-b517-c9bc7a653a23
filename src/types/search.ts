// 基础搜索参数接口
export interface BaseSearchParams {
  keyword: string;
  positionType: number[];
  industryType: number[];
  schType: number;
  areaBusiness: string[];
  salary: number;
  emergency: boolean;
  online: boolean;
  sort: number;
  welfare: number[];
  workProperty: number[];
  entProp: number[];
  firmSize: number[];
  edu: number[];
  firstDay: string;
}

// 兼职工作特有参数
export interface OddJobSearchParams extends BaseSearchParams {
  workCycle: number;
  workHour: number;
  paymentType: number;
}

// 大龄工作者特有参数
export interface ElderlySearchParams extends BaseSearchParams {
  ageLimit?: number;
  experienceRequired?: boolean;
}

// 园区招聘特有参数
export interface ParkSearchParams extends BaseSearchParams {
  parkId?: string;
  companySize?: number[];
}

// 搜索配置接口
export interface SearchConfig<T extends BaseSearchParams = BaseSearchParams> {
  searchType: 'oddJob' | 'elderly' | 'park' | 'search';
  defaultParams: Partial<T>;
  specialParams?: string[];
}

// 搜索结果接口
export interface SearchResult<T = any> {
  success: boolean;
  data?: T[];
  total?: number;
  error?: string;
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  size: number;
}