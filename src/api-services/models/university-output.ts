/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { TypeOfHigherEducation } from './type-of-higher-education';
import { TypeOfInstitutions } from './type-of-institutions';
import { TypeOfKeyUniversity } from './type-of-key-university';
import { UniversityAddressOutput } from './university-address-output';
import { UniversityDepartmentOutput } from './university-department-output';
/**
 * 产业园大学输出数据模型
 * @export
 * @interface UniversityOutput
 */
export interface UniversityOutput {
    /**
     * 唯一标识
     * @type {string}
     * @memberof UniversityOutput
     */
    guid?: string;
    /**
     * 名称
     * @type {string}
     * @memberof UniversityOutput
     */
    name?: string | null;
    /**
     * 简介
     * @type {string}
     * @memberof UniversityOutput
     */
    briefIntroduction?: string | null;
    /**
     * 
     * @type {TypeOfHigherEducation}
     * @memberof UniversityOutput
     */
    typeOfHigherEducation?: TypeOfHigherEducation;
    /**
     * 高等教育分类文本(本科、专科)
     * @type {string}
     * @memberof UniversityOutput
     */
    typeOfHigherEducationText?: string | null;
    /**
     * 
     * @type {TypeOfInstitutions}
     * @memberof UniversityOutput
     */
    typeOfInstitutions?: TypeOfInstitutions;
    /**
     * 普通高校类别文本(综合类、师范类等)
     * @type {string}
     * @memberof UniversityOutput
     */
    typeOfInstitutionsText?: string | null;
    /**
     * 
     * @type {TypeOfKeyUniversity}
     * @memberof UniversityOutput
     */
    typeOfKeyUniversity?: TypeOfKeyUniversity;
    /**
     * 重点大学分类文本(211、985)
     * @type {string}
     * @memberof UniversityOutput
     */
    typeOfKeyUniversityText?: string | null;
    /**
     * 开设专业数量
     * @type {number}
     * @memberof UniversityOutput
     */
    numberOfMajors?: number;
    /**
     * 本科毕业生数量
     * @type {number}
     * @memberof UniversityOutput
     */
    numberOfUndergraduateGraduates?: number;
    /**
     * 专科毕业生数量
     * @type {number}
     * @memberof UniversityOutput
     */
    numberOfJuniorCollegeGraduates?: number;
    /**
     * 学校LOGO图片的URL
     * @type {string}
     * @memberof UniversityOutput
     */
    logo?: string | null;
    /**
     * 小程序端形象照片的URL列表
     * @type {Array<string>}
     * @memberof UniversityOutput
     */
    miniAppPhotos?: Array<string> | null;
    /**
     * 触屏端形象照片的URL列表
     * @type {Array<string>}
     * @memberof UniversityOutput
     */
    h5Photos?: Array<string> | null;
    /**
     * 地址列表
     * @type {Array<UniversityAddressOutput>}
     * @memberof UniversityOutput
     */
    addresses?: Array<UniversityAddressOutput> | null;
    /**
     * 院系列表
     * @type {Array<UniversityDepartmentOutput>}
     * @memberof UniversityOutput
     */
    departments?: Array<UniversityDepartmentOutput> | null;
}
