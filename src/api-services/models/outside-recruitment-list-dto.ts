/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 区外招聘列表返回数据
 * @export
 * @interface OutsideRecruitmentListDto
 */
export interface OutsideRecruitmentListDto {
    /**
     * 招聘信息ID
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    recruitmentId?: string | null;
    /**
     * 企业名称
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    enterpriseName?: string | null;
    /**
     * 统一社会信用代码
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    unifiedSocialCreditCode?: string | null;
    /**
     * 经济类型
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    economicType?: string | null;
    /**
     * 产业类别
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    industryCategory?: string | null;
    /**
     * 企业地址
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    enterpriseAddress?: string | null;
    /**
     * 招聘职业(工种)
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    recruitmentProfession?: string | null;
    /**
     * 招聘人数
     * @type {number}
     * @memberof OutsideRecruitmentListDto
     */
    recruitmentCount?: number;
    /**
     * 月薪
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    monthlySalary?: string | null;
    /**
     * 岗位要求
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    jobRequirements?: string | null;
    /**
     * 招聘有效期起
     * @type {Date}
     * @memberof OutsideRecruitmentListDto
     */
    recruitmentStartDate?: Date;
    /**
     * 招聘有效期止
     * @type {Date}
     * @memberof OutsideRecruitmentListDto
     */
    recruitmentEndDate?: Date;
    /**
     * 所属区划（字典值）
     * @type {number}
     * @memberof OutsideRecruitmentListDto
     */
    jurisdiction?: number;
    /**
     * 联系人
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    contactPerson?: string | null;
    /**
     * 联系电话
     * @type {string}
     * @memberof OutsideRecruitmentListDto
     */
    contactPhone?: string | null;
}
