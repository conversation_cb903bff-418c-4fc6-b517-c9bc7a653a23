/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface AppVersionDto
 */
export interface AppVersionDto {
    /**
     * 
     * @type {string}
     * @memberof AppVersionDto
     */
    ver?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppVersionDto
     */
    downloadUrl?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppVersionDto
     */
    iosVer?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isAppraise?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isUseWebView?: boolean;
    /**
     * 
     * @type {string}
     * @memberof AppVersionDto
     */
    notice?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isForce?: boolean;
    /**
     * 用来控制注册页面，ai助手的开关
     * @type {boolean}
     * @memberof AppVersionDto
     */
    openRegisterAI?: boolean;
    /**
     * 用来控制简历编辑页面ai助手的开关
     * @type {boolean}
     * @memberof AppVersionDto
     */
    openUpdateResumeAI?: boolean;
    /**
     * 开屏广告开关  1代表允许弹出广告，0代表不能弹出广告
     * @type {number}
     * @memberof AppVersionDto
     */
    allowSplashAd?: number | null;
    /**
     * 是否强制更新IOS
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isForceApple?: boolean;
    /**
     * 强制更新标题
     * @type {string}
     * @memberof AppVersionDto
     */
    forceTitle?: string | null;
    /**
     * 强制更新内容
     * @type {string}
     * @memberof AppVersionDto
     */
    forceContent?: string | null;
    /**
     * IOS强制更新版本号
     * @type {string}
     * @memberof AppVersionDto
     */
    forceAppleVersion?: string | null;
    /**
     * Android强制更新版本号
     * @type {string}
     * @memberof AppVersionDto
     */
    forceAndroidVersion?: string | null;
    /**
     * 获取开发者名称
     * @type {string}
     * @memberof AppVersionDto
     */
    developerName?: string | null;
    /**
     * 0=正常，1=红，2=灰色
     * @type {number}
     * @memberof AppVersionDto
     */
    appThemeColor?: number;
    /**
     * 是否显示微信客服
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isShowWXCustomer?: boolean | null;
    /**
     * 是否显示AI客服
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isShowAICustomer?: boolean | null;
    /**
     * AI客服地址
     * @type {string}
     * @memberof AppVersionDto
     */
    aiCustomerUrl?: string | null;
    /**
     * 微信客服ID
     * @type {string}
     * @memberof AppVersionDto
     */
    wxCustomerUrl?: string | null;
    /**
     * 是否开启小程序首页右下角浮窗广告
     * @type {boolean}
     * @memberof AppVersionDto
     */
    isOpendMiniFloatAD?: boolean;
    /**
     * 是否显示新简历模板标记
     * @type {boolean}
     * @memberof AppVersionDto
     */
    newResumeTemplateFlag?: boolean;
}
