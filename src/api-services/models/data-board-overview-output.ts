/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DataBoardTotallyOutput } from './data-board-totally-output';
import { DistributionStatisticsOutput } from './distribution-statistics-output';
import { UniversityStatisticsOutput } from './university-statistics-output';
/**
 * 产业园数据看板统计概况输出数据模型
 * @export
 * @interface DataBoardOverviewOutput
 */
export interface DataBoardOverviewOutput {
    /**
     * 
     * @type {DataBoardTotallyOutput}
     * @memberof DataBoardOverviewOutput
     */
    totally?: DataBoardTotallyOutput;
    /**
     * 企业性质分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    enterpriseProperties?: Array<DistributionStatisticsOutput> | null;
    /**
     * 企业行业分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    enterpriseIndustries?: Array<DistributionStatisticsOutput> | null;
    /**
     * 企业雇员规模分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    scaleOfEnterpriseEmployees?: Array<DistributionStatisticsOutput> | null;
    /**
     * 职位类型分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    positionCareers?: Array<DistributionStatisticsOutput> | null;
    /**
     * 职位学历要求分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    eduRequirementsForPosition?: Array<DistributionStatisticsOutput> | null;
    /**
     * 职位工作经验要求分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    workYearRequirementsForPosition?: Array<DistributionStatisticsOutput> | null;
    /**
     * 职位薪资范围分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    salaryRanges?: Array<DistributionStatisticsOutput> | null;
    /**
     * 登记人才工龄分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    talentWorkYears?: Array<DistributionStatisticsOutput> | null;
    /**
     * 登记人才求职意向分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    talentJobIntentions?: Array<DistributionStatisticsOutput> | null;
    /**
     * 登记人才年龄段分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    talentAges?: Array<DistributionStatisticsOutput> | null;
    /**
     * 登记人才学历分布
     * @type {Array<DistributionStatisticsOutput>}
     * @memberof DataBoardOverviewOutput
     */
    talentEduBackgrounds?: Array<DistributionStatisticsOutput> | null;
    /**
     * 
     * @type {UniversityStatisticsOutput}
     * @memberof DataBoardOverviewOutput
     */
    universityStatistics?: UniversityStatisticsOutput;
}
