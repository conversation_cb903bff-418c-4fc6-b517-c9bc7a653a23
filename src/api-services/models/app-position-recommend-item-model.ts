/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionKeywordDto } from './position-keyword-dto';
/**
 * 职位推荐
 * @export
 * @interface AppPositionRecommendItemModel
 */
export interface AppPositionRecommendItemModel {
    /**
     * 跟踪guid
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    trackingGuid?: string | null;
    /**
     * 企业ID
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    enterpriseID?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    enterpriseGuid?: string;
    /**
     * 职位
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    positionID?: number;
    /**
     * 
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    positionGuid?: string;
    /**
     * 职位名称
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    positionName?: string | null;
    /**
     * 企业名称
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    enterpriseName?: string | null;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 薪资待遇
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    payPackage?: string | null;
    /**
     * 薪资待遇
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    payPackageID?: number;
    /**
     * 工作地
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    workPlace?: string | null;
    /**
     * 工作性质
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    workProperty?: string | null;
    /**
     * 工作性质ID
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    workPropertyID?: number;
    /**
     * 学历
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    degreeName?: string | null;
    /**
     * 发布日期
     * @type {Date}
     * @memberof AppPositionRecommendItemModel
     */
    publishTime?: Date;
    /**
     * 发布日期描述 今天、昨天、04-22（本年度的不用显示年份）、2021-12-31（非本年度显示年份）
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    publishTimeDescribe?: string | null;
    /**
     * 是否接收毕业生
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    isReceiveGraduate?: boolean;
    /**
     * 值聊
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    zhiliao?: boolean;
    /**
     * 招聘人数
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    positionAmount?: string | null;
    /**
     * 是否急聘
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 工龄要求
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    workAge?: string | null;
    /**
     * 职位距离
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    distance?: string | null;
    /**
     * 部门ID
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    departmentId?: number;
    /**
     * 部门名称
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    departmentName?: string | null;
    /**
     * 工资福利
     * @type {Array<string>}
     * @memberof AppPositionRecommendItemModel
     */
    positionWelfare?: Array<string> | null;
    /**
     * 企业行业
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    enterpriseIndustryName?: string | null;
    /**
     * 企业性质
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    enterprisePropertyName?: string | null;
    /**
     * 企业性质
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    enterpriseProperty?: number;
    /**
     * 企业规模
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    enterpriseEmployeeNumberName?: string | null;
    /**
     * 企业Logo
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    logoUrl?: string | null;
    /**
     * 
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    requirementOfWorkAge?: number;
    /**
     * 关键词
     * @type {Array<PositionKeywordDto>}
     * @memberof AppPositionRecommendItemModel
     */
    positionKeywords?: Array<PositionKeywordDto> | null;
    /**
     * 薪酬计算最小值
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    monthPayPackageFrom?: number | null;
    /**
     * 薪酬计算最大值
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    monthPayPackageTo?: number | null;
    /**
     * 是否已 投递/申请
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    isDeliver?: boolean;
    /**
     * 已投递文案
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    deliverText?: string | null;
    /**
     * 曝光业务表主键
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    inviChatRecordId?: number;
    /**
     * 职位描述
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    describe?: string | null;
    /**
     * 是否是代招
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    isAgentRecruit?: boolean | null;
    /**
     * 工作性质
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    workPropertyName?: string | null;
    /**
     * 状态标签  2、回复时间显示 X分钟前回复：30分钟以内有回复的显示此状态 3、回复次数显示   今日回复X次：显示今日企业发出的消息总数，3次及以上才显示，10以内显示具体数字，10≤ X＜20显示10+次。 4、回复率显示 回复率高：今日企业发出的消息总数≥20次时，显示为回复率高。
     * @type {Array<string>}
     * @memberof AppPositionRecommendItemModel
     */
    activationTags?: Array<string> | null;
    /**
     * 企业云信ID
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    yxEnterpriseID?: string | null;
    /**
     * 求职者云信ID
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    yxJobSeekerID?: string | null;
    /**
     * 聊天按钮状态，求职者未登=0，企业未开通业务=1，聊一聊=2，继续聊=3。ChatStatus !=1 说明企业开有直聊业务，可以进行直聊
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    chatStatus?: number;
    /**
     * 只有聊一聊和继续聊两种状态，具体操作需要客户端根据状态码进行
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    chatStatusString?: string | null;
    /**
     * ChatStatus = 1 的时候 提示 企业尚未开通直聊业务或业务状态异常，请联系管理员 这个信息，不给往下操作 ChatStatus = 2 的时候 提示 请登录求职者账号
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    chatErrMsg?: string | null;
    /**
     * 是否已购买竞争力分析
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    isCompetion?: boolean;
    /**
     * 
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    addressZoomId?: number | null;
    /**
     * 0默认值，1准确推荐，2不是准确推荐（补充数据）
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    recommendType?: number;
    /**
     * 类型：0:原获取记录 1:文章广告推送 20230523
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    recordType?: number;
    /**
     * 如果是要显示一张图，这里记录图片链接
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    recordImageUrl?: string | null;
    /**
     * RecordType1:文章广告推送的时候跳转链接 20230523
     * @type {string}
     * @memberof AppPositionRecommendItemModel
     */
    jumpLinkUrl?: string | null;
    /**
     * ArticlePosition的主键ID
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    articlePositionID?: number;
    /**
     * 文章关联LogoID
     * @type {number}
     * @memberof AppPositionRecommendItemModel
     */
    logoID?: number;
    /**
     * 是否在线 20230602
     * @type {boolean}
     * @memberof AppPositionRecommendItemModel
     */
    online?: boolean;
}
