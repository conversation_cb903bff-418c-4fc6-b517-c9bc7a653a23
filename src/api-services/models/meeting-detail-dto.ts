/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ETicketType } from './eticket-type';
import { MeetingTag } from './meeting-tag';
/**
 * 招聘会详情
 * @export
 * @interface MeetingDetailDto
 */
export interface MeetingDetailDto {
    /**
     * 文章ID
     * @type {number}
     * @memberof MeetingDetailDto
     */
    articleID?: number;
    /**
     * 招聘会ID
     * @type {number}
     * @memberof MeetingDetailDto
     */
    articleEvaluation1?: number;
    /**
     * 招聘会标题
     * @type {string}
     * @memberof MeetingDetailDto
     */
    title?: string | null;
    /**
     * 举办时间
     * @type {string}
     * @memberof MeetingDetailDto
     */
    holdTime?: string | null;
    /**
     * 报名截止时间
     * @type {string}
     * @memberof MeetingDetailDto
     */
    appendEndTime?: string | null;
    /**
     * 报名截止
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isAppendEnd?: boolean;
    /**
     * 举办地址
     * @type {string}
     * @memberof MeetingDetailDto
     */
    address?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MeetingDetailDto
     */
    articleDescription?: string | null;
    /**
     * 招聘会标签
     * @type {Array<MeetingTag>}
     * @memberof MeetingDetailDto
     */
    tags?: Array<MeetingTag> | null;
    /**
     * 职位数量
     * @type {number}
     * @memberof MeetingDetailDto
     */
    positionCount?: number;
    /**
     * 岗位数
     * @type {number}
     * @memberof MeetingDetailDto
     */
    positionAmount?: number;
    /**
     * 企业数量
     * @type {number}
     * @memberof MeetingDetailDto
     */
    enterpriseCount?: number;
    /**
     * 承办方
     * @type {string}
     * @memberof MeetingDetailDto
     */
    undertaker?: string | null;
    /**
     * 举办方
     * @type {string}
     * @memberof MeetingDetailDto
     */
    hoster?: string | null;
    /**
     * 是否为掌上招聘会
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isPalm?: boolean;
    /**
     * 是否校园招聘会
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isO2O?: boolean;
    /**
     * 是否招聘会
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isLive?: boolean;
    /**
     * 是否网络招聘会
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isNetWork?: boolean;
    /**
     * 能否投递(是否再投递时间范围内)
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isCanDeliver?: boolean;
    /**
     * 控制小程序职位数量显示0不显示 1显示
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    showCount?: boolean;
    /**
     * 顶部背景
     * @type {string}
     * @memberof MeetingDetailDto
     */
    banner?: string | null;
    /**
     * 招聘会开始时间
     * @type {string}
     * @memberof MeetingDetailDto
     */
    startTime?: string | null;
    /**
     * 是否开启领取券
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isOpenTicket?: boolean;
    /**
     * 
     * @type {ETicketType}
     * @memberof MeetingDetailDto
     */
    ticketType?: ETicketType;
    /**
     * 领取按钮文字
     * @type {string}
     * @memberof MeetingDetailDto
     */
    tickeBtnTip?: string | null;
    /**
     * 是否开启简历打印
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isOpenResumePrint?: boolean;
    /**
     * 招聘会短标题，如果有值，会更新小程序的页面标题
     * @type {string}
     * @memberof MeetingDetailDto
     */
    shortTitle?: string | null;
    /**
     * 是否已经预约打印了
     * @type {boolean}
     * @memberof MeetingDetailDto
     */
    isAppointResumePrint?: boolean;
    /**
     * 简历打印 按钮提示
     * @type {string}
     * @memberof MeetingDetailDto
     */
    resumePrintTip?: string | null;
}
