/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnterpriseVideoOutput } from './enterprise-video-output';
import { EnterpriseVisualImageItemDto } from './enterprise-visual-image-item-dto';
/**
 * 
 * @export
 * @interface EnterpriseDisplayDto
 */
export interface EnterpriseDisplayDto {
    /**
     * 企业Guid
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseGuid?: string;
    /**
     * 企业ID
     * @type {number}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseID?: number;
    /**
     * 企业名称
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseName?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseProperty?: string | null;
    /**
     * 单位人数
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 单位行业
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseIndustry?: string | null;
    /**
     * 单位所在地
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseCity?: string | null;
    /**
     * 单位简介
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    description?: string | null;
    /**
     * 单位详细地址
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    address?: string | null;
    /**
     * 百度坐标纬度
     * @type {number}
     * @memberof EnterpriseDisplayDto
     */
    baiduLat?: number | null;
    /**
     * 百度坐标经度
     * @type {number}
     * @memberof EnterpriseDisplayDto
     */
    baiduLon?: number | null;
    /**
     * 联系人
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    contractPerson?: string | null;
    /**
     * 联系电话（注意，电话只是一张图片，这是图片的地址）
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterprisePhone?: string | null;
    /**
     * 电子邮箱
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseEmail?: string | null;
    /**
     * 官网
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseHomePage?: string | null;
    /**
     * 公司传真
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseFax?: string | null;
    /**
     * 邮政编码
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterprisePostalCode?: string | null;
    /**
     * 企业Logo
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 是否屏蔽
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    isMask?: boolean;
    /**
     * 是否收藏
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    isFavorite?: boolean;
    /**
     * 是否试用
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseTrialFlag?: boolean;
    /**
     * 企业是否有效
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseEnableFlag?: boolean;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 企业属于产业园 名称
     * @type {string}
     * @memberof EnterpriseDisplayDto
     */
    belongIndustrialPark?: string | null;
    /**
     * 企业形象照
     * @type {Array<EnterpriseVisualImageItemDto>}
     * @memberof EnterpriseDisplayDto
     */
    visualImages?: Array<EnterpriseVisualImageItemDto> | null;
    /**
     * 
     * @type {Array<EnterpriseVideoOutput>}
     * @memberof EnterpriseDisplayDto
     */
    visualVideos?: Array<EnterpriseVideoOutput> | null;
    /**
     * 是否公开电话
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    isPhonePublic?: boolean;
    /**
     * 是否公开邮箱
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    isEmailPublic?: boolean;
    /**
     * 最近登录时间 20230602加在线
     * @type {Date}
     * @memberof EnterpriseDisplayDto
     */
    enterpriseLastLoginTime?: Date | null;
    /**
     * 
     * @type {boolean}
     * @memberof EnterpriseDisplayDto
     */
    online?: boolean;
}
