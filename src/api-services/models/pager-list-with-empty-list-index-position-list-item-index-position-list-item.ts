/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexPositionListItem } from './index-position-list-item';
/**
 * 
 * @export
 * @interface PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
 */
export interface PagerListWithEmptyListIndexPositionListItemIndexPositionListItem {
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<IndexPositionListItem>}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    items?: Array<IndexPositionListItem> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    hasNextPages?: boolean;
    /**
     * 
     * @type {Array<IndexPositionListItem>}
     * @memberof PagerListWithEmptyListIndexPositionListItemIndexPositionListItem
     */
    emptyItems?: Array<IndexPositionListItem> | null;
}
