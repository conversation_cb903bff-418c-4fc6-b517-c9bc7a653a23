/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { HomeEnterprisePositionItemInfo } from './home-enterprise-position-item-info';
/**
 * 首页企业与其职位输出模型（包含行业信息）
 * @export
 * @interface HomeEnterprisePositionOutput
 */
export interface HomeEnterprisePositionOutput {
    /**
     * 企业ID
     * @type {number}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseId?: number;
    /**
     * 企业Guid
     * @type {string}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseName?: string | null;
    /**
     * 企业行业ID
     * @type {number}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseIndustryId?: number;
    /**
     * 企业行业名称
     * @type {string}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseIndustryName?: string | null;
    /**
     * 企业性质ID
     * @type {number}
     * @memberof HomeEnterprisePositionOutput
     */
    enterprisePropertyId?: number;
    /**
     * 企业性质名称
     * @type {string}
     * @memberof HomeEnterprisePositionOutput
     */
    enterprisePropertyName?: string | null;
    /**
     * 企业规模ID（职员人数）
     * @type {number}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseEmployeeNumberId?: number;
    /**
     * 企业规模名称（职员人数名称）
     * @type {string}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseEmployeeNumberName?: string | null;
    /**
     * 企业LOGO地址
     * @type {string}
     * @memberof HomeEnterprisePositionOutput
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 职位列表
     * @type {Array<HomeEnterprisePositionItemInfo>}
     * @memberof HomeEnterprisePositionOutput
     */
    positions?: Array<HomeEnterprisePositionItemInfo> | null;
}
