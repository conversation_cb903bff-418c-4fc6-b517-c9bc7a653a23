/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { BussDistrict } from './buss-district';
/**
 * 
 * @export
 * @interface PositionRecommendRequest
 */
export interface PositionRecommendRequest {
    /**
     * 分页
     * @type {number}
     * @memberof PositionRecommendRequest
     */
    page?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof PositionRecommendRequest
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PositionRecommendRequest
     */
    positionCareeID?: number;
    /**
     * 
     * @type {number}
     * @memberof PositionRecommendRequest
     */
    isAddPush?: number;
    /**
     * 1推荐、2附近 3 最新
     * @type {number}
     * @memberof PositionRecommendRequest
     */
    sort?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionRecommendRequest
     */
    location?: string | null;
    /**
     * 
     * @type {BussDistrict}
     * @memberof PositionRecommendRequest
     */
    districtId?: BussDistrict;
    /**
     * 薪酬
     * @type {Array<string>}
     * @memberof PositionRecommendRequest
     */
    payment?: Array<string> | null;
    /**
     * 学历
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    requirementOfEducationDegree?: Array<number> | null;
    /**
     * 工龄
     * @type {string}
     * @memberof PositionRecommendRequest
     */
    workAge?: string | null;
    /**
     * 工作性质
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    workProperty?: Array<number> | null;
    /**
     * 单位性质
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    enterpriseProperty?: Array<number> | null;
    /**
     * 福利
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    welfare?: Array<number> | null;
    /**
     * 行业
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    positionIndustry?: Array<number> | null;
    /**
     * 紧急
     * @type {boolean}
     * @memberof PositionRecommendRequest
     */
    emergency?: boolean | null;
    /**
     * 规模
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    enterpriseEmployeeNumber?: Array<number> | null;
    /**
     * 职能
     * @type {Array<number>}
     * @memberof PositionRecommendRequest
     */
    positionCaree?: Array<number> | null;
    /**
     * 20240415 商圈
     * @type {Array<string>}
     * @memberof PositionRecommendRequest
     */
    businessDistinct?: Array<string> | null;
}
