/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PartTimePayMode } from './part-time-pay-mode';
import { PayUnit } from './pay-unit';
/**
 * 
 * @export
 * @interface DocumentEnterprisePositionItem
 */
export interface DocumentEnterprisePositionItem {
    /**
     * 
     * @type {number}
     * @memberof DocumentEnterprisePositionItem
     */
    positionID?: number;
    /**
     * 
     * @type {string}
     * @memberof DocumentEnterprisePositionItem
     */
    positionGuid?: string;
    /**
     * 职位名称
     * @type {string}
     * @memberof DocumentEnterprisePositionItem
     */
    positionName?: string | null;
    /**
     * 职位排序
     * @type {number}
     * @memberof DocumentEnterprisePositionItem
     */
    positionSort?: number;
    /**
     * 发布日期
     * @type {Date}
     * @memberof DocumentEnterprisePositionItem
     */
    publishTime?: Date;
    /**
     * 薪资
     * @type {string}
     * @memberof DocumentEnterprisePositionItem
     */
    payPackage?: string | null;
    /**
     * 工作性质(全职,兼职,钟点工,临时,实习)
     * @type {number}
     * @memberof DocumentEnterprisePositionItem
     */
    workProperty?: number;
    /**
     * 薪酬区间范围最小值
     * @type {number}
     * @memberof DocumentEnterprisePositionItem
     */
    payPackageFrom?: number;
    /**
     * 薪酬区间范围最大值
     * @type {number}
     * @memberof DocumentEnterprisePositionItem
     */
    payPackageTo?: number;
    /**
     * 薪酬月份
     * @type {number}
     * @memberof DocumentEnterprisePositionItem
     */
    payMonth?: number | null;
    /**
     * 
     * @type {PartTimePayMode}
     * @memberof DocumentEnterprisePositionItem
     */
    partTimePayMode?: PartTimePayMode;
    /**
     * 
     * @type {PayUnit}
     * @memberof DocumentEnterprisePositionItem
     */
    payUnit?: PayUnit;
}
