/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface O2OPositionEntry
 */
export interface O2OPositionEntry {
    /**
     * 
     * @type {number}
     * @memberof O2OPositionEntry
     */
    deliverId?: number;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    positionGuid?: string;
    /**
     * 
     * @type {number}
     * @memberof O2OPositionEntry
     */
    positionId?: number;
    /**
     * 
     * @type {number}
     * @memberof O2OPositionEntry
     */
    enterpriseId?: number;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    enterpriseName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof O2OPositionEntry
     */
    personRequired?: number;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    workAgeRequired?: string | null;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    payment?: string | null;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    majorRequired?: string | null;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    degreeRequired?: string | null;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    introduction?: string | null;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    workAt?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isGraduate?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isTrainee?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isParttime?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isDelived?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isFavorited?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    canDeliver?: boolean;
    /**
     * 是否允许报名
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    registrationPositionEnable?: boolean;
    /**
     * 是否已查看
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isDeliverView?: boolean;
    /**
     * 是否已申请
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isApply?: boolean;
    /**
     * 是否o2o
     * @type {boolean}
     * @memberof O2OPositionEntry
     */
    isO2O?: boolean;
    /**
     * 
     * @type {number}
     * @memberof O2OPositionEntry
     */
    deliveryOrigin?: number;
    /**
     * 
     * @type {string}
     * @memberof O2OPositionEntry
     */
    deliverText?: string | null;
}
