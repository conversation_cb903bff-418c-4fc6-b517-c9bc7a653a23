/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexEnterpriseItemDto } from './index-enterprise-item-dto';
/**
 * 
 * @export
 * @interface PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
 */
export interface PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<IndexEnterpriseItemDto>}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    items?: Array<IndexEnterpriseItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    hasNextPages?: boolean;
    /**
     * 
     * @type {Array<IndexEnterpriseItemDto>}
     * @memberof PagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto
     */
    emptyItems?: Array<IndexEnterpriseItemDto> | null;
}
