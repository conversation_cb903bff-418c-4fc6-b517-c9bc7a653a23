/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { BussDistrict } from './buss-district';
import { SortingSets } from './sorting-sets';
/**
 * 园区职位搜索请求参数
 * @export
 * @interface ParkPositionRequest
 */
export interface ParkPositionRequest {
    /**
     * 搜索的id，加载页码大于1的分页数据，传递用来滑动加载上一条、下一条，加载第一页不需要传递
     * @type {string}
     * @memberof ParkPositionRequest
     */
    searchId?: string | null;
    /**
     * 是否开启滑动加载
     * @type {boolean}
     * @memberof ParkPositionRequest
     */
    isOpenSlide?: boolean;
    /**
     * 
     * @type {BussDistrict}
     * @memberof ParkPositionRequest
     */
    districtID?: BussDistrict;
    /**
     * 
     * @type {BussDistrict}
     * @memberof ParkPositionRequest
     */
    districtFilterID?: BussDistrict;
    /**
     * 关键字
     * @type {string}
     * @memberof ParkPositionRequest
     */
    keyword?: string | null;
    /**
     * 过滤特殊的关键字，用逗号隔开 (经理,主管,管培,)
     * @type {string}
     * @memberof ParkPositionRequest
     */
    eliminateKeyword?: string | null;
    /**
     * 指定关键字
     * @type {string}
     * @memberof ParkPositionRequest
     */
    mustContianKeyword?: string | null;
    /**
     * 工作性质
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    workProperty?: Array<number> | null;
    /**
     * 单位性质
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    enterpriseProperty?: Array<number> | null;
    /**
     * 福利
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    welfare?: Array<number> | null;
    /**
     * 特定的职位
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    positionIDs?: Array<number> | null;
    /**
     * 去掉特定的职位 20230807
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    noPositionIDs?: Array<number> | null;
    /**
     * 规模
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    enterpriseEmployeeNumber?: Array<number> | null;
    /**
     * 工龄
     * @type {string}
     * @memberof ParkPositionRequest
     */
    workAge?: string | null;
    /**
     * 学历
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    requirementOfEducationDegree?: Array<number> | null;
    /**
     * 首发日
     * @type {string}
     * @memberof ParkPositionRequest
     */
    firstPublishDate?: string | null;
    /**
     * 在线
     * @type {boolean}
     * @memberof ParkPositionRequest
     */
    online?: boolean | null;
    /**
     * 紧急
     * @type {boolean}
     * @memberof ParkPositionRequest
     */
    emergency?: boolean | null;
    /**
     * 距离
     * @type {string}
     * @memberof ParkPositionRequest
     */
    distance?: string | null;
    /**
     * 坐标，格式：lat, lon
     * @type {Array<string>}
     * @memberof ParkPositionRequest
     */
    location?: Array<string> | null;
    /**
     * 20240415 商圈
     * @type {Array<string>}
     * @memberof ParkPositionRequest
     */
    businessDistinct?: Array<string> | null;
    /**
     * 薪酬
     * @type {Array<string>}
     * @memberof ParkPositionRequest
     */
    payment?: Array<string> | null;
    /**
     * 工作地
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    workPlace?: Array<number> | null;
    /**
     * 20220708 毕业生加入专业搜索
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    educationSpecialty?: Array<number> | null;
    /**
     * 职能
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    positionCaree?: Array<number> | null;
    /**
     * 行业
     * @type {Array<number>}
     * @memberof ParkPositionRequest
     */
    positionIndustry?: Array<number> | null;
    /**
     * 是否接收毕业生
     * @type {boolean}
     * @memberof ParkPositionRequest
     */
    isReceiveGraduate?: boolean;
    /**
     * 分页
     * @type {number}
     * @memberof ParkPositionRequest
     */
    page?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof ParkPositionRequest
     */
    pageSize?: number;
    /**
     * 
     * @type {SortingSets}
     * @memberof ParkPositionRequest
     */
    orderBy?: SortingSets;
    /**
     * 1=高亮，0=不高亮
     * @type {number}
     * @memberof ParkPositionRequest
     */
    highlight?: number;
    /**
     * 过滤曝光职位 0:显示所有职位 1:只显示曝光职位
     * @type {number}
     * @memberof ParkPositionRequest
     */
    exposure?: number | null;
    /**
     * 20231127 社区推荐职位ES修改 0:显示全部职位  1:过滤社区推荐职位
     * @type {number}
     * @memberof ParkPositionRequest
     */
    isSocialPosition?: number | null;
    /**
     * 园区guid
     * @type {string}
     * @memberof ParkPositionRequest
     */
    parkGuid?: string | null;
    /**
     * 园区ID
     * @type {number}
     * @memberof ParkPositionRequest
     */
    parkId?: number | null;
}
