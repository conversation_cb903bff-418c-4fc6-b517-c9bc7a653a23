/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { BussDistrict } from './buss-district';
/**
 * 求职者报名表
 * @export
 * @interface JobseekerApplication
 */
export interface JobseekerApplication {
    /**
     * 
     * @type {number}
     * @memberof JobseekerApplication
     */
    id?: number;
    /**
     * 
     * @type {BussDistrict}
     * @memberof JobseekerApplication
     */
    district?: BussDistrict;
    /**
     * 报名时间
     * @type {Date}
     * @memberof JobseekerApplication
     */
    createTime?: Date;
    /**
     * 联系电话
     * @type {string}
     * @memberof JobseekerApplication
     */
    phone?: string | null;
    /**
     * 微信同号
     * @type {boolean}
     * @memberof JobseekerApplication
     */
    phoneIsWechat?: boolean | null;
    /**
     * 报名求职者id
     * @type {number}
     * @memberof JobseekerApplication
     */
    jobseekerId?: number | null;
    /**
     * 联系人姓名
     * @type {string}
     * @memberof JobseekerApplication
     */
    name?: string | null;
    /**
     * 是否处理
     * @type {boolean}
     * @memberof JobseekerApplication
     */
    isDeal?: boolean;
    /**
     * 处理人
     * @type {string}
     * @memberof JobseekerApplication
     */
    dealPerson?: string | null;
    /**
     * 联系人年龄
     * @type {number}
     * @memberof JobseekerApplication
     */
    age?: number | null;
    /**
     * 联系人性别 0男 1女
     * @type {number}
     * @memberof JobseekerApplication
     */
    sex?: number | null;
    /**
     * 粤语程度  0  不会听粤语、不会说粤语 1 会听粤语，不会说粤语  2 会听粤语、说粤语 （流利对答）
     * @type {number}
     * @memberof JobseekerApplication
     */
    cantoneseDegree?: number | null;
    /**
     * 求职意向 （岗位）  直接传文本， 多选则逗号分隔 ,
     * @type {string}
     * @memberof JobseekerApplication
     */
    jobIntention?: string | null;
    /**
     * 渠道来源  0 pc 1小红书、2企业微信、3app、4公众号   5抖音、6宣传品
     * @type {number}
     * @memberof JobseekerApplication
     */
    channelSource?: number | null;
}
