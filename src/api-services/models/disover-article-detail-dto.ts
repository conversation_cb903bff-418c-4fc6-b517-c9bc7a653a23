/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 发现列表页咨询项
 * @export
 * @interface DisoverArticleDetailDto
 */
export interface DisoverArticleDetailDto {
    /**
     * 文章标题
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    title?: string | null;
    /**
     * 文章图片
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    imgUrl?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof DisoverArticleDetailDto
     */
    date?: Date;
    /**
     * 阅读数
     * @type {number}
     * @memberof DisoverArticleDetailDto
     */
    count?: number;
    /**
     * Load文章页地址
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    link?: string | null;
    /**
     * ArticleId
     * @type {number}
     * @memberof DisoverArticleDetailDto
     */
    articleID?: number;
    /**
     * guid
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    articleGuid?: string;
    /**
     * 文章内容
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    content?: string | null;
    /**
     * 关键词
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    keyWords?: string | null;
    /**
     * 点赞数
     * @type {number}
     * @memberof DisoverArticleDetailDto
     */
    likesCount?: number;
    /**
     * 收藏数
     * @type {number}
     * @memberof DisoverArticleDetailDto
     */
    collectCount?: number;
    /**
     * 头像展示地址
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    logoImgUrl?: string | null;
    /**
     * 来自
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    fromName?: string | null;
    /**
     * 昵称类型
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    nickName?: string | null;
    /**
     * 是否收藏
     * @type {boolean}
     * @memberof DisoverArticleDetailDto
     */
    isCollect?: boolean;
    /**
     * 是否点赞
     * @type {boolean}
     * @memberof DisoverArticleDetailDto
     */
    isLikes?: boolean;
    /**
     * 评论数
     * @type {number}
     * @memberof DisoverArticleDetailDto
     */
    commentCount?: number;
    /**
     * 文章类型id（栏目名）
     * @type {number}
     * @memberof DisoverArticleDetailDto
     */
    articleCategoryID?: number;
    /**
     * 文章类型（栏目名）
     * @type {string}
     * @memberof DisoverArticleDetailDto
     */
    articleCategoryName?: string | null;
}
