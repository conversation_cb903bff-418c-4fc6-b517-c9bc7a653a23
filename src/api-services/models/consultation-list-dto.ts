/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 咨询列表返回数据
 * @export
 * @interface ConsultationListDto
 */
export interface ConsultationListDto {
    /**
     * 咨询信息ID
     * @type {string}
     * @memberof ConsultationListDto
     */
    consultationId?: string | null;
    /**
     * 指导类型（字典值）
     * @type {number}
     * @memberof ConsultationListDto
     */
    guidanceType?: number;
    /**
     * 咨询时间
     * @type {string}
     * @memberof ConsultationListDto
     */
    consultationTime?: string | null;
    /**
     * 证件号码
     * @type {string}
     * @memberof ConsultationListDto
     */
    idNumber?: string | null;
    /**
     * 联系电话
     * @type {string}
     * @memberof ConsultationListDto
     */
    contactPhone?: string | null;
    /**
     * 咨询服务类别（字典值）
     * @type {number}
     * @memberof ConsultationListDto
     */
    consultationServiceCategory?: number;
    /**
     * 咨询问题描述
     * @type {string}
     * @memberof ConsultationListDto
     */
    consultationProblemDescription?: string | null;
    /**
     * 是否已答复（0-未答复，1-已答复）
     * @type {number}
     * @memberof ConsultationListDto
     */
    hasReplied?: number;
    /**
     * 信息来源（字典值）
     * @type {number}
     * @memberof ConsultationListDto
     */
    informationSource?: number;
    /**
     * 答复专家姓名
     * @type {string}
     * @memberof ConsultationListDto
     */
    replyExpertName?: string | null;
    /**
     * 咨询人姓名/单位名称
     * @type {string}
     * @memberof ConsultationListDto
     */
    consultantName?: string | null;
    /**
     * 人员类别（字典值）
     * @type {number}
     * @memberof ConsultationListDto
     */
    personnelCategory?: number;
    /**
     * 邮箱
     * @type {string}
     * @memberof ConsultationListDto
     */
    email?: string | null;
    /**
     * 指定专家姓名
     * @type {string}
     * @memberof ConsultationListDto
     */
    assignedExpertName?: string | null;
    /**
     * 测评分数
     * @type {string}
     * @memberof ConsultationListDto
     */
    evaluationScore?: string | null;
    /**
     * 答复时间
     * @type {string}
     * @memberof ConsultationListDto
     */
    replyTime?: string | null;
    /**
     * 答复内容
     * @type {string}
     * @memberof ConsultationListDto
     */
    replyContent?: string | null;
    /**
     * 咨询者反馈内容是否有用
     * @type {boolean}
     * @memberof ConsultationListDto
     */
    consultantFeedbackUseful?: boolean | null;
    /**
     * 咨询者反馈内容
     * @type {string}
     * @memberof ConsultationListDto
     */
    consultantFeedbackContent?: string | null;
    /**
     * 求职者Id
     * @type {number}
     * @memberof ConsultationListDto
     */
    jobseekerId?: number;
    /**
     * 企业Id
     * @type {number}
     * @memberof ConsultationListDto
     */
    entepriseId?: number;
}
