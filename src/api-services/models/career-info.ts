/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface CareerInfo
 */
export interface CareerInfo {
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    resumeID?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    position?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    positionID?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    industry1?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    industryID1?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    industry2?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    industryID2?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    industry3?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    industryID3?: number;
    /**
     * 期望工作地
     * @type {number}
     * @memberof CareerInfo
     */
    residencyID?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    residency?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    residencyID2?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    residency2?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    residencyID3?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    residency3?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CareerInfo
     */
    salary?: number;
    /**
     * 求职状态
     * @type {number}
     * @memberof CareerInfo
     */
    workStatusID?: number;
    /**
     * 
     * @type {string}
     * @memberof CareerInfo
     */
    workStatus?: string | null;
}
