/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingPositionListItem } from './meeting-position-list-item';
/**
 * 
 * @export
 * @interface MeetingEnterpriseListItem
 */
export interface MeetingEnterpriseListItem {
    /**
     * 企业ID
     * @type {number}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseID?: number;
    /**
     * 企业名称 高亮
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseName?: string | null;
    /**
     * 企业名称 不高亮
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseNameOriginal?: string | null;
    /**
     * 是否展示展位号
     * @type {boolean}
     * @memberof MeetingEnterpriseListItem
     */
    isShowBoothsNo?: boolean;
    /**
     * 场馆数
     * @type {number}
     * @memberof MeetingEnterpriseListItem
     */
    attendMeetingPlaceCount?: number;
    /**
     * 展位号
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    seatNum?: string | null;
    /**
     * 展位号
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    oldSeatNum?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseProperty?: string | null;
    /**
     * 单位性质ID
     * @type {number}
     * @memberof MeetingEnterpriseListItem
     */
    enterprisePropertyId?: number;
    /**
     * 单位行业
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseIndustry?: string | null;
    /**
     * 单位规模
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 单位说明
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseIntroduction?: string | null;
    /**
     * 企业Logo
     * @type {string}
     * @memberof MeetingEnterpriseListItem
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 职位列表
     * @type {Array<MeetingPositionListItem>}
     * @memberof MeetingEnterpriseListItem
     */
    positions?: Array<MeetingPositionListItem> | null;
}
