/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EConsultationFromType } from './econsultation-from-type';
import { ESchoolO2OConsultationType } from './eschool-o2-oconsultation-type';
/**
 * 
 * @export
 * @interface ConsultationItemOutput
 */
export interface ConsultationItemOutput {
    /**
     * 
     * @type {ESchoolO2OConsultationType}
     * @memberof ConsultationItemOutput
     */
    type?: ESchoolO2OConsultationType;
    /**
     * 
     * @type {EConsultationFromType}
     * @memberof ConsultationItemOutput
     */
    fromType?: EConsultationFromType;
    /**
     * 
     * @type {number}
     * @memberof ConsultationItemOutput
     */
    typeId?: number;
    /**
     * 文章ID
     * @type {number}
     * @memberof ConsultationItemOutput
     */
    articleID?: number;
    /**
     * 文章guid
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleGuid?: string | null;
    /**
     * 文章标题
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleTitle?: string | null;
    /**
     * 文章链接
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleUrl?: string | null;
    /**
     * App端文章链接
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleAppUrl?: string | null;
    /**
     * 关键词
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleKeyWords?: string | null;
    /**
     * 文章浏览量
     * @type {number}
     * @memberof ConsultationItemOutput
     */
    articleHits?: number;
    /**
     * 文章发布日期
     * @type {Date}
     * @memberof ConsultationItemOutput
     */
    articlePublishTime?: Date | null;
    /**
     * logo
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleLogo?: string | null;
    /**
     * url地址，不包括域名
     * @type {string}
     * @memberof ConsultationItemOutput
     */
    articleImagePhysicalName?: string | null;
}
