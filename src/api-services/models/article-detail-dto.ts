/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 文章详情
 * @export
 * @interface ArticleDetailDto
 */
export interface ArticleDetailDto {
    /**
     * 文章ID
     * @type {number}
     * @memberof ArticleDetailDto
     */
    articleID?: number;
    /**
     * 文章标题
     * @type {string}
     * @memberof ArticleDetailDto
     */
    articleTitle?: string | null;
    /**
     * 文章内容
     * @type {string}
     * @memberof ArticleDetailDto
     */
    articleContent?: string | null;
    /**
     * guid
     * @type {string}
     * @memberof ArticleDetailDto
     */
    articleGuid?: string | null;
    /**
     * 文章作者
     * @type {string}
     * @memberof ArticleDetailDto
     */
    articleAuthor?: string | null;
    /**
     * 文章来源
     * @type {string}
     * @memberof ArticleDetailDto
     */
    articleFrom?: string | null;
    /**
     * 是否发布
     * @type {boolean}
     * @memberof ArticleDetailDto
     */
    articlePubishFlag?: boolean | null;
    /**
     * 发布时间
     * @type {Date}
     * @memberof ArticleDetailDto
     */
    articlePublishTime?: Date | null;
    /**
     * 文章阅读数
     * @type {number}
     * @memberof ArticleDetailDto
     */
    articleHits?: number;
    /**
     * 文章职位名称列表
     * @type {Array<string>}
     * @memberof ArticleDetailDto
     */
    articlePositions?: Array<string> | null;
    /**
     * 是否显示关联职位
     * @type {boolean}
     * @memberof ArticleDetailDto
     */
    showPosition?: boolean;
    /**
     * 文章类型id（栏目名）
     * @type {number}
     * @memberof ArticleDetailDto
     */
    articleCategoryID?: number;
    /**
     * 文章类型（栏目名）
     * @type {string}
     * @memberof ArticleDetailDto
     */
    articleCategoryName?: string | null;
}
