/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PartTimeSettlementMode } from './part-time-settlement-mode';
import { PayUnit } from './pay-unit';
/**
 * 职位基本信息
 * @export
 * @interface HomeEnterprisePositionItemInfo
 */
export interface HomeEnterprisePositionItemInfo {
    /**
     * 职位ID
     * @type {number}
     * @memberof HomeEnterprisePositionItemInfo
     */
    positionId?: number;
    /**
     * 职位GUID
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    positionGuid?: string;
    /**
     * 职位名称
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    name?: string | null;
    /**
     * 薪资范围
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    salaryRange?: string | null;
    /**
     * 工作地点
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    location?: string | null;
    /**
     * 工作经验要求
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    requirementOfWorkAge?: string | null;
    /**
     * 学历要求
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    requirementOfEducationDegree?: string | null;
    /**
     * 是否急聘
     * @type {boolean}
     * @memberof HomeEnterprisePositionItemInfo
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 是否是毕业生职位
     * @type {boolean}
     * @memberof HomeEnterprisePositionItemInfo
     */
    isReceiveGraduate?: boolean;
    /**
     * 工作性质代码(全职,兼职,钟点工,临时,实习)
     * @type {number}
     * @memberof HomeEnterprisePositionItemInfo
     */
    workProperty?: number;
    /**
     * 工作性质
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    workPropertyName?: string | null;
    /**
     * 招聘总人数
     * @type {number}
     * @memberof HomeEnterprisePositionItemInfo
     */
    positionAmount?: number;
    /**
     * 职位状态
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    positionState?: string | null;
    /**
     * 
     * @type {PayUnit}
     * @memberof HomeEnterprisePositionItemInfo
     */
    payUnit?: PayUnit;
    /**
     * 
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    payUnitName?: string | null;
    /**
     * 
     * @type {PartTimeSettlementMode}
     * @memberof HomeEnterprisePositionItemInfo
     */
    partTimeSettlementMode?: PartTimeSettlementMode;
    /**
     * 
     * @type {string}
     * @memberof HomeEnterprisePositionItemInfo
     */
    partTimeSettlementModeName?: string | null;
}
