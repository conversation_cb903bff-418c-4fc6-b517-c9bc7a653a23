/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 新增咨询参数
 * @export
 * @interface CreateConsultationInput
 */
export interface CreateConsultationInput {
    /**
     * 指导类型（字典值）
     * @type {number}
     * @memberof CreateConsultationInput
     */
    guidanceType: number;
    /**
     * 证件号码（身份证号等）
     * @type {string}
     * @memberof CreateConsultationInput
     */
    idNumber: string;
    /**
     * 联系电话
     * @type {string}
     * @memberof CreateConsultationInput
     */
    contactPhone: string;
    /**
     * 咨询服务类别（字典值）
     * @type {number}
     * @memberof CreateConsultationInput
     */
    consultationServiceCategory: number;
    /**
     * 咨询问题描述
     * @type {string}
     * @memberof CreateConsultationInput
     */
    consultationProblemDescription: string;
    /**
     * 信息来源（字典值）
     * @type {number}
     * @memberof CreateConsultationInput
     */
    informationSource: number;
    /**
     * 咨询人姓名/单位名称
     * @type {string}
     * @memberof CreateConsultationInput
     */
    consultantName: string;
    /**
     * 人员类别（字典值）
     * @type {number}
     * @memberof CreateConsultationInput
     */
    personnelCategory: number;
    /**
     * 邮箱（可为空）
     * @type {string}
     * @memberof CreateConsultationInput
     */
    email?: string | null;
    /**
     * 指定专家姓名（可为空）
     * @type {string}
     * @memberof CreateConsultationInput
     */
    assignedExpertName?: string | null;
    /**
     * 测评分数（可为空）
     * @type {string}
     * @memberof CreateConsultationInput
     */
    evaluationScore?: string | null;
}
