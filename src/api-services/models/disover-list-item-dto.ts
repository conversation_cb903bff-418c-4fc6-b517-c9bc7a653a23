/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 发现列表页咨询项
 * @export
 * @interface DisoverListItemDto
 */
export interface DisoverListItemDto {
    /**
     * 文章标题
     * @type {string}
     * @memberof DisoverListItemDto
     */
    title?: string | null;
    /**
     * 文章图片
     * @type {string}
     * @memberof DisoverListItemDto
     */
    imgUrl?: string | null;
    /**
     * 头像展示地址
     * @type {string}
     * @memberof DisoverListItemDto
     */
    logoImgUrl?: string | null;
    /**
     * 文章内容
     * @type {string}
     * @memberof DisoverListItemDto
     */
    content?: string | null;
    /**
     * 关键词
     * @type {string}
     * @memberof DisoverListItemDto
     */
    articleSummary?: string | null;
    /**
     * 来自
     * @type {string}
     * @memberof DisoverListItemDto
     */
    fromName?: string | null;
    /**
     * 昵称类型
     * @type {string}
     * @memberof DisoverListItemDto
     */
    nickName?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof DisoverListItemDto
     */
    date?: Date;
    /**
     * 阅读数
     * @type {number}
     * @memberof DisoverListItemDto
     */
    count?: number;
    /**
     * 阅读数
     * @type {number}
     * @memberof DisoverListItemDto
     */
    reviewCount?: number;
    /**
     * 点赞数
     * @type {number}
     * @memberof DisoverListItemDto
     */
    likesCount?: number;
    /**
     * 收藏数
     * @type {number}
     * @memberof DisoverListItemDto
     */
    collectCount?: number;
    /**
     * 是否点赞
     * @type {boolean}
     * @memberof DisoverListItemDto
     */
    isLikes?: boolean;
    /**
     * 是否收藏
     * @type {boolean}
     * @memberof DisoverListItemDto
     */
    isCollect?: boolean;
    /**
     * 链接
     * @type {string}
     * @memberof DisoverListItemDto
     */
    link?: string | null;
    /**
     * guid
     * @type {string}
     * @memberof DisoverListItemDto
     */
    articleGuid?: string;
    /**
     * ArticleID
     * @type {number}
     * @memberof DisoverListItemDto
     */
    articleID?: number;
    /**
     * 文章类型 0图片、1文章
     * @type {number}
     * @memberof DisoverListItemDto
     */
    type?: number;
    /**
     * 链接类型 0原生 1外链
     * @type {number}
     * @memberof DisoverListItemDto
     */
    linkType?: number;
    /**
     * 
     * @type {number}
     * @memberof DisoverListItemDto
     */
    articleCategoryID?: number;
    /**
     * 
     * @type {string}
     * @memberof DisoverListItemDto
     */
    articleCategoryName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DisoverListItemDto
     */
    articleCategoryNameShort?: string | null;
    /**
     * 评论量
     * @type {number}
     * @memberof DisoverListItemDto
     */
    commentCount?: number | null;
}
