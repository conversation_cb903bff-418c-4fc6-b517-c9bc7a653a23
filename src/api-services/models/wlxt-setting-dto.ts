/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface WlxtSettingDto
 */
export interface WlxtSettingDto {
    /**
     * 地市业务ID
     * @type {number}
     * @memberof WlxtSettingDto
     */
    bussDistrictID?: number;
    /**
     * 域名(绝对路径www.gxrc.com）
     * @type {string}
     * @memberof WlxtSettingDto
     */
    domainUrl?: string | null;
    /**
     * 触屏版域名
     * @type {string}
     * @memberof WlxtSettingDto
     */
    webAppHost?: string | null;
    /**
     * 个人后台(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    jobseekerCenterUrl?: string | null;
    /**
     * 搜索结果页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    jobSearchResultUrl?: string | null;
    /**
     * 默认首页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    defaultPageUrl?: string | null;
    /**
     * 搜索页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    jobSearchUrl?: string | null;
    /**
     * 现场招聘页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    senceUrl?: string | null;
    /**
     * 招聘简章页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    jobDetaillUrl?: string | null;
    /**
     * 文章列表页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    articleListUrl?: string | null;
    /**
     * 文章详细页(绝对)
     * @type {string}
     * @memberof WlxtSettingDto
     */
    articleUrl?: string | null;
    /**
     * 网站名称
     * @type {string}
     * @memberof WlxtSettingDto
     */
    webName?: string | null;
    /**
     * 标题
     * @type {string}
     * @memberof WlxtSettingDto
     */
    title?: string | null;
    /**
     * 关键字
     * @type {string}
     * @memberof WlxtSettingDto
     */
    keywords?: string | null;
    /**
     * 描述
     * @type {string}
     * @memberof WlxtSettingDto
     */
    description?: string | null;
    /**
     * Logo位置
     * @type {string}
     * @memberof WlxtSettingDto
     */
    logoUrl?: string | null;
    /**
     * 底部文件
     * @type {string}
     * @memberof WlxtSettingDto
     */
    footerText?: string | null;
    /**
     * 企业管理中心的Footer
     * @type {string}
     * @memberof WlxtSettingDto
     */
    entFooterText?: string | null;
    /**
     * 个人管理中心Footer
     * @type {string}
     * @memberof WlxtSettingDto
     */
    jobseekerFooterText?: string | null;
    /**
     * 个人后台栏目ID
     * @type {number}
     * @memberof WlxtSettingDto
     */
    jobseekerCatologID?: number | null;
    /**
     * 现场招聘ID
     * @type {string}
     * @memberof WlxtSettingDto
     */
    sceneCategoryID?: string | null;
    /**
     * 企业登录首页客户热线
     * @type {string}
     * @memberof WlxtSettingDto
     */
    enterDefaultPageCustomerPhone?: string | null;
    /**
     * 现场招聘列表页
     * @type {string}
     * @memberof WlxtSettingDto
     */
    senceListUrl?: string | null;
    /**
     * 该setting可以用的域名
     * @type {string}
     * @memberof WlxtSettingDto
     */
    usedDomain?: string | null;
    /**
     * 企业后台地址
     * @type {string}
     * @memberof WlxtSettingDto
     */
    enterpriseCenterUrl?: string | null;
    /**
     * 企业登陆页地址
     * @type {string}
     * @memberof WlxtSettingDto
     */
    enterpriseLoginUrl?: string | null;
    /**
     * 人才搜索页Url
     * @type {string}
     * @memberof WlxtSettingDto
     */
    jobSeekerSearchUrl?: string | null;
    /**
     * 友情链接
     * @type {string}
     * @memberof WlxtSettingDto
     */
    friendLinks?: string | null;
    /**
     * 企业注册特别提醒
     * @type {string}
     * @memberof WlxtSettingDto
     */
    regOkRemind?: string | null;
    /**
     * 现场招聘会招聘企业职位列表
     * @type {string}
     * @memberof WlxtSettingDto
     */
    sceneJobsUrl?: string | null;
    /**
     * 现场招聘会展位图
     * @type {string}
     * @memberof WlxtSettingDto
     */
    sceneMapUrl?: string | null;
    /**
     * 
     * @type {string}
     * @memberof WlxtSettingDto
     */
    resumeViewOfDistrict?: string | null;
}
