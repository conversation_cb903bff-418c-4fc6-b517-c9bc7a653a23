/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnterpriseVisualImageItemDto } from './enterprise-visual-image-item-dto';
import { LogoPositionItemDto } from './logo-position-item-dto';
/**
 * logo的企业实体
 * @export
 * @interface LogoEnterpriseParkItemDto
 */
export interface LogoEnterpriseParkItemDto {
    /**
     * logoid
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    logoID?: number;
    /**
     * 广告的pagedi
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    pageId?: number;
    /**
     * 企业id
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseID?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseName?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseProperty?: string | null;
    /**
     * 单位性质id
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterprisePropertyId?: number | null;
    /**
     * 企业形象照
     * @type {Array<EnterpriseVisualImageItemDto>}
     * @memberof LogoEnterpriseParkItemDto
     */
    visualImages?: Array<EnterpriseVisualImageItemDto> | null;
    /**
     * 企业Logo
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 单位人数
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 单位人数id
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseEmployeeNumberId?: number | null;
    /**
     * 单位行业id
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseIndustryId?: number | null;
    /**
     * 单位行业
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    enterpriseIndustry?: string | null;
    /**
     * logo的图片
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    imageUrl?: string | null;
    /**
     * 点击的转跳url
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    linkUrl?: string | null;
    /**
     * 是否有职位
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    positionCount?: number;
    /**
     * 招聘人数
     * @type {number}
     * @memberof LogoEnterpriseParkItemDto
     */
    positionAmount?: number;
    /**
     * Logo业务开始时间
     * @type {Date}
     * @memberof LogoEnterpriseParkItemDto
     */
    beginDate?: Date;
    /**
     * Logo业务结束时间
     * @type {Date}
     * @memberof LogoEnterpriseParkItemDto
     */
    endDate?: Date;
    /**
     * 是否是内部Logo
     * @type {boolean}
     * @memberof LogoEnterpriseParkItemDto
     */
    isPrivateLogo?: boolean;
    /**
     * 职位列表
     * @type {Array<LogoPositionItemDto>}
     * @memberof LogoEnterpriseParkItemDto
     */
    positions?: Array<LogoPositionItemDto> | null;
    /**
     * 显示在什么平台
     * @type {string}
     * @memberof LogoEnterpriseParkItemDto
     */
    showWhere?: string | null;
}
