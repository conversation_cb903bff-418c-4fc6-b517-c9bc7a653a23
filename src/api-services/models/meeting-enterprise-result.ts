/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingPositionResult } from './meeting-position-result';
/**
 * 全区交流大会企业
 * @export
 * @interface MeetingEnterpriseResult
 */
export interface MeetingEnterpriseResult {
    /**
     * 企业ID
     * @type {number}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseID?: number | null;
    /**
     * 企业名称
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseName?: string | null;
    /**
     * 企业描述
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseDescription?: string | null;
    /**
     * 展位
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    seatNum?: string | null;
    /**
     * 展厅
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    showroom?: string | null;
    /**
     * 展位
     * @type {Array<string>}
     * @memberof MeetingEnterpriseResult
     */
    booth?: Array<string> | null;
    /**
     * 企业规模
     * @type {number}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseEmployeeNumber?: number;
    /**
     * 单位性质
     * @type {number}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseProperty?: number;
    /**
     * 企业行业
     * @type {number}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseIndustry?: number;
    /**
     * 行业名称
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseIndustryName?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    enterprisePropertyName?: string | null;
    /**
     * 单位规模
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseEmployeeNumberName?: string | null;
    /**
     * 单位logo
     * @type {string}
     * @memberof MeetingEnterpriseResult
     */
    logoUrl?: string | null;
    /**
     * 地市网联ID
     * @type {number}
     * @memberof MeetingEnterpriseResult
     */
    enterpriseDistrictID?: number;
    /**
     * 职位列表
     * @type {Array<MeetingPositionResult>}
     * @memberof MeetingEnterpriseResult
     */
    positionList?: Array<MeetingPositionResult> | null;
}
