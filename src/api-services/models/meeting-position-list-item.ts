/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 招聘会 职位列表项
 * @export
 * @interface MeetingPositionListItem
 */
export interface MeetingPositionListItem {
    /**
     * 职位ID
     * @type {number}
     * @memberof MeetingPositionListItem
     */
    positionID?: number;
    /**
     * 职位名称 高亮
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    positionName?: string | null;
    /**
     * 职位名称 不高亮
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    positionNameOriginal?: string | null;
    /**
     * 工龄
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    workAge?: string | null;
    /**
     * 学历
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    degree?: string | null;
    /**
     * 招聘人数
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    amount?: string | null;
    /**
     * 工作地
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    workPlace?: string | null;
    /**
     * 薪资
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    payPackage?: string | null;
    /**
     * 职位IE描述
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    description?: string | null;
    /**
     * 职位排序
     * @type {number}
     * @memberof MeetingPositionListItem
     */
    positionSort?: number;
    /**
     * 是否接收毕业生
     * @type {boolean}
     * @memberof MeetingPositionListItem
     */
    isReceiveGraduate?: boolean;
    /**
     * 是否是兼职（0：不是兼职，1：是兼职）
     * @type {boolean}
     * @memberof MeetingPositionListItem
     */
    isParttime?: boolean;
    /**
     * 是否是实习职位(0:全职 1:实习)
     * @type {boolean}
     * @memberof MeetingPositionListItem
     */
    isTrainee?: boolean;
    /**
     * 是否投递
     * @type {boolean}
     * @memberof MeetingPositionListItem
     */
    isDelive?: boolean;
    /**
     * 能否投递(是否再投递时间范围内)
     * @type {boolean}
     * @memberof MeetingPositionListItem
     */
    isCanDeliver?: boolean;
    /**
     * 自定义薪资 20240413
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    payPackageCustomize?: string | null;
    /**
     * 自定义学历 20240513
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    educationDegreeCustomize?: string | null;
    /**
     * 自定义专业 20240513
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    educationSpecialtyCustomize?: string | null;
    /**
     * 自定义工作地 20240513
     * @type {string}
     * @memberof MeetingPositionListItem
     */
    workDistrictCustomize?: string | null;
}
