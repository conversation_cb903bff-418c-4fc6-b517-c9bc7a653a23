/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SubscribeCityOutput } from './subscribe-city-output';
import { SubscribePositionInfoInput } from './subscribe-position-info-input';
import { SubscribeSelectItemOutput } from './subscribe-select-item-output';
/**
 * 
 * @export
 * @interface SubscribePositionInfoOutput
 */
export interface SubscribePositionInfoOutput {
    /**
     * 主键
     * @type {number}
     * @memberof SubscribePositionInfoOutput
     */
    id?: number;
    /**
     * 订阅关键字
     * @type {string}
     * @memberof SubscribePositionInfoOutput
     */
    keyWord?: string | null;
    /**
     * 城市名称
     * @type {string}
     * @memberof SubscribePositionInfoOutput
     */
    workPlaceName?: string | null;
    /**
     * 搜索条件文本
     * @type {string}
     * @memberof SubscribePositionInfoOutput
     */
    searchRequestText?: string | null;
    /**
     * 
     * @type {SubscribePositionInfoInput}
     * @memberof SubscribePositionInfoOutput
     */
    search?: SubscribePositionInfoInput;
    /**
     * 选择的职位类型
     * @type {Array<SubscribeSelectItemOutput>}
     * @memberof SubscribePositionInfoOutput
     */
    positionCarees?: Array<SubscribeSelectItemOutput> | null;
    /**
     * 选择的行业类型
     * @type {Array<SubscribeSelectItemOutput>}
     * @memberof SubscribePositionInfoOutput
     */
    positionIndustry?: Array<SubscribeSelectItemOutput> | null;
    /**
     * 
     * @type {SubscribeCityOutput}
     * @memberof SubscribePositionInfoOutput
     */
    workPlace?: SubscribeCityOutput;
}
