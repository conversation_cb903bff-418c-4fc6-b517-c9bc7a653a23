/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { LogoPositionItemDto } from './logo-position-item-dto';
/**
 * logo的企业实体
 * @export
 * @interface LogoEnterpriseItemDto
 */
export interface LogoEnterpriseItemDto {
    /**
     * logoid
     * @type {number}
     * @memberof LogoEnterpriseItemDto
     */
    logoID?: number;
    /**
     * 广告的pagedi
     * @type {number}
     * @memberof LogoEnterpriseItemDto
     */
    pageId?: number;
    /**
     * 企业id
     * @type {number}
     * @memberof LogoEnterpriseItemDto
     */
    enterpriseID?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof LogoEnterpriseItemDto
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof LogoEnterpriseItemDto
     */
    enterpriseName?: string | null;
    /**
     * logo的图片
     * @type {string}
     * @memberof LogoEnterpriseItemDto
     */
    imageUrl?: string | null;
    /**
     * 点击的转跳url
     * @type {string}
     * @memberof LogoEnterpriseItemDto
     */
    linkUrl?: string | null;
    /**
     * 是否有职位
     * @type {number}
     * @memberof LogoEnterpriseItemDto
     */
    positionCount?: number;
    /**
     * 招聘人数
     * @type {number}
     * @memberof LogoEnterpriseItemDto
     */
    positionAmount?: number;
    /**
     * Logo业务开始时间
     * @type {Date}
     * @memberof LogoEnterpriseItemDto
     */
    beginDate?: Date;
    /**
     * Logo业务结束时间
     * @type {Date}
     * @memberof LogoEnterpriseItemDto
     */
    endDate?: Date;
    /**
     * 是否是内部Logo
     * @type {boolean}
     * @memberof LogoEnterpriseItemDto
     */
    isPrivateLogo?: boolean;
    /**
     * 职位列表
     * @type {Array<LogoPositionItemDto>}
     * @memberof LogoEnterpriseItemDto
     */
    positions?: Array<LogoPositionItemDto> | null;
    /**
     * 显示在什么平台
     * @type {string}
     * @memberof LogoEnterpriseItemDto
     */
    showWhere?: string | null;
}
