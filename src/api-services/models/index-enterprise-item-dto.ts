/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DocumentEnterprisePositionItem } from './document-enterprise-position-item';
/**
 * 
 * @export
 * @interface IndexEnterpriseItemDto
 */
export interface IndexEnterpriseItemDto {
    /**
     * 跟踪guid
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    trackingGuid?: string | null;
    /**
     * 企业ID
     * @type {number}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseID?: number;
    /**
     * 企业Guid
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseName?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseProperty?: string | null;
    /**
     * 单位性质ID
     * @type {number}
     * @memberof IndexEnterpriseItemDto
     */
    enterprisePropertyID?: number;
    /**
     * 雇佣人数
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof IndexEnterpriseItemDto
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 行业
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseIndustry?: string | null;
    /**
     * 联系地址
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseAddress?: string | null;
    /**
     * 网联归属
     * @type {number}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseDistrictID?: number;
    /**
     * logo
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    materialImpUrl?: string | null;
    /**
     * 职位列表
     * @type {Array<DocumentEnterprisePositionItem>}
     * @memberof IndexEnterpriseItemDto
     */
    positions?: Array<DocumentEnterprisePositionItem> | null;
    /**
     * 职位数量
     * @type {number}
     * @memberof IndexEnterpriseItemDto
     */
    positionCount?: number;
    /**
     * 职位数量
     * @type {number}
     * @memberof IndexEnterpriseItemDto
     */
    posiCountSearchCtrl?: number;
    /**
     * 企业Logo
     * @type {string}
     * @memberof IndexEnterpriseItemDto
     */
    enterpriseLogoUrl?: string | null;
}
