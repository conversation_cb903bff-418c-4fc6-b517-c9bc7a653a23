/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { LogoPositionItemDto } from './logo-position-item-dto';
/**
 * 
 * @export
 * @interface LogoEnterpriseParkItemShortDto
 */
export interface LogoEnterpriseParkItemShortDto {
    /**
     * logoid
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    logoID?: number;
    /**
     * 广告的pagedi
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    pageId?: number;
    /**
     * 企业id
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseID?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseName?: string | null;
    /**
     * logo的图片
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    imageUrl?: string | null;
    /**
     * 点击的转跳url
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    linkUrl?: string | null;
    /**
     * 招聘人数
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    positionAmount?: number;
    /**
     * Logo业务开始时间
     * @type {Date}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    beginDate?: Date;
    /**
     * Logo业务结束时间
     * @type {Date}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    endDate?: Date;
    /**
     * 是否是内部Logo
     * @type {boolean}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    isPrivateLogo?: boolean;
    /**
     * 职位列表
     * @type {Array<LogoPositionItemDto>}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    positions?: Array<LogoPositionItemDto> | null;
    /**
     * 显示在什么平台
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    showWhere?: string | null;
    /**
     * 单位人数
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 是否有职位
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    positionCount?: number;
    /**
     * 单位人数id
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseEmployeeNumberId?: number | null;
    /**
     * 单位行业id
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseIndustryId?: number | null;
    /**
     * 单位行业
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseIndustry?: string | null;
    /**
     * logo
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterpriseProperty?: string | null;
    /**
     * 单位性质id
     * @type {number}
     * @memberof LogoEnterpriseParkItemShortDto
     */
    enterprisePropertyId?: number | null;
}
