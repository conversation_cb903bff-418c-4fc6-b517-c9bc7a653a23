/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ReceiveCouponFromActivityInput } from '../models';
import { ReceiveCouponInput } from '../models';
import { RestfulResultCouponActivityOutput } from '../models';
/**
 * ConponApi - axios parameter creator
 * @export
 */
export const ConponApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取活动的详细信息
         * @param {string} [activityGuid] 活动的唯一标识
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConponGetCouponActivityGet: async (activityGuid?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Conpon/GetCouponActivity`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (activityGuid !== undefined) {
                localVarQueryParameter['activityGuid'] = activityGuid;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 领取活动的优惠券
         * @param {ReceiveCouponFromActivityInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConponReceiveCouponFromActivityPost: async (body?: ReceiveCouponFromActivityInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Conpon/ReceiveCouponFromActivity`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 领取活动的优惠券
         * @param {ReceiveCouponInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConponReceiveCouponPost: async (body?: ReceiveCouponInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Conpon/ReceiveCoupon`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ConponApi - functional programming interface
 * @export
 */
export const ConponApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取活动的详细信息
         * @param {string} [activityGuid] 活动的唯一标识
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConponGetCouponActivityGet(activityGuid?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultCouponActivityOutput>>> {
            const localVarAxiosArgs = await ConponApiAxiosParamCreator(configuration).apiConponGetCouponActivityGet(activityGuid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 领取活动的优惠券
         * @param {ReceiveCouponFromActivityInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConponReceiveCouponFromActivityPost(body?: ReceiveCouponFromActivityInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ConponApiAxiosParamCreator(configuration).apiConponReceiveCouponFromActivityPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 领取活动的优惠券
         * @param {ReceiveCouponInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConponReceiveCouponPost(body?: ReceiveCouponInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await ConponApiAxiosParamCreator(configuration).apiConponReceiveCouponPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ConponApi - factory interface
 * @export
 */
export const ConponApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取活动的详细信息
         * @param {string} [activityGuid] 活动的唯一标识
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConponGetCouponActivityGet(activityGuid?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultCouponActivityOutput>> {
            return ConponApiFp(configuration).apiConponGetCouponActivityGet(activityGuid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 领取活动的优惠券
         * @param {ReceiveCouponFromActivityInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConponReceiveCouponFromActivityPost(body?: ReceiveCouponFromActivityInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ConponApiFp(configuration).apiConponReceiveCouponFromActivityPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 领取活动的优惠券
         * @param {ReceiveCouponInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConponReceiveCouponPost(body?: ReceiveCouponInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return ConponApiFp(configuration).apiConponReceiveCouponPost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ConponApi - object-oriented interface
 * @export
 * @class ConponApi
 * @extends {BaseAPI}
 */
export class ConponApi extends BaseAPI {
    /**
     * 
     * @summary 获取活动的详细信息
     * @param {string} [activityGuid] 活动的唯一标识
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConponApi
     */
    public async apiConponGetCouponActivityGet(activityGuid?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultCouponActivityOutput>> {
        return ConponApiFp(this.configuration).apiConponGetCouponActivityGet(activityGuid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 领取活动的优惠券
     * @param {ReceiveCouponFromActivityInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConponApi
     */
    public async apiConponReceiveCouponFromActivityPost(body?: ReceiveCouponFromActivityInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ConponApiFp(this.configuration).apiConponReceiveCouponFromActivityPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 领取活动的优惠券
     * @param {ReceiveCouponInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConponApi
     */
    public async apiConponReceiveCouponPost(body?: ReceiveCouponInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return ConponApiFp(this.configuration).apiConponReceiveCouponPost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
