/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { BussDistrict } from '../models';
import { RestfulResultObject } from '../models';
import { RestfulResultShareConfigDto } from '../models';
/**
 * ConfigurationApi - axios parameter creator
 * @export
 */
export const ConfigurationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取微信分享配置
         * @param {string} [url] 
         * @param {string} [callback] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConfigurationShareConfigGet: async (url?: string, callback?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Configuration/ShareConfig`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (url !== undefined) {
                localVarQueryParameter['url'] = url;
            }

            if (callback !== undefined) {
                localVarQueryParameter['callback'] = callback;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {BussDistrict} [districtId] &lt;br /&gt;&amp;nbsp; 广西 &#x3D; 0&lt;br /&gt;&amp;nbsp; 桂林 &#x3D; 1&lt;br /&gt;&amp;nbsp; 柳州 &#x3D; 2&lt;br /&gt;&amp;nbsp; 梧州 &#x3D; 4&lt;br /&gt;&amp;nbsp; 桂平 &#x3D; 5&lt;br /&gt;&amp;nbsp; 百色 &#x3D; 6&lt;br /&gt;&amp;nbsp; 钦州 &#x3D; 7&lt;br /&gt;&amp;nbsp; 河池 &#x3D; 8&lt;br /&gt;&amp;nbsp; 北海 &#x3D; 9&lt;br /&gt;&amp;nbsp; 桂兴 &#x3D; 10&lt;br /&gt;&amp;nbsp; 防港 &#x3D; 11&lt;br /&gt;&amp;nbsp; 玉林 &#x3D; 12&lt;br /&gt;&amp;nbsp; 崇左 &#x3D; 13&lt;br /&gt;&amp;nbsp; 贵港 &#x3D; 14&lt;br /&gt;&amp;nbsp; 来宾 &#x3D; 15&lt;br /&gt;&amp;nbsp; 合浦 &#x3D; 16&lt;br /&gt;&amp;nbsp; 永福 &#x3D; 17&lt;br /&gt;&amp;nbsp; 贺州 &#x3D; 18&lt;br /&gt;&amp;nbsp; 南宁 &#x3D; 19&lt;br /&gt;&amp;nbsp; 平南 &#x3D; 20&lt;br /&gt;&amp;nbsp; 毕业生频道 &#x3D; 10000&lt;br /&gt;&amp;nbsp; 桂林毕业生就业服务网 &#x3D; 10001&lt;br /&gt;&amp;nbsp; 柳州毕业生就业服务网 &#x3D; 10002&lt;br /&gt;&amp;nbsp; 梧州毕业生就业服务网 &#x3D; 10004&lt;br /&gt;&amp;nbsp; 桂平毕业生就业服务网 &#x3D; 10005&lt;br /&gt;&amp;nbsp; 百色毕业生就业服务网 &#x3D; 10006&lt;br /&gt;&amp;nbsp; 钦州毕业生就业服务网 &#x3D; 10007&lt;br /&gt;&amp;nbsp; 河池毕业生就业服务网 &#x3D; 10008&lt;br /&gt;&amp;nbsp; 北海毕业生就业服务网 &#x3D; 10009&lt;br /&gt;&amp;nbsp; 防城毕业生就业服务网 &#x3D; 10011&lt;br /&gt;&amp;nbsp; 玉林毕业生就业服务网 &#x3D; 10012&lt;br /&gt;&amp;nbsp; 崇左毕业生就业服务网 &#x3D; 10013&lt;br /&gt;&amp;nbsp; 贵港毕业生就业服务网 &#x3D; 10014&lt;br /&gt;&amp;nbsp; 来宾毕业生就业服务网 &#x3D; 10015&lt;br /&gt;&amp;nbsp; 贺州毕业生就业服务网 &#x3D; 10018&lt;br /&gt;&amp;nbsp; 南宁毕业生就业服务网 &#x3D; 10019&lt;br /&gt;&amp;nbsp; 平南毕业生就业服务网 &#x3D; 10020&lt;br /&gt;&amp;nbsp; 军培 &#x3D; 10021&lt;br /&gt;&amp;nbsp; 所有 &#x3D; -1&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConfigurationWlSettingGet: async (districtId?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Configuration/WlSetting`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ConfigurationApi - functional programming interface
 * @export
 */
export const ConfigurationApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取微信分享配置
         * @param {string} [url] 
         * @param {string} [callback] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationShareConfigGet(url?: string, callback?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultShareConfigDto>>> {
            const localVarAxiosArgs = await ConfigurationApiAxiosParamCreator(configuration).apiConfigurationShareConfigGet(url, callback, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {BussDistrict} [districtId] &lt;br /&gt;&amp;nbsp; 广西 &#x3D; 0&lt;br /&gt;&amp;nbsp; 桂林 &#x3D; 1&lt;br /&gt;&amp;nbsp; 柳州 &#x3D; 2&lt;br /&gt;&amp;nbsp; 梧州 &#x3D; 4&lt;br /&gt;&amp;nbsp; 桂平 &#x3D; 5&lt;br /&gt;&amp;nbsp; 百色 &#x3D; 6&lt;br /&gt;&amp;nbsp; 钦州 &#x3D; 7&lt;br /&gt;&amp;nbsp; 河池 &#x3D; 8&lt;br /&gt;&amp;nbsp; 北海 &#x3D; 9&lt;br /&gt;&amp;nbsp; 桂兴 &#x3D; 10&lt;br /&gt;&amp;nbsp; 防港 &#x3D; 11&lt;br /&gt;&amp;nbsp; 玉林 &#x3D; 12&lt;br /&gt;&amp;nbsp; 崇左 &#x3D; 13&lt;br /&gt;&amp;nbsp; 贵港 &#x3D; 14&lt;br /&gt;&amp;nbsp; 来宾 &#x3D; 15&lt;br /&gt;&amp;nbsp; 合浦 &#x3D; 16&lt;br /&gt;&amp;nbsp; 永福 &#x3D; 17&lt;br /&gt;&amp;nbsp; 贺州 &#x3D; 18&lt;br /&gt;&amp;nbsp; 南宁 &#x3D; 19&lt;br /&gt;&amp;nbsp; 平南 &#x3D; 20&lt;br /&gt;&amp;nbsp; 毕业生频道 &#x3D; 10000&lt;br /&gt;&amp;nbsp; 桂林毕业生就业服务网 &#x3D; 10001&lt;br /&gt;&amp;nbsp; 柳州毕业生就业服务网 &#x3D; 10002&lt;br /&gt;&amp;nbsp; 梧州毕业生就业服务网 &#x3D; 10004&lt;br /&gt;&amp;nbsp; 桂平毕业生就业服务网 &#x3D; 10005&lt;br /&gt;&amp;nbsp; 百色毕业生就业服务网 &#x3D; 10006&lt;br /&gt;&amp;nbsp; 钦州毕业生就业服务网 &#x3D; 10007&lt;br /&gt;&amp;nbsp; 河池毕业生就业服务网 &#x3D; 10008&lt;br /&gt;&amp;nbsp; 北海毕业生就业服务网 &#x3D; 10009&lt;br /&gt;&amp;nbsp; 防城毕业生就业服务网 &#x3D; 10011&lt;br /&gt;&amp;nbsp; 玉林毕业生就业服务网 &#x3D; 10012&lt;br /&gt;&amp;nbsp; 崇左毕业生就业服务网 &#x3D; 10013&lt;br /&gt;&amp;nbsp; 贵港毕业生就业服务网 &#x3D; 10014&lt;br /&gt;&amp;nbsp; 来宾毕业生就业服务网 &#x3D; 10015&lt;br /&gt;&amp;nbsp; 贺州毕业生就业服务网 &#x3D; 10018&lt;br /&gt;&amp;nbsp; 南宁毕业生就业服务网 &#x3D; 10019&lt;br /&gt;&amp;nbsp; 平南毕业生就业服务网 &#x3D; 10020&lt;br /&gt;&amp;nbsp; 军培 &#x3D; 10021&lt;br /&gt;&amp;nbsp; 所有 &#x3D; -1&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationWlSettingGet(districtId?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultObject>>> {
            const localVarAxiosArgs = await ConfigurationApiAxiosParamCreator(configuration).apiConfigurationWlSettingGet(districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ConfigurationApi - factory interface
 * @export
 */
export const ConfigurationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取微信分享配置
         * @param {string} [url] 
         * @param {string} [callback] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationShareConfigGet(url?: string, callback?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultShareConfigDto>> {
            return ConfigurationApiFp(configuration).apiConfigurationShareConfigGet(url, callback, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {BussDistrict} [districtId] &lt;br /&gt;&amp;nbsp; 广西 &#x3D; 0&lt;br /&gt;&amp;nbsp; 桂林 &#x3D; 1&lt;br /&gt;&amp;nbsp; 柳州 &#x3D; 2&lt;br /&gt;&amp;nbsp; 梧州 &#x3D; 4&lt;br /&gt;&amp;nbsp; 桂平 &#x3D; 5&lt;br /&gt;&amp;nbsp; 百色 &#x3D; 6&lt;br /&gt;&amp;nbsp; 钦州 &#x3D; 7&lt;br /&gt;&amp;nbsp; 河池 &#x3D; 8&lt;br /&gt;&amp;nbsp; 北海 &#x3D; 9&lt;br /&gt;&amp;nbsp; 桂兴 &#x3D; 10&lt;br /&gt;&amp;nbsp; 防港 &#x3D; 11&lt;br /&gt;&amp;nbsp; 玉林 &#x3D; 12&lt;br /&gt;&amp;nbsp; 崇左 &#x3D; 13&lt;br /&gt;&amp;nbsp; 贵港 &#x3D; 14&lt;br /&gt;&amp;nbsp; 来宾 &#x3D; 15&lt;br /&gt;&amp;nbsp; 合浦 &#x3D; 16&lt;br /&gt;&amp;nbsp; 永福 &#x3D; 17&lt;br /&gt;&amp;nbsp; 贺州 &#x3D; 18&lt;br /&gt;&amp;nbsp; 南宁 &#x3D; 19&lt;br /&gt;&amp;nbsp; 平南 &#x3D; 20&lt;br /&gt;&amp;nbsp; 毕业生频道 &#x3D; 10000&lt;br /&gt;&amp;nbsp; 桂林毕业生就业服务网 &#x3D; 10001&lt;br /&gt;&amp;nbsp; 柳州毕业生就业服务网 &#x3D; 10002&lt;br /&gt;&amp;nbsp; 梧州毕业生就业服务网 &#x3D; 10004&lt;br /&gt;&amp;nbsp; 桂平毕业生就业服务网 &#x3D; 10005&lt;br /&gt;&amp;nbsp; 百色毕业生就业服务网 &#x3D; 10006&lt;br /&gt;&amp;nbsp; 钦州毕业生就业服务网 &#x3D; 10007&lt;br /&gt;&amp;nbsp; 河池毕业生就业服务网 &#x3D; 10008&lt;br /&gt;&amp;nbsp; 北海毕业生就业服务网 &#x3D; 10009&lt;br /&gt;&amp;nbsp; 防城毕业生就业服务网 &#x3D; 10011&lt;br /&gt;&amp;nbsp; 玉林毕业生就业服务网 &#x3D; 10012&lt;br /&gt;&amp;nbsp; 崇左毕业生就业服务网 &#x3D; 10013&lt;br /&gt;&amp;nbsp; 贵港毕业生就业服务网 &#x3D; 10014&lt;br /&gt;&amp;nbsp; 来宾毕业生就业服务网 &#x3D; 10015&lt;br /&gt;&amp;nbsp; 贺州毕业生就业服务网 &#x3D; 10018&lt;br /&gt;&amp;nbsp; 南宁毕业生就业服务网 &#x3D; 10019&lt;br /&gt;&amp;nbsp; 平南毕业生就业服务网 &#x3D; 10020&lt;br /&gt;&amp;nbsp; 军培 &#x3D; 10021&lt;br /&gt;&amp;nbsp; 所有 &#x3D; -1&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationWlSettingGet(districtId?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultObject>> {
            return ConfigurationApiFp(configuration).apiConfigurationWlSettingGet(districtId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ConfigurationApi - object-oriented interface
 * @export
 * @class ConfigurationApi
 * @extends {BaseAPI}
 */
export class ConfigurationApi extends BaseAPI {
    /**
     * 
     * @summary 获取微信分享配置
     * @param {string} [url] 
     * @param {string} [callback] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigurationApi
     */
    public async apiConfigurationShareConfigGet(url?: string, callback?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultShareConfigDto>> {
        return ConfigurationApiFp(this.configuration).apiConfigurationShareConfigGet(url, callback, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {BussDistrict} [districtId] &lt;br /&gt;&amp;nbsp; 广西 &#x3D; 0&lt;br /&gt;&amp;nbsp; 桂林 &#x3D; 1&lt;br /&gt;&amp;nbsp; 柳州 &#x3D; 2&lt;br /&gt;&amp;nbsp; 梧州 &#x3D; 4&lt;br /&gt;&amp;nbsp; 桂平 &#x3D; 5&lt;br /&gt;&amp;nbsp; 百色 &#x3D; 6&lt;br /&gt;&amp;nbsp; 钦州 &#x3D; 7&lt;br /&gt;&amp;nbsp; 河池 &#x3D; 8&lt;br /&gt;&amp;nbsp; 北海 &#x3D; 9&lt;br /&gt;&amp;nbsp; 桂兴 &#x3D; 10&lt;br /&gt;&amp;nbsp; 防港 &#x3D; 11&lt;br /&gt;&amp;nbsp; 玉林 &#x3D; 12&lt;br /&gt;&amp;nbsp; 崇左 &#x3D; 13&lt;br /&gt;&amp;nbsp; 贵港 &#x3D; 14&lt;br /&gt;&amp;nbsp; 来宾 &#x3D; 15&lt;br /&gt;&amp;nbsp; 合浦 &#x3D; 16&lt;br /&gt;&amp;nbsp; 永福 &#x3D; 17&lt;br /&gt;&amp;nbsp; 贺州 &#x3D; 18&lt;br /&gt;&amp;nbsp; 南宁 &#x3D; 19&lt;br /&gt;&amp;nbsp; 平南 &#x3D; 20&lt;br /&gt;&amp;nbsp; 毕业生频道 &#x3D; 10000&lt;br /&gt;&amp;nbsp; 桂林毕业生就业服务网 &#x3D; 10001&lt;br /&gt;&amp;nbsp; 柳州毕业生就业服务网 &#x3D; 10002&lt;br /&gt;&amp;nbsp; 梧州毕业生就业服务网 &#x3D; 10004&lt;br /&gt;&amp;nbsp; 桂平毕业生就业服务网 &#x3D; 10005&lt;br /&gt;&amp;nbsp; 百色毕业生就业服务网 &#x3D; 10006&lt;br /&gt;&amp;nbsp; 钦州毕业生就业服务网 &#x3D; 10007&lt;br /&gt;&amp;nbsp; 河池毕业生就业服务网 &#x3D; 10008&lt;br /&gt;&amp;nbsp; 北海毕业生就业服务网 &#x3D; 10009&lt;br /&gt;&amp;nbsp; 防城毕业生就业服务网 &#x3D; 10011&lt;br /&gt;&amp;nbsp; 玉林毕业生就业服务网 &#x3D; 10012&lt;br /&gt;&amp;nbsp; 崇左毕业生就业服务网 &#x3D; 10013&lt;br /&gt;&amp;nbsp; 贵港毕业生就业服务网 &#x3D; 10014&lt;br /&gt;&amp;nbsp; 来宾毕业生就业服务网 &#x3D; 10015&lt;br /&gt;&amp;nbsp; 贺州毕业生就业服务网 &#x3D; 10018&lt;br /&gt;&amp;nbsp; 南宁毕业生就业服务网 &#x3D; 10019&lt;br /&gt;&amp;nbsp; 平南毕业生就业服务网 &#x3D; 10020&lt;br /&gt;&amp;nbsp; 军培 &#x3D; 10021&lt;br /&gt;&amp;nbsp; 所有 &#x3D; -1&lt;br /&gt;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigurationApi
     */
    public async apiConfigurationWlSettingGet(districtId?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultObject>> {
        return ConfigurationApiFp(this.configuration).apiConfigurationWlSettingGet(districtId, options).then((request) => request(this.axios, this.basePath));
    }
}
