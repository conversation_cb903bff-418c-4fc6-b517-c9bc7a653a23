/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { CreateConsultationInput } from '../models';
import { RestfulResultCreateConsultationOutput } from '../models';
import { RestfulResultPagedListConsultationListDto } from '../models';
import { RestfulResultPagedListOutsideRecruitmentListDto } from '../models';
import { RestfulResultPagedListYueGuiRecruitmentListDto } from '../models';
/**
 * EmploymentApi - axios parameter creator
 * @export
 */
export const EmploymentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取咨询列表 职业指导咨询答复信息
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {Date} [startTime] 咨询时间开始（格式：YYYY-MM-DD HH:mm:ss）
         * @param {Date} [endTime] 咨询时间结束（格式：YYYY-MM-DD HH:mm:ss）
         * @param {string} [problemDescription] 问题描述关键词（模糊查询）
         * @param {number} [hasReplied] 是否已答复（0-未答复，1-已答复）
         * @param {number} [guidanceType] 指导类型（字典值）
         * @param {number} [consultationServiceCategory] 咨询服务类别（字典值）
         * @param {string} [consultantName] 咨询人姓名（模糊查询）
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentConsultationListGet: async (page?: number, pageSize?: number, startTime?: Date, endTime?: Date, problemDescription?: string, hasReplied?: number, guidanceType?: number, consultationServiceCategory?: number, consultantName?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/employment/consultationList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (problemDescription !== undefined) {
                localVarQueryParameter['ProblemDescription'] = problemDescription;
            }

            if (hasReplied !== undefined) {
                localVarQueryParameter['HasReplied'] = hasReplied;
            }

            if (guidanceType !== undefined) {
                localVarQueryParameter['GuidanceType'] = guidanceType;
            }

            if (consultationServiceCategory !== undefined) {
                localVarQueryParameter['ConsultationServiceCategory'] = consultationServiceCategory;
            }

            if (consultantName !== undefined) {
                localVarQueryParameter['ConsultantName'] = consultantName;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 新增咨询 职业指导咨询答复信息
         * @param {CreateConsultationInput} [body] 咨询信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentConsultationPost: async (body?: CreateConsultationInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/employment/consultation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取区外招聘列表
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {number} [jurisdiction] 职位所属地（行政区划代码）
         * @param {string} [recruitmentProfession] 职位工种（招聘职业或工种名称）
         * @param {string} [enterpriseName] 公司名称（企业名称）
         * @param {Date} [startDate] 招聘有效期开始时间
         * @param {Date} [endDate] 招聘有效期结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentOutsideRecruitmentListGet: async (page?: number, pageSize?: number, jurisdiction?: number, recruitmentProfession?: string, enterpriseName?: string, startDate?: Date, endDate?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/employment/outsideRecruitmentList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (jurisdiction !== undefined) {
                localVarQueryParameter['Jurisdiction'] = jurisdiction;
            }

            if (recruitmentProfession !== undefined) {
                localVarQueryParameter['RecruitmentProfession'] = recruitmentProfession;
            }

            if (enterpriseName !== undefined) {
                localVarQueryParameter['EnterpriseName'] = enterpriseName;
            }

            if (startDate !== undefined) {
                localVarQueryParameter['StartDate'] = (startDate as any instanceof Date) ?
                    (startDate as any).toISOString() :
                    startDate;
            }

            if (endDate !== undefined) {
                localVarQueryParameter['EndDate'] = (endDate as any instanceof Date) ?
                    (endDate as any).toISOString() :
                    endDate;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取粤桂劳务协作招聘列表
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {string} [workCity] 职位所属地（职位所在城市）
         * @param {string} [jobTitle] 职位工种（招聘职位名称）
         * @param {string} [companyName] 公司名称（企业名称）
         * @param {Date} [startDate] 招聘有效期开始时间
         * @param {Date} [endDate] 招聘有效期结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEmploymentYueGuiRecruitmentListGet: async (page?: number, pageSize?: number, workCity?: string, jobTitle?: string, companyName?: string, startDate?: Date, endDate?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/employment/yueGuiRecruitmentList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (workCity !== undefined) {
                localVarQueryParameter['WorkCity'] = workCity;
            }

            if (jobTitle !== undefined) {
                localVarQueryParameter['JobTitle'] = jobTitle;
            }

            if (companyName !== undefined) {
                localVarQueryParameter['CompanyName'] = companyName;
            }

            if (startDate !== undefined) {
                localVarQueryParameter['StartDate'] = (startDate as any instanceof Date) ?
                    (startDate as any).toISOString() :
                    startDate;
            }

            if (endDate !== undefined) {
                localVarQueryParameter['EndDate'] = (endDate as any instanceof Date) ?
                    (endDate as any).toISOString() :
                    endDate;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EmploymentApi - functional programming interface
 * @export
 */
export const EmploymentApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取咨询列表 职业指导咨询答复信息
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {Date} [startTime] 咨询时间开始（格式：YYYY-MM-DD HH:mm:ss）
         * @param {Date} [endTime] 咨询时间结束（格式：YYYY-MM-DD HH:mm:ss）
         * @param {string} [problemDescription] 问题描述关键词（模糊查询）
         * @param {number} [hasReplied] 是否已答复（0-未答复，1-已答复）
         * @param {number} [guidanceType] 指导类型（字典值）
         * @param {number} [consultationServiceCategory] 咨询服务类别（字典值）
         * @param {string} [consultantName] 咨询人姓名（模糊查询）
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentConsultationListGet(page?: number, pageSize?: number, startTime?: Date, endTime?: Date, problemDescription?: string, hasReplied?: number, guidanceType?: number, consultationServiceCategory?: number, consultantName?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListConsultationListDto>>> {
            const localVarAxiosArgs = await EmploymentApiAxiosParamCreator(configuration).apiEmploymentConsultationListGet(page, pageSize, startTime, endTime, problemDescription, hasReplied, guidanceType, consultationServiceCategory, consultantName, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 新增咨询 职业指导咨询答复信息
         * @param {CreateConsultationInput} [body] 咨询信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentConsultationPost(body?: CreateConsultationInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultCreateConsultationOutput>>> {
            const localVarAxiosArgs = await EmploymentApiAxiosParamCreator(configuration).apiEmploymentConsultationPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取区外招聘列表
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {number} [jurisdiction] 职位所属地（行政区划代码）
         * @param {string} [recruitmentProfession] 职位工种（招聘职业或工种名称）
         * @param {string} [enterpriseName] 公司名称（企业名称）
         * @param {Date} [startDate] 招聘有效期开始时间
         * @param {Date} [endDate] 招聘有效期结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentOutsideRecruitmentListGet(page?: number, pageSize?: number, jurisdiction?: number, recruitmentProfession?: string, enterpriseName?: string, startDate?: Date, endDate?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListOutsideRecruitmentListDto>>> {
            const localVarAxiosArgs = await EmploymentApiAxiosParamCreator(configuration).apiEmploymentOutsideRecruitmentListGet(page, pageSize, jurisdiction, recruitmentProfession, enterpriseName, startDate, endDate, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取粤桂劳务协作招聘列表
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {string} [workCity] 职位所属地（职位所在城市）
         * @param {string} [jobTitle] 职位工种（招聘职位名称）
         * @param {string} [companyName] 公司名称（企业名称）
         * @param {Date} [startDate] 招聘有效期开始时间
         * @param {Date} [endDate] 招聘有效期结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentYueGuiRecruitmentListGet(page?: number, pageSize?: number, workCity?: string, jobTitle?: string, companyName?: string, startDate?: Date, endDate?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListYueGuiRecruitmentListDto>>> {
            const localVarAxiosArgs = await EmploymentApiAxiosParamCreator(configuration).apiEmploymentYueGuiRecruitmentListGet(page, pageSize, workCity, jobTitle, companyName, startDate, endDate, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EmploymentApi - factory interface
 * @export
 */
export const EmploymentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取咨询列表 职业指导咨询答复信息
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {Date} [startTime] 咨询时间开始（格式：YYYY-MM-DD HH:mm:ss）
         * @param {Date} [endTime] 咨询时间结束（格式：YYYY-MM-DD HH:mm:ss）
         * @param {string} [problemDescription] 问题描述关键词（模糊查询）
         * @param {number} [hasReplied] 是否已答复（0-未答复，1-已答复）
         * @param {number} [guidanceType] 指导类型（字典值）
         * @param {number} [consultationServiceCategory] 咨询服务类别（字典值）
         * @param {string} [consultantName] 咨询人姓名（模糊查询）
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentConsultationListGet(page?: number, pageSize?: number, startTime?: Date, endTime?: Date, problemDescription?: string, hasReplied?: number, guidanceType?: number, consultationServiceCategory?: number, consultantName?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListConsultationListDto>> {
            return EmploymentApiFp(configuration).apiEmploymentConsultationListGet(page, pageSize, startTime, endTime, problemDescription, hasReplied, guidanceType, consultationServiceCategory, consultantName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 新增咨询 职业指导咨询答复信息
         * @param {CreateConsultationInput} [body] 咨询信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentConsultationPost(body?: CreateConsultationInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultCreateConsultationOutput>> {
            return EmploymentApiFp(configuration).apiEmploymentConsultationPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取区外招聘列表
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {number} [jurisdiction] 职位所属地（行政区划代码）
         * @param {string} [recruitmentProfession] 职位工种（招聘职业或工种名称）
         * @param {string} [enterpriseName] 公司名称（企业名称）
         * @param {Date} [startDate] 招聘有效期开始时间
         * @param {Date} [endDate] 招聘有效期结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentOutsideRecruitmentListGet(page?: number, pageSize?: number, jurisdiction?: number, recruitmentProfession?: string, enterpriseName?: string, startDate?: Date, endDate?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListOutsideRecruitmentListDto>> {
            return EmploymentApiFp(configuration).apiEmploymentOutsideRecruitmentListGet(page, pageSize, jurisdiction, recruitmentProfession, enterpriseName, startDate, endDate, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取粤桂劳务协作招聘列表
         * @param {number} [page] 页码，默认第1页
         * @param {number} [pageSize] 每页数量，默认20条
         * @param {string} [workCity] 职位所属地（职位所在城市）
         * @param {string} [jobTitle] 职位工种（招聘职位名称）
         * @param {string} [companyName] 公司名称（企业名称）
         * @param {Date} [startDate] 招聘有效期开始时间
         * @param {Date} [endDate] 招聘有效期结束时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEmploymentYueGuiRecruitmentListGet(page?: number, pageSize?: number, workCity?: string, jobTitle?: string, companyName?: string, startDate?: Date, endDate?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListYueGuiRecruitmentListDto>> {
            return EmploymentApiFp(configuration).apiEmploymentYueGuiRecruitmentListGet(page, pageSize, workCity, jobTitle, companyName, startDate, endDate, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EmploymentApi - object-oriented interface
 * @export
 * @class EmploymentApi
 * @extends {BaseAPI}
 */
export class EmploymentApi extends BaseAPI {
    /**
     * 
     * @summary 获取咨询列表 职业指导咨询答复信息
     * @param {number} [page] 页码，默认第1页
     * @param {number} [pageSize] 每页数量，默认20条
     * @param {Date} [startTime] 咨询时间开始（格式：YYYY-MM-DD HH:mm:ss）
     * @param {Date} [endTime] 咨询时间结束（格式：YYYY-MM-DD HH:mm:ss）
     * @param {string} [problemDescription] 问题描述关键词（模糊查询）
     * @param {number} [hasReplied] 是否已答复（0-未答复，1-已答复）
     * @param {number} [guidanceType] 指导类型（字典值）
     * @param {number} [consultationServiceCategory] 咨询服务类别（字典值）
     * @param {string} [consultantName] 咨询人姓名（模糊查询）
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentApi
     */
    public async apiEmploymentConsultationListGet(page?: number, pageSize?: number, startTime?: Date, endTime?: Date, problemDescription?: string, hasReplied?: number, guidanceType?: number, consultationServiceCategory?: number, consultantName?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListConsultationListDto>> {
        return EmploymentApiFp(this.configuration).apiEmploymentConsultationListGet(page, pageSize, startTime, endTime, problemDescription, hasReplied, guidanceType, consultationServiceCategory, consultantName, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 新增咨询 职业指导咨询答复信息
     * @param {CreateConsultationInput} [body] 咨询信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentApi
     */
    public async apiEmploymentConsultationPost(body?: CreateConsultationInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultCreateConsultationOutput>> {
        return EmploymentApiFp(this.configuration).apiEmploymentConsultationPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取区外招聘列表
     * @param {number} [page] 页码，默认第1页
     * @param {number} [pageSize] 每页数量，默认20条
     * @param {number} [jurisdiction] 职位所属地（行政区划代码）
     * @param {string} [recruitmentProfession] 职位工种（招聘职业或工种名称）
     * @param {string} [enterpriseName] 公司名称（企业名称）
     * @param {Date} [startDate] 招聘有效期开始时间
     * @param {Date} [endDate] 招聘有效期结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentApi
     */
    public async apiEmploymentOutsideRecruitmentListGet(page?: number, pageSize?: number, jurisdiction?: number, recruitmentProfession?: string, enterpriseName?: string, startDate?: Date, endDate?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListOutsideRecruitmentListDto>> {
        return EmploymentApiFp(this.configuration).apiEmploymentOutsideRecruitmentListGet(page, pageSize, jurisdiction, recruitmentProfession, enterpriseName, startDate, endDate, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取粤桂劳务协作招聘列表
     * @param {number} [page] 页码，默认第1页
     * @param {number} [pageSize] 每页数量，默认20条
     * @param {string} [workCity] 职位所属地（职位所在城市）
     * @param {string} [jobTitle] 职位工种（招聘职位名称）
     * @param {string} [companyName] 公司名称（企业名称）
     * @param {Date} [startDate] 招聘有效期开始时间
     * @param {Date} [endDate] 招聘有效期结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EmploymentApi
     */
    public async apiEmploymentYueGuiRecruitmentListGet(page?: number, pageSize?: number, workCity?: string, jobTitle?: string, companyName?: string, startDate?: Date, endDate?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListYueGuiRecruitmentListDto>> {
        return EmploymentApiFp(this.configuration).apiEmploymentYueGuiRecruitmentListGet(page, pageSize, workCity, jobTitle, companyName, startDate, endDate, options).then((request) => request(this.axios, this.basePath));
    }
}
