/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RestfulResultListHomeEnterprisePositionOutput } from '../models';
import { RestfulResultListHomeNewHotPositionViewModel } from '../models';
import { RestfulResultListHomeNewsOutput } from '../models';
/**
 * HomePCApi - axios parameter creator
 * @export
 */
export const HomePCApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取零工企业（最新登录且在招，职位性质为916），每个企业返回指定数量的职位信息
         * @param {number} [maxEnterprises] 企业数量，默认27
         * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认1
         * @param {number} [districtId] 地区ID，0表示所有地区
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiHomePCHomeGigEnterprisesGet: async (maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/HomePC/HomeGigEnterprises`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (maxEnterprises !== undefined) {
                localVarQueryParameter['maxEnterprises'] = maxEnterprises;
            }

            if (positionsPerEnterprise !== undefined) {
                localVarQueryParameter['positionsPerEnterprise'] = positionsPerEnterprise;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取热门企业（最新登录且在招），每个企业返回指定数量的职位信息
         * @param {number} [maxEnterprises] 企业数量，默认9
         * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认3
         * @param {number} [districtId] 地区ID，0表示所有地区
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiHomePCHomeHotEnterprisesGet: async (maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/HomePC/HomeHotEnterprises`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (maxEnterprises !== undefined) {
                localVarQueryParameter['maxEnterprises'] = maxEnterprises;
            }

            if (positionsPerEnterprise !== undefined) {
                localVarQueryParameter['positionsPerEnterprise'] = positionsPerEnterprise;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取热门职位分类数据
         * @param {number} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiHomePCHomeNewHotPositionGet: async (districtId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/HomePC/HomeNewHotPosition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiHomePCNewsGet: async (districtId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/HomePC/News`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * HomePCApi - functional programming interface
 * @export
 */
export const HomePCApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取零工企业（最新登录且在招，职位性质为916），每个企业返回指定数量的职位信息
         * @param {number} [maxEnterprises] 企业数量，默认27
         * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认1
         * @param {number} [districtId] 地区ID，0表示所有地区
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCHomeGigEnterprisesGet(maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListHomeEnterprisePositionOutput>>> {
            const localVarAxiosArgs = await HomePCApiAxiosParamCreator(configuration).apiHomePCHomeGigEnterprisesGet(maxEnterprises, positionsPerEnterprise, districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取热门企业（最新登录且在招），每个企业返回指定数量的职位信息
         * @param {number} [maxEnterprises] 企业数量，默认9
         * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认3
         * @param {number} [districtId] 地区ID，0表示所有地区
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCHomeHotEnterprisesGet(maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListHomeEnterprisePositionOutput>>> {
            const localVarAxiosArgs = await HomePCApiAxiosParamCreator(configuration).apiHomePCHomeHotEnterprisesGet(maxEnterprises, positionsPerEnterprise, districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取热门职位分类数据
         * @param {number} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCHomeNewHotPositionGet(districtId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListHomeNewHotPositionViewModel>>> {
            const localVarAxiosArgs = await HomePCApiAxiosParamCreator(configuration).apiHomePCHomeNewHotPositionGet(districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {number} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCNewsGet(districtId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListHomeNewsOutput>>> {
            const localVarAxiosArgs = await HomePCApiAxiosParamCreator(configuration).apiHomePCNewsGet(districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * HomePCApi - factory interface
 * @export
 */
export const HomePCApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取零工企业（最新登录且在招，职位性质为916），每个企业返回指定数量的职位信息
         * @param {number} [maxEnterprises] 企业数量，默认27
         * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认1
         * @param {number} [districtId] 地区ID，0表示所有地区
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCHomeGigEnterprisesGet(maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListHomeEnterprisePositionOutput>> {
            return HomePCApiFp(configuration).apiHomePCHomeGigEnterprisesGet(maxEnterprises, positionsPerEnterprise, districtId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取热门企业（最新登录且在招），每个企业返回指定数量的职位信息
         * @param {number} [maxEnterprises] 企业数量，默认9
         * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认3
         * @param {number} [districtId] 地区ID，0表示所有地区
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCHomeHotEnterprisesGet(maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListHomeEnterprisePositionOutput>> {
            return HomePCApiFp(configuration).apiHomePCHomeHotEnterprisesGet(maxEnterprises, positionsPerEnterprise, districtId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取热门职位分类数据
         * @param {number} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCHomeNewHotPositionGet(districtId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListHomeNewHotPositionViewModel>> {
            return HomePCApiFp(configuration).apiHomePCHomeNewHotPositionGet(districtId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiHomePCNewsGet(districtId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListHomeNewsOutput>> {
            return HomePCApiFp(configuration).apiHomePCNewsGet(districtId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * HomePCApi - object-oriented interface
 * @export
 * @class HomePCApi
 * @extends {BaseAPI}
 */
export class HomePCApi extends BaseAPI {
    /**
     * 
     * @summary 获取零工企业（最新登录且在招，职位性质为916），每个企业返回指定数量的职位信息
     * @param {number} [maxEnterprises] 企业数量，默认27
     * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认1
     * @param {number} [districtId] 地区ID，0表示所有地区
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HomePCApi
     */
    public async apiHomePCHomeGigEnterprisesGet(maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListHomeEnterprisePositionOutput>> {
        return HomePCApiFp(this.configuration).apiHomePCHomeGigEnterprisesGet(maxEnterprises, positionsPerEnterprise, districtId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取热门企业（最新登录且在招），每个企业返回指定数量的职位信息
     * @param {number} [maxEnterprises] 企业数量，默认9
     * @param {number} [positionsPerEnterprise] 每个企业的职位数量，默认3
     * @param {number} [districtId] 地区ID，0表示所有地区
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HomePCApi
     */
    public async apiHomePCHomeHotEnterprisesGet(maxEnterprises?: number, positionsPerEnterprise?: number, districtId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListHomeEnterprisePositionOutput>> {
        return HomePCApiFp(this.configuration).apiHomePCHomeHotEnterprisesGet(maxEnterprises, positionsPerEnterprise, districtId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取热门职位分类数据
     * @param {number} [districtId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HomePCApi
     */
    public async apiHomePCHomeNewHotPositionGet(districtId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListHomeNewHotPositionViewModel>> {
        return HomePCApiFp(this.configuration).apiHomePCHomeNewHotPositionGet(districtId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {number} [districtId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HomePCApi
     */
    public async apiHomePCNewsGet(districtId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListHomeNewsOutput>> {
        return HomePCApiFp(this.configuration).apiHomePCNewsGet(districtId, options).then((request) => request(this.axios, this.basePath));
    }
}
