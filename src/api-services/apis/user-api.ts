/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BatchDeliverModel } from '../models';
import { BussDistrict } from '../models';
import { DeliveryResumeModel } from '../models';
import { FavoritePositionQuery } from '../models';
import { FeedBackInputDto } from '../models';
import { MessagePlatformModel } from '../models';
import { RESTfulResultObject } from '../models';
import { RestfulResultBoolean } from '../models';
import { RestfulResultCareerInfo } from '../models';
import { RestfulResultDeliverResultModel } from '../models';
import { RestfulResultDeliverResumeList } from '../models';
import { RestfulResultIActionResult } from '../models';
import { RestfulResultInt32 } from '../models';
import { RestfulResultObject } from '../models';
import { RestfulResultRESTfulResultObject } from '../models';
import { RestfulResultUserIndentityDto } from '../models';
import { ResumeDeliveryTypeEnum } from '../models';
/**
 * UserApi - axios parameter creator
 * @export
 */
export const UserApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary App批量投递
         * @param {BatchDeliverModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserBatchDeliverPost: async (body?: BatchDeliverModel, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/BatchDeliver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 投递记录里面的撤销投递
         * @param {number} [deliverId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserCancelDeliverPost: async (deliverId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/CancelDeliver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (deliverId !== undefined) {
                localVarQueryParameter['deliverId'] = deliverId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 取消收藏企业
         * @param {string} [enterpriGuid] 企业Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserCancelFavoriteEnterprisePost: async (enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/CancelFavoriteEnterprise`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (enterpriGuid !== undefined) {
                localVarQueryParameter['enterpriGuid'] = enterpriGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 取消收藏职位
         * @param {FavoritePositionQuery} [body] 职位Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserCancelFavoritePositionPost: async (body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/CancelFavoritePosition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 手动触发检查竞争力分析活动通知
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserCheckCompetitionAnalysisNoticePost: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/CheckCompetitionAnalysisNotice`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 简历投递 H5 触屏版
         * @param {MessagePlatformModel} [body] 
         * @param {string} [positionGuid] 
         * @param {string} [resumeGuid] 
         * @param {boolean} [isUseDefaultResume] 是否使用默认简历投递
         * @param {boolean} [isTop] 是否置顶简历
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {number} [liveId] 
         * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserDeliverPost: async (body?: MessagePlatformModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveId?: number, resumeDeliveryType?: ResumeDeliveryTypeEnum, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/Deliver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (positionGuid !== undefined) {
                localVarQueryParameter['PositionGuid'] = positionGuid;
            }

            if (resumeGuid !== undefined) {
                localVarQueryParameter['ResumeGuid'] = resumeGuid;
            }

            if (isUseDefaultResume !== undefined) {
                localVarQueryParameter['isUseDefaultResume'] = isUseDefaultResume;
            }

            if (isTop !== undefined) {
                localVarQueryParameter['IsTop'] = isTop;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (liveId !== undefined) {
                localVarQueryParameter['LiveId'] = liveId;
            }

            if (resumeDeliveryType !== undefined) {
                localVarQueryParameter['resumeDeliveryType'] = resumeDeliveryType;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary App、小程序简历投递
         * @param {DeliveryResumeModel} [body] 
         * @param {string} [positionGuid] 
         * @param {string} [resumeGuid] 
         * @param {boolean} [isUseDefaultResume] 
         * @param {boolean} [isTop] 是否置顶简历
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {string} [liveGuid] 
         * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 职位投递业务类型来源(首页、聊一聊、直播、社区动态等)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserDeliverResumePost: async (body?: DeliveryResumeModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveGuid?: string, resumeDeliveryType?: ResumeDeliveryTypeEnum, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/DeliverResume`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (positionGuid !== undefined) {
                localVarQueryParameter['PositionGuid'] = positionGuid;
            }

            if (resumeGuid !== undefined) {
                localVarQueryParameter['ResumeGuid'] = resumeGuid;
            }

            if (isUseDefaultResume !== undefined) {
                localVarQueryParameter['isUseDefaultResume'] = isUseDefaultResume;
            }

            if (isTop !== undefined) {
                localVarQueryParameter['IsTop'] = isTop;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (liveGuid !== undefined) {
                localVarQueryParameter['liveGuid'] = liveGuid;
            }

            if (resumeDeliveryType !== undefined) {
                localVarQueryParameter['resumeDeliveryType'] = resumeDeliveryType;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 收藏企业
         * @param {string} [enterpriGuid] 企业Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserFavoriteEnterprisePost: async (enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/FavoriteEnterprise`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (enterpriGuid !== undefined) {
                localVarQueryParameter['enterpriGuid'] = enterpriGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 收藏职位
         * @param {FavoritePositionQuery} [body] 职位Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserFavoritePositionPost: async (body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/FavoritePosition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 提交意见反馈(图片采用base64)
         * @param {FeedBackInputDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserFeedBackPost: async (body?: FeedBackInputDto, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/FeedBack`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取求职意向
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserGetCareerInfoGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/GetCareerInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 登录验证的信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserIdentityGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/Identity`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取当前登录的用户(不返回用户信息)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserLoginStatusGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/LoginStatus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 简历列表
         * @param {string} [positionGuid] 投递职位的Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserResumeListGet: async (positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/User/ResumeList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (positionGuid !== undefined) {
                localVarQueryParameter['positionGuid'] = positionGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserApi - functional programming interface
 * @export
 */
export const UserApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary App批量投递
         * @param {BatchDeliverModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserBatchDeliverPost(body?: BatchDeliverModel, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultDeliverResultModel>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserBatchDeliverPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 投递记录里面的撤销投递
         * @param {number} [deliverId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCancelDeliverPost(deliverId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultObject>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserCancelDeliverPost(deliverId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 取消收藏企业
         * @param {string} [enterpriGuid] 企业Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCancelFavoriteEnterprisePost(enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserCancelFavoriteEnterprisePost(enterpriGuid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 取消收藏职位
         * @param {FavoritePositionQuery} [body] 职位Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCancelFavoritePositionPost(body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultInt32>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserCancelFavoritePositionPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 手动触发检查竞争力分析活动通知
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCheckCompetitionAnalysisNoticePost(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultIActionResult>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserCheckCompetitionAnalysisNoticePost(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 简历投递 H5 触屏版
         * @param {MessagePlatformModel} [body] 
         * @param {string} [positionGuid] 
         * @param {string} [resumeGuid] 
         * @param {boolean} [isUseDefaultResume] 是否使用默认简历投递
         * @param {boolean} [isTop] 是否置顶简历
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {number} [liveId] 
         * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserDeliverPost(body?: MessagePlatformModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveId?: number, resumeDeliveryType?: ResumeDeliveryTypeEnum, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserDeliverPost(body, positionGuid, resumeGuid, isUseDefaultResume, isTop, districtId, from, liveId, resumeDeliveryType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary App、小程序简历投递
         * @param {DeliveryResumeModel} [body] 
         * @param {string} [positionGuid] 
         * @param {string} [resumeGuid] 
         * @param {boolean} [isUseDefaultResume] 
         * @param {boolean} [isTop] 是否置顶简历
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {string} [liveGuid] 
         * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 职位投递业务类型来源(首页、聊一聊、直播、社区动态等)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserDeliverResumePost(body?: DeliveryResumeModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveGuid?: string, resumeDeliveryType?: ResumeDeliveryTypeEnum, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultRESTfulResultObject>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserDeliverResumePost(body, positionGuid, resumeGuid, isUseDefaultResume, isTop, districtId, from, liveGuid, resumeDeliveryType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 收藏企业
         * @param {string} [enterpriGuid] 企业Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserFavoriteEnterprisePost(enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserFavoriteEnterprisePost(enterpriGuid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 收藏职位
         * @param {FavoritePositionQuery} [body] 职位Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserFavoritePositionPost(body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultInt32>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserFavoritePositionPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 提交意见反馈(图片采用base64)
         * @param {FeedBackInputDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserFeedBackPost(body?: FeedBackInputDto, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserFeedBackPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取求职意向
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserGetCareerInfoGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultCareerInfo>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserGetCareerInfoGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 登录验证的信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserIdentityGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultUserIndentityDto>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserIdentityGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取当前登录的用户(不返回用户信息)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserLoginStatusGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBoolean>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserLoginStatusGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 简历列表
         * @param {string} [positionGuid] 投递职位的Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserResumeListGet(positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultDeliverResumeList>>> {
            const localVarAxiosArgs = await UserApiAxiosParamCreator(configuration).apiUserResumeListGet(positionGuid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * UserApi - factory interface
 * @export
 */
export const UserApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary App批量投递
         * @param {BatchDeliverModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserBatchDeliverPost(body?: BatchDeliverModel, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultDeliverResultModel>> {
            return UserApiFp(configuration).apiUserBatchDeliverPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 投递记录里面的撤销投递
         * @param {number} [deliverId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCancelDeliverPost(deliverId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultObject>> {
            return UserApiFp(configuration).apiUserCancelDeliverPost(deliverId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 取消收藏企业
         * @param {string} [enterpriGuid] 企业Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCancelFavoriteEnterprisePost(enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return UserApiFp(configuration).apiUserCancelFavoriteEnterprisePost(enterpriGuid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 取消收藏职位
         * @param {FavoritePositionQuery} [body] 职位Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCancelFavoritePositionPost(body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultInt32>> {
            return UserApiFp(configuration).apiUserCancelFavoritePositionPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 手动触发检查竞争力分析活动通知
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserCheckCompetitionAnalysisNoticePost(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultIActionResult>> {
            return UserApiFp(configuration).apiUserCheckCompetitionAnalysisNoticePost(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 简历投递 H5 触屏版
         * @param {MessagePlatformModel} [body] 
         * @param {string} [positionGuid] 
         * @param {string} [resumeGuid] 
         * @param {boolean} [isUseDefaultResume] 是否使用默认简历投递
         * @param {boolean} [isTop] 是否置顶简历
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {number} [liveId] 
         * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserDeliverPost(body?: MessagePlatformModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveId?: number, resumeDeliveryType?: ResumeDeliveryTypeEnum, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return UserApiFp(configuration).apiUserDeliverPost(body, positionGuid, resumeGuid, isUseDefaultResume, isTop, districtId, from, liveId, resumeDeliveryType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary App、小程序简历投递
         * @param {DeliveryResumeModel} [body] 
         * @param {string} [positionGuid] 
         * @param {string} [resumeGuid] 
         * @param {boolean} [isUseDefaultResume] 
         * @param {boolean} [isTop] 是否置顶简历
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {string} [liveGuid] 
         * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 职位投递业务类型来源(首页、聊一聊、直播、社区动态等)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserDeliverResumePost(body?: DeliveryResumeModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveGuid?: string, resumeDeliveryType?: ResumeDeliveryTypeEnum, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultRESTfulResultObject>> {
            return UserApiFp(configuration).apiUserDeliverResumePost(body, positionGuid, resumeGuid, isUseDefaultResume, isTop, districtId, from, liveGuid, resumeDeliveryType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 收藏企业
         * @param {string} [enterpriGuid] 企业Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserFavoriteEnterprisePost(enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return UserApiFp(configuration).apiUserFavoriteEnterprisePost(enterpriGuid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 收藏职位
         * @param {FavoritePositionQuery} [body] 职位Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserFavoritePositionPost(body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultInt32>> {
            return UserApiFp(configuration).apiUserFavoritePositionPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 提交意见反馈(图片采用base64)
         * @param {FeedBackInputDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserFeedBackPost(body?: FeedBackInputDto, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return UserApiFp(configuration).apiUserFeedBackPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取求职意向
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserGetCareerInfoGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultCareerInfo>> {
            return UserApiFp(configuration).apiUserGetCareerInfoGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 登录验证的信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserIdentityGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultUserIndentityDto>> {
            return UserApiFp(configuration).apiUserIdentityGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取当前登录的用户(不返回用户信息)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserLoginStatusGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBoolean>> {
            return UserApiFp(configuration).apiUserLoginStatusGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 简历列表
         * @param {string} [positionGuid] 投递职位的Guid
         * @param {BussDistrict} [districtId] 地市ID
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserResumeListGet(positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultDeliverResumeList>> {
            return UserApiFp(configuration).apiUserResumeListGet(positionGuid, districtId, from, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserApi - object-oriented interface
 * @export
 * @class UserApi
 * @extends {BaseAPI}
 */
export class UserApi extends BaseAPI {
    /**
     * 
     * @summary App批量投递
     * @param {BatchDeliverModel} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserBatchDeliverPost(body?: BatchDeliverModel, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultDeliverResultModel>> {
        return UserApiFp(this.configuration).apiUserBatchDeliverPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 投递记录里面的撤销投递
     * @param {number} [deliverId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserCancelDeliverPost(deliverId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultObject>> {
        return UserApiFp(this.configuration).apiUserCancelDeliverPost(deliverId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 取消收藏企业
     * @param {string} [enterpriGuid] 企业Guid
     * @param {BussDistrict} [districtId] 地市ID
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserCancelFavoriteEnterprisePost(enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return UserApiFp(this.configuration).apiUserCancelFavoriteEnterprisePost(enterpriGuid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 取消收藏职位
     * @param {FavoritePositionQuery} [body] 职位Guid
     * @param {BussDistrict} [districtId] 地市ID
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserCancelFavoritePositionPost(body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultInt32>> {
        return UserApiFp(this.configuration).apiUserCancelFavoritePositionPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 手动触发检查竞争力分析活动通知
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserCheckCompetitionAnalysisNoticePost(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultIActionResult>> {
        return UserApiFp(this.configuration).apiUserCheckCompetitionAnalysisNoticePost(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 简历投递 H5 触屏版
     * @param {MessagePlatformModel} [body] 
     * @param {string} [positionGuid] 
     * @param {string} [resumeGuid] 
     * @param {boolean} [isUseDefaultResume] 是否使用默认简历投递
     * @param {boolean} [isTop] 是否置顶简历
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {number} [liveId] 
     * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserDeliverPost(body?: MessagePlatformModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveId?: number, resumeDeliveryType?: ResumeDeliveryTypeEnum, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return UserApiFp(this.configuration).apiUserDeliverPost(body, positionGuid, resumeGuid, isUseDefaultResume, isTop, districtId, from, liveId, resumeDeliveryType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary App、小程序简历投递
     * @param {DeliveryResumeModel} [body] 
     * @param {string} [positionGuid] 
     * @param {string} [resumeGuid] 
     * @param {boolean} [isUseDefaultResume] 
     * @param {boolean} [isTop] 是否置顶简历
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {string} [liveGuid] 
     * @param {ResumeDeliveryTypeEnum} [resumeDeliveryType] 职位投递业务类型来源(首页、聊一聊、直播、社区动态等)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserDeliverResumePost(body?: DeliveryResumeModel, positionGuid?: string, resumeGuid?: string, isUseDefaultResume?: boolean, isTop?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, liveGuid?: string, resumeDeliveryType?: ResumeDeliveryTypeEnum, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultRESTfulResultObject>> {
        return UserApiFp(this.configuration).apiUserDeliverResumePost(body, positionGuid, resumeGuid, isUseDefaultResume, isTop, districtId, from, liveGuid, resumeDeliveryType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 收藏企业
     * @param {string} [enterpriGuid] 企业Guid
     * @param {BussDistrict} [districtId] 地市ID
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserFavoriteEnterprisePost(enterpriGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return UserApiFp(this.configuration).apiUserFavoriteEnterprisePost(enterpriGuid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 收藏职位
     * @param {FavoritePositionQuery} [body] 职位Guid
     * @param {BussDistrict} [districtId] 地市ID
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserFavoritePositionPost(body?: FavoritePositionQuery, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultInt32>> {
        return UserApiFp(this.configuration).apiUserFavoritePositionPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 提交意见反馈(图片采用base64)
     * @param {FeedBackInputDto} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserFeedBackPost(body?: FeedBackInputDto, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return UserApiFp(this.configuration).apiUserFeedBackPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取求职意向
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserGetCareerInfoGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultCareerInfo>> {
        return UserApiFp(this.configuration).apiUserGetCareerInfoGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 登录验证的信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserIdentityGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultUserIndentityDto>> {
        return UserApiFp(this.configuration).apiUserIdentityGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取当前登录的用户(不返回用户信息)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserLoginStatusGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBoolean>> {
        return UserApiFp(this.configuration).apiUserLoginStatusGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 简历列表
     * @param {string} [positionGuid] 投递职位的Guid
     * @param {BussDistrict} [districtId] 地市ID
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public async apiUserResumeListGet(positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultDeliverResumeList>> {
        return UserApiFp(this.configuration).apiUserResumeListGet(positionGuid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
}
