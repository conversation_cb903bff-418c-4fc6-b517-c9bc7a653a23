/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { EnterpriseClickFrom } from '../models';
import { PositionRequest } from '../models';
import { RestfulResultBaseDataOutputBoolean } from '../models';
import { RestfulResultEnterpriseDisplayDto } from '../models';
import { RestfulResultListTopicZoneItemDto } from '../models';
import { RestfulResultPagedListIndexPositionListItem } from '../models';
import { RestfulResultPositionDisplayDto } from '../models';
import { RestfulResultSearchOptions } from '../models';
/**
 * WanDaApi - axios parameter creator
 * @export
 */
export const WanDaApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 清除职位搜索滑动记录
         * @param {string} [searchId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWanDaClearSlideLoadSearchPost: async (searchId?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/WanDa/ClearSlideLoadSearch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (searchId !== undefined) {
                localVarQueryParameter['searchId'] = searchId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业详情
         * @param {string} [guid] 企业guid
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
         * @param {string} [acsTk] 标识
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWanDaEnterpriseDetailGet: async (guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, acsTk?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/WanDa/EnterpriseDetail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (clickFrom !== undefined) {
                localVarQueryParameter['clickFrom'] = clickFrom;
            }

            if (acsTk !== undefined) {
                localVarQueryParameter['acsTk'] = acsTk;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWanDaPositionDetailGet: async (id?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/WanDa/PositionDetail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWanDaPositionSearchPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/WanDa/PositionSearch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索配置
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWanDaSearchOptionGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/WanDa/SearchOption`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 专题分类id
         * @param {number} [topicId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWanDaTopicZoneInfoGet: async (topicId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/WanDa/TopicZoneInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (topicId !== undefined) {
                localVarQueryParameter['topicId'] = topicId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * WanDaApi - functional programming interface
 * @export
 */
export const WanDaApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 清除职位搜索滑动记录
         * @param {string} [searchId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaClearSlideLoadSearchPost(searchId?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await WanDaApiAxiosParamCreator(configuration).apiWanDaClearSlideLoadSearchPost(searchId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业详情
         * @param {string} [guid] 企业guid
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
         * @param {string} [acsTk] 标识
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaEnterpriseDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, acsTk?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterpriseDisplayDto>>> {
            const localVarAxiosArgs = await WanDaApiAxiosParamCreator(configuration).apiWanDaEnterpriseDetailGet(guid, districtId, from, clickFrom, acsTk, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaPositionDetailGet(id?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPositionDisplayDto>>> {
            const localVarAxiosArgs = await WanDaApiAxiosParamCreator(configuration).apiWanDaPositionDetailGet(id, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaPositionSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>>> {
            const localVarAxiosArgs = await WanDaApiAxiosParamCreator(configuration).apiWanDaPositionSearchPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 搜索配置
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaSearchOptionGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSearchOptions>>> {
            const localVarAxiosArgs = await WanDaApiAxiosParamCreator(configuration).apiWanDaSearchOptionGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 专题分类id
         * @param {number} [topicId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaTopicZoneInfoGet(topicId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListTopicZoneItemDto>>> {
            const localVarAxiosArgs = await WanDaApiAxiosParamCreator(configuration).apiWanDaTopicZoneInfoGet(topicId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * WanDaApi - factory interface
 * @export
 */
export const WanDaApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 清除职位搜索滑动记录
         * @param {string} [searchId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaClearSlideLoadSearchPost(searchId?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return WanDaApiFp(configuration).apiWanDaClearSlideLoadSearchPost(searchId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业详情
         * @param {string} [guid] 企业guid
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
         * @param {string} [acsTk] 标识
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaEnterpriseDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, acsTk?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterpriseDisplayDto>> {
            return WanDaApiFp(configuration).apiWanDaEnterpriseDetailGet(guid, districtId, from, clickFrom, acsTk, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaPositionDetailGet(id?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPositionDisplayDto>> {
            return WanDaApiFp(configuration).apiWanDaPositionDetailGet(id, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaPositionSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
            return WanDaApiFp(configuration).apiWanDaPositionSearchPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索配置
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaSearchOptionGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSearchOptions>> {
            return WanDaApiFp(configuration).apiWanDaSearchOptionGet(districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 专题分类id
         * @param {number} [topicId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWanDaTopicZoneInfoGet(topicId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListTopicZoneItemDto>> {
            return WanDaApiFp(configuration).apiWanDaTopicZoneInfoGet(topicId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * WanDaApi - object-oriented interface
 * @export
 * @class WanDaApi
 * @extends {BaseAPI}
 */
export class WanDaApi extends BaseAPI {
    /**
     * 
     * @summary 清除职位搜索滑动记录
     * @param {string} [searchId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WanDaApi
     */
    public async apiWanDaClearSlideLoadSearchPost(searchId?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return WanDaApiFp(this.configuration).apiWanDaClearSlideLoadSearchPost(searchId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业详情
     * @param {string} [guid] 企业guid
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
     * @param {string} [acsTk] 标识
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WanDaApi
     */
    public async apiWanDaEnterpriseDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, acsTk?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterpriseDisplayDto>> {
        return WanDaApiFp(this.configuration).apiWanDaEnterpriseDetailGet(guid, districtId, from, clickFrom, acsTk, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位详情
     * @param {string} [id] 职位ID
     * @param {BussDistrict} [districtId] 地市来源
     * @param {ApplicationPlatform} [from] 地市来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WanDaApi
     */
    public async apiWanDaPositionDetailGet(id?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPositionDisplayDto>> {
        return WanDaApiFp(this.configuration).apiWanDaPositionDetailGet(id, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位搜索
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WanDaApi
     */
    public async apiWanDaPositionSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
        return WanDaApiFp(this.configuration).apiWanDaPositionSearchPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 搜索配置
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WanDaApi
     */
    public async apiWanDaSearchOptionGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSearchOptions>> {
        return WanDaApiFp(this.configuration).apiWanDaSearchOptionGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 专题分类id
     * @param {number} [topicId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WanDaApi
     */
    public async apiWanDaTopicZoneInfoGet(topicId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListTopicZoneItemDto>> {
        return WanDaApiFp(this.configuration).apiWanDaTopicZoneInfoGet(topicId, options).then((request) => request(this.axios, this.basePath));
    }
}
