/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { LogoPositionExposureDto } from '../models';
import { LogoPositionLogDto } from '../models';
import { RestfulResultListLogoEnterpriseItemDto } from '../models';
import { RestfulResultLoadingADDto } from '../models';
import { RestfulResultPagedListLogoEnterpriseItemDto } from '../models';
/**
 * LogoApi - axios parameter creator
 * @export
 */
export const LogoApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取广告列表
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {number} [adType] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoAdlistGet: async (page?: number, pageSize?: number, districtId?: BussDistrict, adType?: number, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/logo/adlist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (adType !== undefined) {
                localVarQueryParameter['adType'] = adType;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 点击logo
         * @param {number} [logoID] logoID
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoClickPost: async (logoID?: number, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/logo/click`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (logoID !== undefined) {
                localVarQueryParameter['logoID'] = logoID;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary app load页面 弹出广告
         * @param {string} [url] 
         * @param {number} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoLoadingADGet: async (url?: string, from?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/logo/loadingAD`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (url !== undefined) {
                localVarQueryParameter['url'] = url;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 广告Logo职位点击
         * @param {LogoPositionLogDto} [body] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoPositionClickPost: async (body?: LogoPositionLogDto, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/logo/positionClick`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 广告Logo职位曝光度
         * @param {LogoPositionExposureDto} [body] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoPositionExposurePost: async (body?: LogoPositionExposureDto, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/logo/positionExposure`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索s.grxrc.com结果页面右侧广告，支持补位
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoSearchResultRightAdGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/logo/searchResultRightAd`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * LogoApi - functional programming interface
 * @export
 */
export const LogoApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取广告列表
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {number} [adType] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoAdlistGet(page?: number, pageSize?: number, districtId?: BussDistrict, adType?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoAdlistGet(page, pageSize, districtId, adType, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 点击logo
         * @param {number} [logoID] logoID
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoClickPost(logoID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoClickPost(logoID, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary app load页面 弹出广告
         * @param {string} [url] 
         * @param {number} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoLoadingADGet(url?: string, from?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultLoadingADDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoLoadingADGet(url, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 广告Logo职位点击
         * @param {LogoPositionLogDto} [body] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionClickPost(body?: LogoPositionLogDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoPositionClickPost(body, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 广告Logo职位曝光度
         * @param {LogoPositionExposureDto} [body] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionExposurePost(body?: LogoPositionExposureDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoPositionExposurePost(body, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 搜索s.grxrc.com结果页面右侧广告，支持补位
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoSearchResultRightAdGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListLogoEnterpriseItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoSearchResultRightAdGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * LogoApi - factory interface
 * @export
 */
export const LogoApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取广告列表
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {number} [adType] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoAdlistGet(page?: number, pageSize?: number, districtId?: BussDistrict, adType?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>> {
            return LogoApiFp(configuration).apiLogoAdlistGet(page, pageSize, districtId, adType, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 点击logo
         * @param {number} [logoID] logoID
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoClickPost(logoID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return LogoApiFp(configuration).apiLogoClickPost(logoID, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary app load页面 弹出广告
         * @param {string} [url] 
         * @param {number} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoLoadingADGet(url?: string, from?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultLoadingADDto>> {
            return LogoApiFp(configuration).apiLogoLoadingADGet(url, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 广告Logo职位点击
         * @param {LogoPositionLogDto} [body] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionClickPost(body?: LogoPositionLogDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return LogoApiFp(configuration).apiLogoPositionClickPost(body, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 广告Logo职位曝光度
         * @param {LogoPositionExposureDto} [body] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionExposurePost(body?: LogoPositionExposureDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return LogoApiFp(configuration).apiLogoPositionExposurePost(body, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索s.grxrc.com结果页面右侧广告，支持补位
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoSearchResultRightAdGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListLogoEnterpriseItemDto>> {
            return LogoApiFp(configuration).apiLogoSearchResultRightAdGet(districtId, from, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * LogoApi - object-oriented interface
 * @export
 * @class LogoApi
 * @extends {BaseAPI}
 */
export class LogoApi extends BaseAPI {
    /**
     * 
     * @summary 获取广告列表
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {BussDistrict} [districtId] 
     * @param {number} [adType] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoAdlistGet(page?: number, pageSize?: number, districtId?: BussDistrict, adType?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>> {
        return LogoApiFp(this.configuration).apiLogoAdlistGet(page, pageSize, districtId, adType, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 点击logo
     * @param {number} [logoID] logoID
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoClickPost(logoID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return LogoApiFp(this.configuration).apiLogoClickPost(logoID, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary app load页面 弹出广告
     * @param {string} [url] 
     * @param {number} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoLoadingADGet(url?: string, from?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultLoadingADDto>> {
        return LogoApiFp(this.configuration).apiLogoLoadingADGet(url, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 广告Logo职位点击
     * @param {LogoPositionLogDto} [body] 
     * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoPositionClickPost(body?: LogoPositionLogDto, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return LogoApiFp(this.configuration).apiLogoPositionClickPost(body, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 广告Logo职位曝光度
     * @param {LogoPositionExposureDto} [body] 
     * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoPositionExposurePost(body?: LogoPositionExposureDto, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return LogoApiFp(this.configuration).apiLogoPositionExposurePost(body, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 搜索s.grxrc.com结果页面右侧广告，支持补位
     * @param {BussDistrict} [districtId] 网联地址
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoSearchResultRightAdGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListLogoEnterpriseItemDto>> {
        return LogoApiFp(this.configuration).apiLogoSearchResultRightAdGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
}
