/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
/**
 * SearchApi - axios parameter creator
 * @export
 */
export const SearchApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 查寻分词搜索有没有漏了
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSearchAnalyzePositionCheckCountGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Search/AnalyzePositionCheckCount`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询索引条数
         * @param {number} [indexType] 1:企业索引 2:职位索引 3:简历索引 4:正在招聘职位和OnlinePosition对比
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSearchIndexCountCheckGet: async (indexType?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Search/IndexCountCheck`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (indexType !== undefined) {
                localVarQueryParameter['indexType'] = indexType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分词结果
         * @param {string} [keyword] 搜索关键字
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSearchKeyWordAnalyzeGet: async (keyword?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Search/KeyWordAnalyze`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SearchApi - functional programming interface
 * @export
 */
export const SearchApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 查寻分词搜索有没有漏了
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSearchAnalyzePositionCheckCountGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<string>>> {
            const localVarAxiosArgs = await SearchApiAxiosParamCreator(configuration).apiSearchAnalyzePositionCheckCountGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 查询索引条数
         * @param {number} [indexType] 1:企业索引 2:职位索引 3:简历索引 4:正在招聘职位和OnlinePosition对比
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSearchIndexCountCheckGet(indexType?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<string>>> {
            const localVarAxiosArgs = await SearchApiAxiosParamCreator(configuration).apiSearchIndexCountCheckGet(indexType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分词结果
         * @param {string} [keyword] 搜索关键字
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSearchKeyWordAnalyzeGet(keyword?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<string>>> {
            const localVarAxiosArgs = await SearchApiAxiosParamCreator(configuration).apiSearchKeyWordAnalyzeGet(keyword, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SearchApi - factory interface
 * @export
 */
export const SearchApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 查寻分词搜索有没有漏了
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSearchAnalyzePositionCheckCountGet(options?: AxiosRequestConfig): Promise<AxiosResponse<string>> {
            return SearchApiFp(configuration).apiSearchAnalyzePositionCheckCountGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询索引条数
         * @param {number} [indexType] 1:企业索引 2:职位索引 3:简历索引 4:正在招聘职位和OnlinePosition对比
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSearchIndexCountCheckGet(indexType?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<string>> {
            return SearchApiFp(configuration).apiSearchIndexCountCheckGet(indexType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分词结果
         * @param {string} [keyword] 搜索关键字
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSearchKeyWordAnalyzeGet(keyword?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<string>> {
            return SearchApiFp(configuration).apiSearchKeyWordAnalyzeGet(keyword, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SearchApi - object-oriented interface
 * @export
 * @class SearchApi
 * @extends {BaseAPI}
 */
export class SearchApi extends BaseAPI {
    /**
     * 
     * @summary 查寻分词搜索有没有漏了
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SearchApi
     */
    public async apiSearchAnalyzePositionCheckCountGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<string>> {
        return SearchApiFp(this.configuration).apiSearchAnalyzePositionCheckCountGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 查询索引条数
     * @param {number} [indexType] 1:企业索引 2:职位索引 3:简历索引 4:正在招聘职位和OnlinePosition对比
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SearchApi
     */
    public async apiSearchIndexCountCheckGet(indexType?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<string>> {
        return SearchApiFp(this.configuration).apiSearchIndexCountCheckGet(indexType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分词结果
     * @param {string} [keyword] 搜索关键字
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SearchApi
     */
    public async apiSearchKeyWordAnalyzeGet(keyword?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<string>> {
        return SearchApiFp(this.configuration).apiSearchKeyWordAnalyzeGet(keyword, options).then((request) => request(this.axios, this.basePath));
    }
}
