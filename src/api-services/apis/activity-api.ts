/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApiResultModelBoolean } from '../models';
import { BussDistrict } from '../models';
import { JobseekerApplication } from '../models';
import { RestfulResultListString } from '../models';
import { RestfulResultWebsiteWeChatInfo } from '../models';
/**
 * ActivityApi - axios parameter creator
 * @export
 */
export const ActivityApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取赴港岗位信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiActivityJobintentionlistGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/activity/jobintentionlist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 添加求职者报名信息
         * @param {JobseekerApplication} [body] 求职者报名信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiActivityJobseekerapplicationPost: async (body?: JobseekerApplication, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/activity/jobseekerapplication`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取站点微信信息
         * @param {BussDistrict} [district] 站点ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiActivityWebsitewechatinfoGet: async (district?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/activity/websitewechatinfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (district !== undefined) {
                localVarQueryParameter['district'] = district;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ActivityApi - functional programming interface
 * @export
 */
export const ActivityApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取赴港岗位信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiActivityJobintentionlistGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListString>>> {
            const localVarAxiosArgs = await ActivityApiAxiosParamCreator(configuration).apiActivityJobintentionlistGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 添加求职者报名信息
         * @param {JobseekerApplication} [body] 求职者报名信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiActivityJobseekerapplicationPost(body?: JobseekerApplication, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<ApiResultModelBoolean>>> {
            const localVarAxiosArgs = await ActivityApiAxiosParamCreator(configuration).apiActivityJobseekerapplicationPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取站点微信信息
         * @param {BussDistrict} [district] 站点ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiActivityWebsitewechatinfoGet(district?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultWebsiteWeChatInfo>>> {
            const localVarAxiosArgs = await ActivityApiAxiosParamCreator(configuration).apiActivityWebsitewechatinfoGet(district, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ActivityApi - factory interface
 * @export
 */
export const ActivityApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取赴港岗位信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiActivityJobintentionlistGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListString>> {
            return ActivityApiFp(configuration).apiActivityJobintentionlistGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 添加求职者报名信息
         * @param {JobseekerApplication} [body] 求职者报名信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiActivityJobseekerapplicationPost(body?: JobseekerApplication, options?: AxiosRequestConfig): Promise<AxiosResponse<ApiResultModelBoolean>> {
            return ActivityApiFp(configuration).apiActivityJobseekerapplicationPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取站点微信信息
         * @param {BussDistrict} [district] 站点ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiActivityWebsitewechatinfoGet(district?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultWebsiteWeChatInfo>> {
            return ActivityApiFp(configuration).apiActivityWebsitewechatinfoGet(district, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ActivityApi - object-oriented interface
 * @export
 * @class ActivityApi
 * @extends {BaseAPI}
 */
export class ActivityApi extends BaseAPI {
    /**
     * 
     * @summary 获取赴港岗位信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ActivityApi
     */
    public async apiActivityJobintentionlistGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListString>> {
        return ActivityApiFp(this.configuration).apiActivityJobintentionlistGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 添加求职者报名信息
     * @param {JobseekerApplication} [body] 求职者报名信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ActivityApi
     */
    public async apiActivityJobseekerapplicationPost(body?: JobseekerApplication, options?: AxiosRequestConfig) : Promise<AxiosResponse<ApiResultModelBoolean>> {
        return ActivityApiFp(this.configuration).apiActivityJobseekerapplicationPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取站点微信信息
     * @param {BussDistrict} [district] 站点ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ActivityApi
     */
    public async apiActivityWebsitewechatinfoGet(district?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultWebsiteWeChatInfo>> {
        return ActivityApiFp(this.configuration).apiActivityWebsitewechatinfoGet(district, options).then((request) => request(this.axios, this.basePath));
    }
}
