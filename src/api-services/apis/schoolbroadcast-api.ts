/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RestfulResultPagedListSchoolBroadcastItemOutput } from '../models';
import { RestfulResultSchoolBroadcastOutput } from '../models';
/**
 * SchoolbroadcastApi - axios parameter creator
 * @export
 */
export const SchoolbroadcastApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 校园直播详细数据
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchoolbroadcastGetbyidGet: async (id?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schoolbroadcast/getbyid`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 校园直播分页数据
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchoolbroadcastGetpagelistGet: async (page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schoolbroadcast/getpagelist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SchoolbroadcastApi - functional programming interface
 * @export
 */
export const SchoolbroadcastApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 校园直播详细数据
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchoolbroadcastGetbyidGet(id?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSchoolBroadcastOutput>>> {
            const localVarAxiosArgs = await SchoolbroadcastApiAxiosParamCreator(configuration).apiSchoolbroadcastGetbyidGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 校园直播分页数据
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchoolbroadcastGetpagelistGet(page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSchoolBroadcastItemOutput>>> {
            const localVarAxiosArgs = await SchoolbroadcastApiAxiosParamCreator(configuration).apiSchoolbroadcastGetpagelistGet(page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SchoolbroadcastApi - factory interface
 * @export
 */
export const SchoolbroadcastApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 校园直播详细数据
         * @param {number} [id] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchoolbroadcastGetbyidGet(id?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSchoolBroadcastOutput>> {
            return SchoolbroadcastApiFp(configuration).apiSchoolbroadcastGetbyidGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 校园直播分页数据
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchoolbroadcastGetpagelistGet(page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSchoolBroadcastItemOutput>> {
            return SchoolbroadcastApiFp(configuration).apiSchoolbroadcastGetpagelistGet(page, pageSize, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SchoolbroadcastApi - object-oriented interface
 * @export
 * @class SchoolbroadcastApi
 * @extends {BaseAPI}
 */
export class SchoolbroadcastApi extends BaseAPI {
    /**
     * 
     * @summary 校园直播详细数据
     * @param {number} [id] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchoolbroadcastApi
     */
    public async apiSchoolbroadcastGetbyidGet(id?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSchoolBroadcastOutput>> {
        return SchoolbroadcastApiFp(this.configuration).apiSchoolbroadcastGetbyidGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 校园直播分页数据
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchoolbroadcastApi
     */
    public async apiSchoolbroadcastGetpagelistGet(page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSchoolBroadcastItemOutput>> {
        return SchoolbroadcastApiFp(this.configuration).apiSchoolbroadcastGetpagelistGet(page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
}
