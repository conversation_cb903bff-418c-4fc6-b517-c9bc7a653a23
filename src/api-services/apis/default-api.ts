/* tslint:disable */
/* eslint-disable */
/**
 * 所有接口
 * 广西人才网 - 中国广西人才市场官方网站
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { HotPositionKeywordsPlatform } from '../models';
import { MeetingDisplayTerms } from '../models';
import { ParkPositionRequest } from '../models';
import { RestfulResultDataBoardOverviewOutput } from '../models';
import { RestfulResultEnterpriseParkDto } from '../models';
import { RestfulResultListBaseInfoOutput } from '../models';
import { RestfulResultListHotPositionKeywordsOutput } from '../models';
import { RestfulResultListKeywordItemDto } from '../models';
import { RestfulResultMeetingDetailDto } from '../models';
import { RestfulResultPagedListIndexPositionListItem } from '../models';
import { RestfulResultPagedListLogoEnterpriseParkItemDto } from '../models';
import { RestfulResultPagedListLogoEnterpriseParkItemShortDto } from '../models';
import { RestfulResultPagedListO2OMeetingListItemDto } from '../models';
import { RestfulResultPagedListParkMeetingListItemDto } from '../models';
import { RestfulResultPagedListUniversityOutput } from '../models';
import { RestfulResultPagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto } from '../models';
import { RestfulResultPagerListWithEmptyListIndexPositionListItemIndexPositionListItem } from '../models';
import { RestfulResultUniversityStatisticsOutput } from '../models';
/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 根据产业园的GUID获取该产业园的热门职位关键词列表
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {HotPositionKeywordsPlatform} platform 平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet: async (industrialParkGuid: string, platform: HotPositionKeywordsPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industrialParkGuid' is not null or undefined
            if (industrialParkGuid === null || industrialParkGuid === undefined) {
                throw new RequiredError('industrialParkGuid','Required parameter industrialParkGuid was null or undefined when calling apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet.');
            }
            // verify required parameter 'platform' is not null or undefined
            if (platform === null || platform === undefined) {
                throw new RequiredError('platform','Required parameter platform was null or undefined when calling apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet.');
            }
            const localVarPath = `/api/industrialPark/baseInfo/hotPositionKeywords/{industrialParkGuid}/{platform}`
                .replace(`{${"industrialParkGuid"}}`, encodeURIComponent(String(industrialParkGuid)))
                .replace(`{${"platform"}}`, encodeURIComponent(String(platform)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取所有产业园的基本信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkBaseInfoListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/baseInfo/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取统计概况
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet: async (industrialParkGuid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industrialParkGuid' is not null or undefined
            if (industrialParkGuid === null || industrialParkGuid === undefined) {
                throw new RequiredError('industrialParkGuid','Required parameter industrialParkGuid was null or undefined when calling apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet.');
            }
            const localVarPath = `/api/industrialPark/baseInfo/overviewStatistics/{industrialParkGuid}`
                .replace(`{${"industrialParkGuid"}}`, encodeURIComponent(String(industrialParkGuid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 产业园首页获取名企
         * @param {BussDistrict} [bussDistrict] 
         * @param {string} [parkGuid] 园区id
         * @param {ApplicationPlatform} [from] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkFamousEnterpriselistGet: async (bussDistrict?: BussDistrict, parkGuid?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/famousEnterpriselist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (bussDistrict !== undefined) {
                localVarQueryParameter['bussDistrict'] = bussDistrict;
            }

            if (parkGuid !== undefined) {
                localVarQueryParameter['parkGuid'] = parkGuid;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取行业字典信息 只拉取一级大行业
         * @param {boolean} [withCache] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkIndustryOnlyOneDataGet: async (withCache?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/industryOnlyOneData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (withCache !== undefined) {
                localVarQueryParameter['withCache'] = withCache;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘会详情
         * @param {number} [articleID] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkMeetingdetailGet: async (articleID?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/meetingdetail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取产业园招聘会列表
         * @param {string} industrialParkGuid 
         * @param {number} pageIndex 
         * @param {number} pageSize 
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet: async (industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industrialParkGuid' is not null or undefined
            if (industrialParkGuid === null || industrialParkGuid === undefined) {
                throw new RequiredError('industrialParkGuid','Required parameter industrialParkGuid was null or undefined when calling apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            // verify required parameter 'pageIndex' is not null or undefined
            if (pageIndex === null || pageIndex === undefined) {
                throw new RequiredError('pageIndex','Required parameter pageIndex was null or undefined when calling apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            // verify required parameter 'pageSize' is not null or undefined
            if (pageSize === null || pageSize === undefined) {
                throw new RequiredError('pageSize','Required parameter pageSize was null or undefined when calling apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            const localVarPath = `/api/industrialPark/meetingpage/{industrialParkGuid}/{pageIndex}/{pageSize}`
                .replace(`{${"industrialParkGuid"}}`, encodeURIComponent(String(industrialParkGuid)))
                .replace(`{${"pageIndex"}}`, encodeURIComponent(String(pageIndex)))
                .replace(`{${"pageSize"}}`, encodeURIComponent(String(pageSize)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (meetingDisplayTerms !== undefined) {
                localVarQueryParameter['meetingDisplayTerms'] = meetingDisplayTerms;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 行业分类  产业园名企
         * @param {number} [industryId] 行业id
         * @param {string} [parkGuid] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkParklistByIndustryIdGet: async (industryId?: number, parkGuid?: string, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/parklistByIndustryId`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (industryId !== undefined) {
                localVarQueryParameter['IndustryId'] = industryId;
            }

            if (parkGuid !== undefined) {
                localVarQueryParameter['parkGuid'] = parkGuid;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位推荐
         * @param {string} [park] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkRecommendpositionPost: async (park?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/recommendposition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (park !== undefined) {
                localVarQueryParameter['park'] = park;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取产业园校园招聘会列表
         * @param {string} industrialParkGuid 产业园GUID
         * @param {number} pageIndex 页码
         * @param {number} pageSize 每页大小
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会显示类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet: async (industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industrialParkGuid' is not null or undefined
            if (industrialParkGuid === null || industrialParkGuid === undefined) {
                throw new RequiredError('industrialParkGuid','Required parameter industrialParkGuid was null or undefined when calling apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            // verify required parameter 'pageIndex' is not null or undefined
            if (pageIndex === null || pageIndex === undefined) {
                throw new RequiredError('pageIndex','Required parameter pageIndex was null or undefined when calling apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            // verify required parameter 'pageSize' is not null or undefined
            if (pageSize === null || pageSize === undefined) {
                throw new RequiredError('pageSize','Required parameter pageSize was null or undefined when calling apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            const localVarPath = `/api/industrialPark/schoolmeetingpage/{industrialParkGuid}/{pageIndex}/{pageSize}`
                .replace(`{${"industrialParkGuid"}}`, encodeURIComponent(String(industrialParkGuid)))
                .replace(`{${"pageIndex"}}`, encodeURIComponent(String(pageIndex)))
                .replace(`{${"pageSize"}}`, encodeURIComponent(String(pageSize)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (meetingDisplayTerms !== undefined) {
                localVarQueryParameter['meetingDisplayTerms'] = meetingDisplayTerms;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位搜索
         * @param {ParkPositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkSearchPost: async (body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业搜索
         * @param {ParkPositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkSearchcompanyPost: async (body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/searchcompany`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 名企 产业园搜索职位或企业
         * @param {ApplicationPlatform} [from] 
         * @param {string} [parkGuid] 园区guid
         * @param {string} [keyword] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkSearchparklistGet: async (from?: ApplicationPlatform, parkGuid?: string, keyword?: string, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/industrialPark/searchparklist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (parkGuid !== undefined) {
                localVarQueryParameter['parkGuid'] = parkGuid;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询产业园大学基本信息
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {number} pageIndex 页码
         * @param {number} pageSize 页容量
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet: async (industrialParkGuid: string, pageIndex: number, pageSize: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industrialParkGuid' is not null or undefined
            if (industrialParkGuid === null || industrialParkGuid === undefined) {
                throw new RequiredError('industrialParkGuid','Required parameter industrialParkGuid was null or undefined when calling apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            // verify required parameter 'pageIndex' is not null or undefined
            if (pageIndex === null || pageIndex === undefined) {
                throw new RequiredError('pageIndex','Required parameter pageIndex was null or undefined when calling apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            // verify required parameter 'pageSize' is not null or undefined
            if (pageSize === null || pageSize === undefined) {
                throw new RequiredError('pageSize','Required parameter pageSize was null or undefined when calling apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet.');
            }
            const localVarPath = `/api/industrialPark/university/page/{industrialParkGuid}/{pageIndex}/{pageSize}`
                .replace(`{${"industrialParkGuid"}}`, encodeURIComponent(String(industrialParkGuid)))
                .replace(`{${"pageIndex"}}`, encodeURIComponent(String(pageIndex)))
                .replace(`{${"pageSize"}}`, encodeURIComponent(String(pageSize)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取产业园大学统计信息
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet: async (industrialParkGuid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'industrialParkGuid' is not null or undefined
            if (industrialParkGuid === null || industrialParkGuid === undefined) {
                throw new RequiredError('industrialParkGuid','Required parameter industrialParkGuid was null or undefined when calling apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet.');
            }
            const localVarPath = `/api/industrialPark/university/statistics/{industrialParkGuid}`
                .replace(`{${"industrialParkGuid"}}`, encodeURIComponent(String(industrialParkGuid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 根据产业园的GUID获取该产业园的热门职位关键词列表
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {HotPositionKeywordsPlatform} platform 平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet(industrialParkGuid: string, platform: HotPositionKeywordsPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListHotPositionKeywordsOutput>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet(industrialParkGuid, platform, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取所有产业园的基本信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkBaseInfoListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListBaseInfoOutput>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkBaseInfoListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取统计概况
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet(industrialParkGuid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultDataBoardOverviewOutput>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet(industrialParkGuid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 产业园首页获取名企
         * @param {BussDistrict} [bussDistrict] 
         * @param {string} [parkGuid] 园区id
         * @param {ApplicationPlatform} [from] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkFamousEnterpriselistGet(bussDistrict?: BussDistrict, parkGuid?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseParkItemShortDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkFamousEnterpriselistGet(bussDistrict, parkGuid, from, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取行业字典信息 只拉取一级大行业
         * @param {boolean} [withCache] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkIndustryOnlyOneDataGet(withCache?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListKeywordItemDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkIndustryOnlyOneDataGet(withCache, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘会详情
         * @param {number} [articleID] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkMeetingdetailGet(articleID?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultMeetingDetailDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkMeetingdetailGet(articleID, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取产业园招聘会列表
         * @param {string} industrialParkGuid 
         * @param {number} pageIndex 
         * @param {number} pageSize 
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListParkMeetingListItemDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, meetingDisplayTerms, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 行业分类  产业园名企
         * @param {number} [industryId] 行业id
         * @param {string} [parkGuid] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkParklistByIndustryIdGet(industryId?: number, parkGuid?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseParkItemDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkParklistByIndustryIdGet(industryId, parkGuid, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位推荐
         * @param {string} [park] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkRecommendpositionPost(park?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkRecommendpositionPost(park, from, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取产业园校园招聘会列表
         * @param {string} industrialParkGuid 产业园GUID
         * @param {number} pageIndex 页码
         * @param {number} pageSize 每页大小
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会显示类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListO2OMeetingListItemDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, meetingDisplayTerms, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位搜索
         * @param {ParkPositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSearchPost(body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagerListWithEmptyListIndexPositionListItemIndexPositionListItem>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkSearchPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业搜索
         * @param {ParkPositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSearchcompanyPost(body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkSearchcompanyPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 名企 产业园搜索职位或企业
         * @param {ApplicationPlatform} [from] 
         * @param {string} [parkGuid] 园区guid
         * @param {string} [keyword] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSearchparklistGet(from?: ApplicationPlatform, parkGuid?: string, keyword?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterpriseParkDto>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkSearchparklistGet(from, parkGuid, keyword, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分页查询产业园大学基本信息
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {number} pageIndex 页码
         * @param {number} pageSize 页容量
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListUniversityOutput>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取产业园大学统计信息
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet(industrialParkGuid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultUniversityStatisticsOutput>>> {
            const localVarAxiosArgs = await DefaultApiAxiosParamCreator(configuration).apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet(industrialParkGuid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 根据产业园的GUID获取该产业园的热门职位关键词列表
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {HotPositionKeywordsPlatform} platform 平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet(industrialParkGuid: string, platform: HotPositionKeywordsPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListHotPositionKeywordsOutput>> {
            return DefaultApiFp(configuration).apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet(industrialParkGuid, platform, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取所有产业园的基本信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkBaseInfoListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListBaseInfoOutput>> {
            return DefaultApiFp(configuration).apiIndustrialParkBaseInfoListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取统计概况
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet(industrialParkGuid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultDataBoardOverviewOutput>> {
            return DefaultApiFp(configuration).apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet(industrialParkGuid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 产业园首页获取名企
         * @param {BussDistrict} [bussDistrict] 
         * @param {string} [parkGuid] 园区id
         * @param {ApplicationPlatform} [from] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkFamousEnterpriselistGet(bussDistrict?: BussDistrict, parkGuid?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseParkItemShortDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkFamousEnterpriselistGet(bussDistrict, parkGuid, from, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取行业字典信息 只拉取一级大行业
         * @param {boolean} [withCache] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkIndustryOnlyOneDataGet(withCache?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkIndustryOnlyOneDataGet(withCache, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘会详情
         * @param {number} [articleID] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkMeetingdetailGet(articleID?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultMeetingDetailDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkMeetingdetailGet(articleID, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取产业园招聘会列表
         * @param {string} industrialParkGuid 
         * @param {number} pageIndex 
         * @param {number} pageSize 
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListParkMeetingListItemDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, meetingDisplayTerms, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 行业分类  产业园名企
         * @param {number} [industryId] 行业id
         * @param {string} [parkGuid] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkParklistByIndustryIdGet(industryId?: number, parkGuid?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseParkItemDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkParklistByIndustryIdGet(industryId, parkGuid, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位推荐
         * @param {string} [park] 
         * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkRecommendpositionPost(park?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
            return DefaultApiFp(configuration).apiIndustrialParkRecommendpositionPost(park, from, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取产业园校园招聘会列表
         * @param {string} industrialParkGuid 产业园GUID
         * @param {number} pageIndex 页码
         * @param {number} pageSize 每页大小
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会显示类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListO2OMeetingListItemDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, meetingDisplayTerms, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位搜索
         * @param {ParkPositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSearchPost(body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagerListWithEmptyListIndexPositionListItemIndexPositionListItem>> {
            return DefaultApiFp(configuration).apiIndustrialParkSearchPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业搜索
         * @param {ParkPositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSearchcompanyPost(body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkSearchcompanyPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 名企 产业园搜索职位或企业
         * @param {ApplicationPlatform} [from] 
         * @param {string} [parkGuid] 园区guid
         * @param {string} [keyword] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkSearchparklistGet(from?: ApplicationPlatform, parkGuid?: string, keyword?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterpriseParkDto>> {
            return DefaultApiFp(configuration).apiIndustrialParkSearchparklistGet(from, parkGuid, keyword, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询产业园大学基本信息
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {number} pageIndex 页码
         * @param {number} pageSize 页容量
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListUniversityOutput>> {
            return DefaultApiFp(configuration).apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取产业园大学统计信息
         * @param {string} industrialParkGuid 产业园的GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet(industrialParkGuid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultUniversityStatisticsOutput>> {
            return DefaultApiFp(configuration).apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet(industrialParkGuid, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
    /**
     * 
     * @summary 根据产业园的GUID获取该产业园的热门职位关键词列表
     * @param {string} industrialParkGuid 产业园的GUID
     * @param {HotPositionKeywordsPlatform} platform 平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet(industrialParkGuid: string, platform: HotPositionKeywordsPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListHotPositionKeywordsOutput>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkBaseInfoHotPositionKeywordsIndustrialParkGuidPlatformGet(industrialParkGuid, platform, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取所有产业园的基本信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkBaseInfoListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListBaseInfoOutput>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkBaseInfoListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取统计概况
     * @param {string} industrialParkGuid 产业园的GUID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet(industrialParkGuid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultDataBoardOverviewOutput>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkBaseInfoOverviewStatisticsIndustrialParkGuidGet(industrialParkGuid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 产业园首页获取名企
     * @param {BussDistrict} [bussDistrict] 
     * @param {string} [parkGuid] 园区id
     * @param {ApplicationPlatform} [from] 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkFamousEnterpriselistGet(bussDistrict?: BussDistrict, parkGuid?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseParkItemShortDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkFamousEnterpriselistGet(bussDistrict, parkGuid, from, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取行业字典信息 只拉取一级大行业
     * @param {boolean} [withCache] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkIndustryOnlyOneDataGet(withCache?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkIndustryOnlyOneDataGet(withCache, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘会详情
     * @param {number} [articleID] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkMeetingdetailGet(articleID?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultMeetingDetailDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkMeetingdetailGet(articleID, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取产业园招聘会列表
     * @param {string} industrialParkGuid 
     * @param {number} pageIndex 
     * @param {number} pageSize 
     * @param {MeetingDisplayTerms} [meetingDisplayTerms] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListParkMeetingListItemDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkMeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, meetingDisplayTerms, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 行业分类  产业园名企
     * @param {number} [industryId] 行业id
     * @param {string} [parkGuid] 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkParklistByIndustryIdGet(industryId?: number, parkGuid?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseParkItemDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkParklistByIndustryIdGet(industryId, parkGuid, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位推荐
     * @param {string} [park] 
     * @param {ApplicationPlatform} [from] &lt;br /&gt;&amp;nbsp; PC &#x3D; 0&lt;br /&gt;&amp;nbsp; Wechat &#x3D; 1&lt;br /&gt;&amp;nbsp; Android &#x3D; 2&lt;br /&gt;&amp;nbsp; IOS &#x3D; 3&lt;br /&gt;&amp;nbsp; H5 &#x3D; 4&lt;br /&gt;&amp;nbsp; TikTok &#x3D; 6&lt;br /&gt;&amp;nbsp; Alipay &#x3D; 7&lt;br /&gt;&amp;nbsp; PCP2P &#x3D; 10&lt;br /&gt;&amp;nbsp; WechatP2P &#x3D; 11&lt;br /&gt;&amp;nbsp; AndroidP2P &#x3D; 12&lt;br /&gt;&amp;nbsp; IOSP2P &#x3D; 13&lt;br /&gt;&amp;nbsp; H5P2P &#x3D; 14&lt;br /&gt;
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkRecommendpositionPost(park?: string, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkRecommendpositionPost(park, from, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取产业园校园招聘会列表
     * @param {string} industrialParkGuid 产业园GUID
     * @param {number} pageIndex 页码
     * @param {number} pageSize 每页大小
     * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会显示类型
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListO2OMeetingListItemDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkSchoolmeetingpageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, meetingDisplayTerms, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位搜索
     * @param {ParkPositionRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkSearchPost(body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagerListWithEmptyListIndexPositionListItemIndexPositionListItem>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkSearchPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业搜索
     * @param {ParkPositionRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkSearchcompanyPost(body?: ParkPositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagerListWithEmptyListIndexEnterpriseItemDtoIndexEnterpriseItemDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkSearchcompanyPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 名企 产业园搜索职位或企业
     * @param {ApplicationPlatform} [from] 
     * @param {string} [parkGuid] 园区guid
     * @param {string} [keyword] 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkSearchparklistGet(from?: ApplicationPlatform, parkGuid?: string, keyword?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterpriseParkDto>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkSearchparklistGet(from, parkGuid, keyword, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分页查询产业园大学基本信息
     * @param {string} industrialParkGuid 产业园的GUID
     * @param {number} pageIndex 页码
     * @param {number} pageSize 页容量
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid: string, pageIndex: number, pageSize: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListUniversityOutput>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkUniversityPageIndustrialParkGuidPageIndexPageSizeGet(industrialParkGuid, pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取产业园大学统计信息
     * @param {string} industrialParkGuid 产业园的GUID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public async apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet(industrialParkGuid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultUniversityStatisticsOutput>> {
        return DefaultApiFp(this.configuration).apiIndustrialParkUniversityStatisticsIndustrialParkGuidGet(industrialParkGuid, options).then((request) => request(this.axios, this.basePath));
    }
}
