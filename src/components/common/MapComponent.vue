<template>
  <div class="map-component">
    <div 
      :id="mapId"
      class="map-container"
      :style="{ width: width, height: height }"
    >
      <!-- 地图加载中的占位符 -->
      <div v-if="isLoading" class="map-placeholder">
        <div class="location-info">
          <i class="location-icon">📍</i>
          <div class="info-content">
            <h3 class="company-name">{{ title }}</h3>
            <p class="address">{{ address }}</p>
            <div class="coordinates" v-if="showCoordinates">
              经度: {{ longitude }} | 纬度: {{ latitude }}
            </div>
          </div>
        </div>
        <div class="map-footer">
          <span class="map-brand">地图加载中...</span>
        </div>
      </div>

      <!-- 地图加载失败的占位符 -->
      <div v-else-if="loadError" class="map-error">
        <div class="error-info">
          <i class="error-icon">⚠️</i>
          <div class="error-content">
            <h3 class="error-title">地图加载失败</h3>
            <p class="error-message">{{ loadError }}</p>
            <div class="fallback-info">
              <h4 class="company-name">{{ title }}</h4>
              <p class="address">{{ address }}</p>
              <div class="coordinates" v-if="showCoordinates">
                经度: {{ longitude }} | 纬度: {{ latitude }}
              </div>
            </div>
            <button class="retry-btn" @click="initBaiduMap">重试加载</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { loadBaiduMap } from '@/utils/baiduMap'

// 百度地图类型声明
declare global {
  interface Window {
    BMapGL: any
    onBMapCallback: () => void
  }
  const BMapGL: any
}

interface Props {
  title?: string
  address?: string
  longitude?: number
  latitude?: number
  width?: string
  height?: string
  showCoordinates?: boolean
  zoom?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '公司地址',
  address: '地址信息',
  longitude: 108.374104,
  latitude: 22.805206,
  width: '100%',
  height: '400px',
  showCoordinates: false,
  zoom: 15
})

// 生成唯一的地图容器ID
const mapId = ref(`map-${Math.random().toString(36).substr(2, 9)}`)
const isLoading = ref(true)
const loadError = ref('')
const mapInstance = ref(null)

// 初始化百度地图
const initBaiduMap = async () => {
  try {
    isLoading.value = true
    loadError.value = ''
    
    // 验证坐标有效性
    if (!props.longitude || !props.latitude || 
        props.longitude === 0 || props.latitude === 0) {
      throw new Error('无效的地图坐标')
    }

    // 加载百度地图SDK
    const baiduSdk = await loadBaiduMap()
    
    if (!baiduSdk || !window.BMapGL) {
      throw new Error('百度地图SDK加载失败')
    }

    // 创建地图实例
    const map = new window.BMapGL.Map(mapId.value, {
      enableMapClick: true
    })
    
    // 设置地图中心点和缩放级别
    const point = new window.BMapGL.Point(props.longitude, props.latitude)
    map.centerAndZoom(point, props.zoom)
    
    // 启用地图功能
    map.enableScrollWheelZoom(true)
    map.enableKeyboard()
    map.enableInertialDragging()
    map.enableContinuousZoom()
    
    // 添加地图控件
    map.addControl(new window.BMapGL.NavigationControl3D())
    map.addControl(new window.BMapGL.ZoomControl())
    map.addControl(new window.BMapGL.ScaleControl())
    
    // 创建标注
    const marker = new window.BMapGL.Marker(point)
    map.addOverlay(marker)
    
    // 创建信息窗口
    const infoWindow = new window.BMapGL.InfoWindow(
      `<div style="padding: 10px; line-height: 1.5;">
        <h4 style="margin: 0 0 8px 0; color: #333;">${props.title}</h4>
        <p style="margin: 0 0 8px 0; color: #666;">${props.address}</p>
        ${props.showCoordinates ? 
          `<p style="margin: 0; color: #999; font-size: 12px;">
            经度: ${props.longitude} | 纬度: ${props.latitude}
          </p>` : ''
        }
      </div>`,
      {
        width: 280,
        height: 100,
        title: props.title
      }
    )
    
    // 标注点击事件
    marker.addEventListener('click', () => {
      map.openInfoWindow(infoWindow, point)
    })
    
    // 默认显示信息窗口
    setTimeout(() => {
      map.openInfoWindow(infoWindow, point)
    }, 500)
    
    mapInstance.value = map
    isLoading.value = false
    
    console.log('百度地图初始化成功:', {
      title: props.title,
      address: props.address,
      longitude: props.longitude,
      latitude: props.latitude
    })
    
  } catch (error) {
    console.error('地图初始化失败:', error)
    isLoading.value = false
    loadError.value = error.message || '地图加载失败，请检查网络连接'
  }
}

// 组件挂载时初始化地图
onMounted(() => {
  initBaiduMap()
})

// 组件卸载时清理
onUnmounted(() => {
  if (mapInstance.value) {
    mapInstance.value.destroy()
    mapInstance.value = null
  }
})
</script>

<style scoped lang="scss">
.map-component {
  .map-container {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
  }

  .map-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        radial-gradient(circle at 25% 25%, rgba(66, 165, 245, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(156, 39, 176, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }
  }

  .map-error {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .location-info,
  .error-info {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 90%;
    
    .location-icon,
    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }
    
    .company-name,
    .error-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    .address,
    .error-message {
      font-size: 14px;
      color: #666;
      margin: 0 0 8px 0;
    }
    
    .coordinates {
      font-size: 12px;
      color: #999;
      margin: 0;
    }
  }

  .fallback-info {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    
    .company-name {
      font-size: 16px;
      margin-bottom: 6px;
    }
  }

  .retry-btn {
    margin-top: 16px;
    padding: 8px 20px;
    background: #3399ff;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    
    &:hover {
      background: #2d8cee;
      transform: translateY(-1px);
    }
  }

  .map-footer {
    position: absolute;
    bottom: 8px;
    left: 8px;
    
    .map-brand {
      font-size: 11px;
      color: #666;
      background: rgba(255, 255, 255, 0.9);
      padding: 3px 8px;
      border-radius: 4px;
      backdrop-filter: blur(4px);
    }
  }
}

// 百度地图容器样式
:deep(.BMap_mask) {
  border-radius: 8px;
}

:deep(.anchorBL) {
  display: none; // 隐藏百度地图版权信息
}
</style> 