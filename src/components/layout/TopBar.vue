<template>
  <!-- 顶部区域 -->
  <div class="bg-white border-b">
    <div class="container-lg py-2">
      <div class="flex items-center justify-between text-sm text-gray-600">
        <div class="flex items-center space-x-4">
          <CitySelector class="hidden md:block" />
          <span>您好，欢迎来到广西壮族自治区"数智人社"—广西就业平台！</span>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 bg-#E4E7F1 rounded-20px px-15px py-7px cursor-pointer"
          >
            <img
              src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
              class="w-21px h-21px"
              alt=""
            />
            <span>AI智能客服</span>
          </div>
          <!-- 登录状态区域 -->
          <div class="flex items-center space-x-2">
            <!-- 未登录状态 - 显示登录按钮 -->
            <template v-if="!userStore.isLoggedIn">
              <div class="login-btn enterprise" @click="handleEnterpriseLogin">企业登录</div>
              <div class="login-btn personal" @click="handlePersonalLogin">个人登录</div>
            </template>
            <!-- 已登录状态 - 显示用户信息和下拉菜单 -->
            <template v-else>
              <el-dropdown trigger="hover" @command="userStore.handleMenuClick">
                <div class="user-info-btn" :class="userStore.userType">
                  <span>{{ userStore.displayName }}</span>
                  <el-icon class="el-icon--right ml-1">
                    <arrow-down />
                  </el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="(item, index) in userStore.menuItems"
                      :key="index"
                      :command="item"
                      :divided="item.divided"
                    >
                      
                      {{ item.label }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowDown } from '@element-plus/icons-vue'
import CitySelector from '@/components/common/CitySelector.vue'
import { useUserStore } from '@/stores/userStore'

const userStore = useUserStore()

// 企业登录
const handleEnterpriseLogin = () => {
  const enterpriseLoginUrl = import.meta.env.VITE_ENTERPRISE_LOGIN_URL
  if (enterpriseLoginUrl) {
    window.location.href = `${enterpriseLoginUrl}/login?returnUrl=${window.location.href}`
  }
}

// 个人登录
const handlePersonalLogin = () => {
  const personalLoginUrl = import.meta.env.VITE_PERSONAL_LOGIN_URL
  if (personalLoginUrl) {
    window.location.href = `${personalLoginUrl}/login?returnUrl=${window.location.href}`
  }
}
</script>

<style scoped lang="scss">
.login-btn {
  @apply cursor-pointer rounded-20px text-white text-14px px-23px py-7px;
}

.user-info-btn {
  @apply cursor-pointer rounded-20px text-white text-14px px-23px py-7px flex items-center;
  transition: all 0.2s ease;
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }
}

.enterprise {
  background: linear-gradient(301deg, #0b83fb 0%, #0a23ff 100%);
}

.personal {
  background: linear-gradient(301deg, #00ebc3 0%, #00b0ff 100%);
}
</style>