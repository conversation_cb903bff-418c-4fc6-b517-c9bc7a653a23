<template>
    <div class="more-list">

        <div class="table_box">
            <div class="tabs">
                <tabs :options="tabsList" v-model="post.type" @change="changeTab" />
            </div>
            <div>
            <el-input v-model="asyncPost.name" placeholder="请输入关键词" class="search-input" />
            <el-button @click="search">搜索</el-button>
            <el-button @click="clear">清空</el-button>
            </div>
            <Pagination :allFn="false" ref="paginationRef"
                :ApiFunction="getAPI(CareerGuidanceApi).apiNanningEmploymentServicesCareerGuidancePagePost.bind(getAPI(CareerGuidanceApi))"
                :option="post"
                :asyncOption="asyncPost"
                >
                <template #default="{ tableData, loading, empty }">
                    <div class="art-card-list" v-loading="loading">
                        <div class="art-card-item" v-for="item in tableData as CareerGuidanceListOutput[]"
                            :key="item.id" @click="goDetail(item)">
                            <artCard :title="item.title" :subtitle="item.shortCourseIntroduction" :image="item.image"
                                :subtitleHeight="'32px'" />
                        </div>
                    </div>
                    <el-empty :image-size="200" :description="'暂无数据'" v-if="empty" />
                </template>
            </Pagination>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import Pagination from '@/components/table/Pagination.vue';
import { getAPI } from '@/utils/axios-utils';
import searchBox from '@/components/public/searchBox.vue';
import { CareerGuidanceApi } from '@/api-services/home/<USER>/career-guidance-api'
import { useRouter } from 'vue-router';
import tabs from '@/components/public/tabs.vue';
import { CareerGuidanceListOutput } from '@/api-services/home/<USER>/career-guidance-list-output';

const router = useRouter();
const changeTab = (value: string) => {
    console.log(value)
}

const tabsList = [
    { name: '全部', value: 0 },
    { name: '就业指导', value: 1 },
    { name: '求职指导', value: 2 },
    { name: '职场能力', value: 3 }
];

const goDetail = (item: CareerGuidanceListOutput) => {
    router.push({
        path: '/careerGuidanceRouter/courseDetail',
        query: {
            id: item.guid
        }
    })
}

const paginationRef = ref(null)

const search = ()=>{
    paginationRef.value.asyncOptionReset()
}

const clear = ()=>{
    const {asyncOption,post} = paginationRef.value.getOriginalOption()
    for(let key in asyncPost.value){
        asyncPost.value[key] = asyncOption[key]
    }
    paginationRef.value.asyncOptionReset()
}

const post = ref({
    type: 0
})

const asyncPost =ref({
    name:''
})



</script>
<style lang="scss" scoped>
.more-list {

    .header_tab {}

    .table_box {
        .art-card-list {
            margin: 20px 0;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;

            .art-card-item {
                height: 243px;
                padding: 20px 14px;
                padding-bottom: 16px;
                background: #fff;
                border-radius: 8px 8px 8px 8px;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                cursor: pointer;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                }
            }
        }

        .tabs {
            display: flex;
            margin-top: 16px;


        }
    }
}
</style>