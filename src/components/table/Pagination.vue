<template>
  <div style="position: relative">
    <slot name="other" :tableData="tableData" :allData="allData"></slot>
    <div>
      <slot :tableData="tableData" :loading="dataObj.loading" :empty="empty" :lazyLoadingOpen="lazyLoadingOpen"></slot>
    </div>

    <slot name="footer" :tableData="tableData"  :allData="allData"></slot>
    <el-affix position="bottom" v-if="total != 0 && !lazyLoading && showAffix" :offset="offset" :target="affixTarget">
      <div class="pagination" v-if="total != 0" :class="{ flexRight: !slots.paginationleft }">
        <slot name="paginationleft" v-if="total != 0"></slot>
        <el-pagination @size-change="sizeChange" v-if="total != 0" @current-change="currentChange"
          :current-page="currPage" :page-sizes="pageSize" :page-size="post.num" background :prev-text="'上一页'"
          :next-text="'下一页'" :layout="`${layout}`" :total="total"></el-pagination>
      </div>
    </el-affix>
    <div class="pagination" v-if="total != 0 && !showAffix"  :class="{ flexRight: !slots.paginationleft }">
      <slot name="paginationleft" v-if="total != 0"></slot>
      <el-pagination @size-change="sizeChange" v-if="total != 0" @current-change="currentChange"
        :current-page="currPage" :page-sizes="pageSize" :page-size="post.num" background :prev-text="'上一页'"
        :next-text="'下一页'" :layout="`${layout}`" :total="total"></el-pagination>
    </div>
    <slot name="bottom" :tableData="tableData"></slot>
    <div class="loadingBottom" v-if="lazyLoading">
      <el-button type="text" v-if="isNoData && !lazyLoadingOpen">已经到底啦~</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  computed,
  reactive,
  toRefs,
  watch,
  getCurrentInstance,
  useSlots,
} from 'vue';
import { useAppStore } from "@/stores/appStore";
// 定义组件名称
defineOptions({
  name: 'pagination'
});

// 定义 props
interface Props {
  allFn?: boolean;
  EmptyRequired?: boolean;
  option?: Record<string, any>;
  asyncOption?: object;
  splitUrl?: boolean;
  offset?: number;
  lazyLoading?: boolean;
  changePageToTop?: boolean;
  topTo?: number;
  showLoadingTarget?: string;
  scrollTarget?: string;
  defaultPage?: number;
  affixTarget?: string;
  ApiFunction?: (data: any) => Promise<any>;
  backgroundColor?: string;
  showAffix?: boolean;
  fixedPageSize?: number;
  layout?: string;
}

const props = withDefaults(defineProps<Props>(), {
  allFn: true,
  EmptyRequired: false,
  option: () => ({}),
  splitUrl: true,
  offset: 0,
  lazyLoading: false,
  changePageToTop: true,
  topTo: 0,
  showLoadingTarget: '',
  scrollTarget: '',
  defaultPage: 1,
  affixTarget: '',
  backgroundColor: '#fff',
  showAffix: true,
  fixedPageSize: 0,
  layout: 'total, sizes, prev, pager, next, jumper'
});
const asyncOption = computed(() => props.asyncOption);

const clonePost = Object.assign({}, props.option);
const cloneAsyncPost = Object.assign({}, props.asyncOption);

const getOriginalOption = () => {
  return {
    option: Object.assign({}, clonePost),
    asyncOption: Object.assign({}, cloneAsyncPost),
  };
};

// 定义 emits
const emit = defineEmits<{
  tableDate: [data: any];
  parameterChange: [post: any];
  complete: [data: any];
  pageChange: [page: number];
}>();

const backgroundColor = computed(() => {
  return props.backgroundColor
});

const slots = useSlots();
const store = useAppStore();
const { offset } = toRefs(props);
const { proxy }: any = getCurrentInstance();

const dataObj = reactive({
  isNoData: false,
  currPage: props.option?.page || props.defaultPage,
  // 是否没有数据
  empty: false,
  tableData: [],
  post: Object.assign({}, props.option, {
    page: props.option?.page || props.defaultPage,
    num: 0,
    pageSize: 0,
    rid: '',

  }),
  total: 0,
  pageSizeName: 'pageSize',
  lazyLoadingOpen: false,
  loading: false
});

const pagesize = props.fixedPageSize ? props.fixedPageSize : Number(
  localStorage.getItem(dataObj.pageSizeName) || store.pageSize[0]
);

dataObj.post = Object.assign({}, dataObj.post, {
  num: pagesize,
  pageSize: pagesize,
});

const pageSize = computed(() => {
  if (props.fixedPageSize) {
    return [props.fixedPageSize];
  }
  if (props.lazyLoading) {
    return [15];
  } else {
    return store.pageSize;
  }
});
const allData = ref<any>(null)
const getData = (data: any) => {

  dataObj.loading = true
  dataObj.isNoData = false;
  props.lazyLoading && (dataObj.lazyLoadingOpen = true);

  data.pageIndex = data.page
  if (!props.ApiFunction) return;

  props.ApiFunction(data).then((res) => {

    let Data: any = {}
    if (res.data.code == 1) {
      Data = res.data.data
    }
    allData.value = Data
    let { items = null, data = null, count, all_number, total, allNum, totalCount } = Data

    emit('tableDate', data);
    emit('parameterChange', dataObj.post);
    dataObj.total = Number(
      count ||
      all_number ||
      total ||
      allNum || totalCount ||
      data.all_number ||
      data.count ||
      data.total ||
      data.data.total ||
      0
    );
    dataObj.tableData = data || items

    dataObj.lazyLoadingOpen = false;


    dataObj.empty = dataObj.post.page == 1 && dataObj.total == 0;
  }).catch(() => {
    dataObj.total = 0;
    dataObj.tableData = [];
    allData.value = null
    dataObj.empty = dataObj.post.page == 1 && dataObj.total == 0;
  }).finally(() => {
    dataObj.loading = false;
  });

};

watch(
  () => dataObj.post,
  (newValue, oldValue) => {
    const options = Object.assign({}, dataObj.post, asyncOption.value);

    // 如果page相等说明 变动的是其他数据 page 需重置
    if (
      newValue &&
      oldValue &&
      oldValue.page === newValue.page &&
      newValue.page !== 1
    ) {
      // dataObj.post.page = 1
      // dataObj.currPage = 1
      getData(options);
    } else {
      getData(options);
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => props.option,
  (value) => {
    dataObj.post = Object.assign({}, dataObj.post, value, {
      page: 1,
    });
    dataObj.post.page = value?.page || props.defaultPage

    if (Object.keys(value).length <= 0 && props.EmptyRequired) {
      dataObj.post = Object.assign(
        {},
        {
          page: props.defaultPage,
          num: dataObj.post.num,
          pageSize: dataObj.post.pageSize,
          rid: '',
        }
      );
    }
    dataObj.currPage = value?.page || props.defaultPage;

  },
  {
    deep: true,
  }
);

const sizeChange = (value: any) => {
  localStorage.setItem(dataObj.pageSizeName, value);
  dataObj.post = Object.assign({}, dataObj.post, {
    num: value,
    pageSize: value,
  });
};

const reset = () => {
  dataObj.post.rid = Math.random().toString();
};

const asyncOptionReset = () => {
  dataObj.post = Object.assign({}, dataObj.post, asyncOption.value);
};


const retrunItem = (key: string, keyValue: unknown) => {
  let row = dataObj.tableData.find((item) => item[key] == keyValue);
  return row;
};

const currentadd = (data: Record<string, any>) => {
  if (dataObj.total / 15 > dataObj.currPage) {
    dataObj.currPage++;
    dataObj.post = Object.assign({}, dataObj.post, data, {
      page: dataObj.currPage,
    });
    dataObj.isNoData = false;
    emit('pageChange', dataObj.currPage);
  } else {
    dataObj.isNoData = true;
    return;
  }
};

defineExpose({
  reset,
  asyncOptionReset,
  getOriginalOption,
});

const currentChange = (value: any) => {
 
  dataObj.currPage = value;
  dataObj.post = Object.assign({}, dataObj.post, {
    page: value,
  });

  if (!props.changePageToTop) {
    return;
  }
  if (props.scrollTarget) {
    try {
      let app: any = document.querySelector(props.scrollTarget);
      app.scrollTop = props.topTo;
    } catch (e) {
      console.log(e);
    }
    return;
  }
  try {
    let app: any = document.documentElement
    document.body.scrollTop = props.topTo;
    app.scrollTop = props.topTo;
  } catch (e) {
    console.log(e);
  }
};

// 暴露给模板使用的响应式数据和方法
const {
  isNoData,
  currPage,
  empty,
  tableData,
  post,
  total,
  pageSizeName,
  lazyLoadingOpen,
} = toRefs(dataObj);
</script>
<style lang="scss" scoped>
.pagination {
  display: flex;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
  text-align: right;
  height: 56px;
  line-height: 36px;
  background: v-bind(backgroundColor);
}

:deep(.el-affix--fixed) {
  z-index: 99 !important;
}

.loadingBottom {
  :deep(.el-button) {
    color: #979ea5;
    font-size: 16px;
  }

  :deep(.el-icon-loading) {
    font-size: 16px !important;
  }
}

.flexRight {
  justify-content: center;
}


:deep(.el-pagination) {
  .el-pager li {
    background-color: #fff;
    border-radius: 6px;
  }

  .el-pager li.is-active {
    background: #426EFF;
  }

  button {
    border-radius: 6px;
  }
}


:deep(.el-pagination button) {
  background: #fff !important;
}
</style>
