<script setup lang="ts">
import { ApplicationPlatform, DeliverResumeList, UserApi } from "@/api-services";
import { feature, getPcAPI } from "@/utils/axios-utils";
import { useCityStore } from "@/stores/cityStore";
import { useUserStore } from "@/stores/userStore";
import { useRouter } from "vue-router";

const router = useRouter();
const visible = defineModel('visible', {
  type: Boolean,
  default: false
})
const props = defineProps<{
  posName: string;
  posGuid: string;
}>();
const emit = defineEmits<{
  (e: "close"): void;
  (e: "success", positionGuid: string): void;
}>()

const cityStore = useCityStore();
const userStore = useUserStore();
const districtId = computed(() => cityStore.cityInfo.id);
const host = import.meta.env.VITE_PERSONAL_LOGIN_URL;

const positionName = computed(() => props.posName);
const positionGuid = computed(() => props.posGuid);
const resumeList = ref<DeliverResumeList>();
const selectResumeId = ref("");
const errorMsg = ref('')

const loading = ref(false)

// 检查用户登录状态并处理跳转
const checkLoginStatusAndRedirect = () => {
  // 如果用户未登录或不是求职者身份，跳转到个人登录页
  if (!userStore.isLoggedIn || userStore.userType !== 'personal') {
    // 构建登录页URL，携带当前页面信息作为回调
    const currentUrl = encodeURIComponent(window.location.href);
    const loginUrl = `${host}/login?returnUrl=${currentUrl}`;
    
    // 跳转到登录页
    window.location.href = loginUrl;
    return false;
  }
  return true;
};

// 监听弹窗显示状态
watch(visible, async (newVisible) => {
  if (newVisible) {
    // 弹窗显示时检查登录状态
    if (!checkLoginStatusAndRedirect()) {
      // 如果需要跳转登录，关闭弹窗
      visible.value = false;
      return;
    }
    // 如果是合法的求职者用户，获取简历列表
    await getResumeList();
  }
});

onMounted(async () => {
  // 如果组件挂载时弹窗就是显示状态，需要检查登录状态
  if (visible.value) {
    if (!checkLoginStatusAndRedirect()) {
      visible.value = false;
      return;
    }
    await getResumeList();
  }
});

const getResumeList = async () => {
  const [err, res] = await feature(getPcAPI(UserApi).apiUserResumeListGet());
  if (!err && res.data.code == 1) {
    
    resumeList.value = res.data.data;
    selectResumeId.value =
      resumeList.value?.resumeList?.find((item) => item.isDefault)
        ?.resumeGuid || "";
    console.log(33,selectResumeId.value);
  }
};
const beforeClose = () => {
  visible.value = false
  errorMsg.value = ''
};
watch(()=>selectResumeId.value,()=>{
  errorMsg.value = ''
})
const deliverResume = async () => {
  if (selectResumeId.value && positionGuid.value) {
    loading.value = true
    const [err, res] = await feature(
      getPcAPI(UserApi).apiUserDeliverPost(
        { platform: 0 },
        positionGuid.value,
        selectResumeId.value,
        false,
        false,
        districtId.value,
        ApplicationPlatform.NUMBER_0
      )
    );
    loading.value = false
    
    if(res?.data.succeeded){
        const path = cityStore.getCityPagePath('/jobDetail/JobDeliveryResult');
        router.push(path)
        emit("success", positionGuid.value)
    }else if(err){
      
      errorMsg.value = err.message
    }
  }else{
    errorMsg.value = '请选择简历'
  }
};
</script>

<template>
  <el-dialog
    title="职位申请"
    :append-to-body="true"
    v-model="visible"
    :before-close="beforeClose"
    width="800px"
    top="25vh"
  >
    <div class="resume-selection">
      <div class="tit clearfix">
        <span>您在申请“</span><span class="pos-name">{{ positionName }}</span
        ><span>”职位，请选择投递简历：</span>
      </div>
      <template
        v-if="resumeList?.resumeList && resumeList?.resumeList?.length > 0"
      >
        <ul>
          <li v-for="item in resumeList?.resumeList" :key="item.resumeID">
            <div class="resume clearfix">
              <label class="resume-name"
                ><input
                  type="radio"
                  :disabled="
                    item.resumeState == 0 || item.resumeState == 3
                      ? true
                      : false
                  "
                  v-model="selectResumeId"
                  :value="item.resumeGuid"
                  name="ResumeID"
                />{{ item.resumeName }}</label
              ><span class="complete">完善度 {{ item.totalScoreNew }}%</span
              ><a
                :href="`${host}/resume/${item.resumeID}`"
                class="edit"
                target="_blank"
                >编辑</a
              ><a
                :href="`${host}/preview/${item.resumeID}`"
                class="preview"
                target="_blank"
                >预览</a
              >
            </div>
            <div class="resume-state"></div>
            <p v-if="item.resumeState == 0 || item.resumeState == 3">
              该份简历{{
                item.resumeState == 3 ? "审核未通过" : "未完成"
              }}，暂不可投递，请点击编辑修改简历。
            </p>
          </li>
        </ul>
        <div class="btn-wrap">
          <div v-if="errorMsg" style="color:red;padding-bottom:20px;" v-html="errorMsg"></div>
          <!-- <button class="btn btn-blue" @click="deliverResume">立即申请</button> -->
           <el-button class="btn btn-blue" :loading="loading" @click="deliverResume">{{ loading ? '提交中' : '立即申请'}}</el-button>
        </div>
      </template>
      <template v-else>
        <div class="no-resume">
          您还没有创建简历，不能找工作，<a
            :href="host"
            target="_blank"
            >立即创建一份简历</a
          >
        </div>
      </template>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 0 20px 30px 20px;
}
:deep(.el-dialog) {
  border-radius: 20px;
}
.resume-selection {
  padding: 20px;
  .tit {
    color: #666;
    padding-bottom: 5px;
    span {
      float: left;
      display: block;
      line-height: 28px;
      &.pos-name {
        max-width: 220px;
        _width: 220px;
        line-height: 26px;
        color: #195bd4;
        font-size: 18px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
  ul {
    li {
      padding: 15px 0;
      border-bottom: 1px solid #f2f2f2;
      .resume label,
      .resume span,
      .resume a {
        display: block;
        float: left;
        height: 40px;
        line-height: 40px;
      }
      .resume {
        .resume-name {
          width: 260px;
          padding-right: 10px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-size: 16px;
          color: #000;
          input {
            margin-right: 10px;
          }
        }
        .complete {
          width: 100px;
          color: #3c9;
        }
        a {
          float: right;
          width: 40px;
          color: #195bd4;
          text-align: right;
        }
      }
      p {
        color: #f66;
        padding-left: 22px;
      }
    }
  }
  .btn-wrap {
    text-align: center;
    padding-top: 30px;
    .btn {
      cursor: pointer;
      width: 380px;
      // height: 48px;
      // line-height: 48px;
      background: #195bd4;
      color: #fff;
      border: none;
      border-radius: 10px;
      &:hover {
        background: #0b4bc1;
      }
    }
  }
}
</style>
