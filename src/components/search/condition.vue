<script setup lang="ts">
import {
  DegreeOption,
  FirstPublishDateItemOption,
  KeywordSimpleOption,
  PaypackageItemOption,
  SearchOptions,
  WorkAgeItemOption,
  WorkPropertyItemOption,
} from "@/api-services";

const props = defineProps<{
  searchOptions: SearchOptions;
  selectedConditions?: {
    salary?: string;
    workProperty?: string;
    entProp?: string;
    firmSize?: string;
    edu?: string;
    firstDay?: string;
    welfare?: string;
  }
}>();

const emit = defineEmits<{
  conditionChange: [condition: { name: string; value: string }];
  conditionClear: [];
}>();

type paypackagesDto = PaypackageItemOption & {
  selected?: boolean;
};

type workPropertyDto = WorkPropertyItemOption & {
  selected?: boolean;
};



type keywordDto = KeywordSimpleOption & {
  selected?: boolean;
};

type degreeDto = DegreeOption & {
  selected?: boolean;
};

type firstDayDto = FirstPublishDateItemOption & {
  selected?: boolean;
  ids?: string;
};



// 使用computed来确保响应式，避免toRefs在Vue 2.7中的兼容性问题
const searchOptions = computed(() => props.searchOptions);

const mouseActiveIndex = ref(0);
const welfareInitCheckList = ref<Array<number>>([]);
const welfareCheckboxList = ref<Array<number>>([]);

watch(
  () => welfareInitCheckList.value,
  () => {
    welfareCheckboxList.value = [...welfareInitCheckList.value];
  }
);

const salaryList = computed(() => {
  const list = searchOptions.value?.paypackages || [];
  if (list.length == 0) {
    return { count: 0, list: [] };
  }
  const arr = list.map((item: PaypackageItemOption) => {
    let selected = false;
    if (props.selectedConditions?.salary) {
      const id = props.selectedConditions.salary;
      selected = item.id === id;
    }
    return {
      ...item,
      selected,
    };
  });
  const count = arr.filter((item) => item.selected).length;
  return {
    count: count,
    list: arr,
  };
});

const workPropertyList = computed(() => {
  const list = searchOptions.value?.workProperties || [];
  if (list.length == 0) {
    return { count: 0, list: [] };
  }
  const arr = list.map((item: WorkPropertyItemOption) => {
    let selected = false;
    if (props.selectedConditions?.workProperty) {
      const id = Number(props.selectedConditions.workProperty);
      selected = item.id == id;
    }
    return {
      ...item,
      selected,
    };
  });
  const count = arr.filter((item) => item.selected).length;
  return {
    count: count,
    list: arr,
  };
});



const entPropertyList = computed(() => {
  const list = searchOptions.value?.enterpriseProperties || [];
  if (list.length == 0) {
    return { count: 0, list: [] };
  }
  const arr = list.map((item: KeywordSimpleOption) => {
    let selected = false;
    if (props.selectedConditions?.entProp) {
      const id = Number(props.selectedConditions.entProp);
      selected = item.keywordID == id;
    }
    return {
      ...item,
      selected,
    };
  });
  const count = arr.filter((item) => item.selected).length;
  return {
    count: count,
    list: arr,
  };
});


const degreeList = computed(() => {
  const list = searchOptions.value?.degrees || [];
  if (list.length == 0) {
    return { count: 0, list: [] };
  }
  const arr = list.map((item: DegreeOption) => {
    let selected = false;
    if (props.selectedConditions?.edu) {
      const id = Number(props.selectedConditions.edu);
      selected = item.id === id;
    }
    return {
      ...item,
      selected,
    };
  });
  const count = arr.filter((item) => item.selected).length;
  return {
    count: count,
    list: arr,
  };
});

const firstDayList = computed(() => {
  const list = searchOptions.value?.firstDateOption || [];
  if (list.length == 0) {
    return { count: 0, list: [] };
  }
  const arr = list.map((item: FirstPublishDateItemOption) => {
    let selected = false;
    let ids = ''
    switch(item.dateValue){
      case "":
        ids=""
        break
      case "1Day":
        ids = "1"
        break
      case "3Day":
        ids = "3"
        break
      case "7Day":
        ids = "7"
        break
      case "14Day":
        ids = "14"
        break
      case "30Day":
        ids = "30"
        break
    }
    if (props.selectedConditions?.firstDay) {
      const id = Number(props.selectedConditions.firstDay);
      selected = Number(ids) === id;
    }
    return {
      ...item,
      ids,
      selected,
    };
  });
  const count = arr.filter((item) => item.selected).length;
  return {
    count: count,
    list: arr,
  };
});

// 监听选中的福利条件变化
watch(
  () => props.selectedConditions?.welfare,
  (newWelfare) => {
    if (newWelfare) {
      welfareInitCheckList.value = newWelfare.split(",").map(Number);
    } else {
      welfareInitCheckList.value = [];
    }
  },
  { immediate: true }
);

const welfareList = computed(() => {
  const list = searchOptions.value?.welfares || [];
  if (list.length == 0) {
    return { count: 0, list: [] };
  }

  const arr = list.map((item: KeywordSimpleOption) => {
    const selected = welfareInitCheckList.value.includes(item.keywordID || 0);
    return {
      ...item,
      selected,
    };
  });
  const count = arr.filter((item) => item.selected).length;
  return {
    count: count,
    list: arr,
  };
});

const welfareNameList = computed(() =>
  welfareList.value.list.filter((item) => item.selected)
);

const salaryItemClick = (item: paypackagesDto) => {
  emit('conditionChange', { name: "salary", value: item.id as string });
  mouseActiveIndex.value = 0;
};

const workPropertyItemClick = (item: workPropertyDto) => {
  emit('conditionChange', { name: "workProperty", value: (item.id || 0).toString() });
  mouseActiveIndex.value = 0;
};



const entProppertyItemClick = (item: keywordDto) => {
  emit('conditionChange', { name: "entProp", value: (item.keywordID || 0).toString() });
  mouseActiveIndex.value = 0;
};


const degreeItemClick = (item: degreeDto) => {
  emit('conditionChange', { name: "edu", value: (item.id || 0).toString() });
  mouseActiveIndex.value = 0;
};

const firstDayItemClick = (item: firstDayDto) => {
  emit('conditionChange', { name: "firstDay", value: item.ids || '' });
  mouseActiveIndex.value = 0;
};



const welfareItemClick = () => {
  const welfareStr = welfareCheckboxList.value.join(",");
  emit('conditionChange', { name: "welfare", value: welfareStr });
};

const welfareNameClick = (item: keywordDto) => {
  const index = welfareCheckboxList.value.findIndex(
    (id) => id === item.keywordID
  );
  if (index > -1) {
    welfareCheckboxList.value.splice(index, 1);
    welfareItemClick();
  } 
};

const clearAll = () => {
  welfareCheckboxList.value = [];
  emit('conditionClear');
};

</script>

<template>
  <div class="condition clearfix">
    <div
      class="condition-filter-select"
      :class="{
        open: mouseActiveIndex === 1,
        'is-selected': salaryList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0"
    >
      <div class="current-select" @mouseenter="mouseActiveIndex = 1">
        <span>薪酬范围</span>
        <em class="select-num" v-if="salaryList.count > 0"
          >({{ salaryList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 1 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-dropdown" v-show="mouseActiveIndex === 1">
          <ul>
            <li
              v-for="(item, index) in salaryList.list"
              :key="index"
              :class="{ active: item.selected }"
              @click="salaryItemClick(item)"
            >
              {{ item.name }}
              <i class="el-icon-check is-checked" v-if="item.selected"></i>
            </li>
          </ul>
        </div>
      </transition>
    </div>
    <div
      class="condition-filter-select"
      :class="{
        open: mouseActiveIndex === 2,
        'is-selected': workPropertyList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0"
    >
      <div class="current-select" @mouseenter="mouseActiveIndex = 2">
        <span>工作性质</span>
        <em class="select-num" v-if="workPropertyList.count > 0"
          >({{ workPropertyList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 2 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-dropdown" v-show="mouseActiveIndex === 2">
          <ul>
            <li
              v-for="(item, index) in workPropertyList.list"
              :key="index"
              :class="{ active: item.selected }"
              @click="workPropertyItemClick(item)"
            >
              {{ item.name }}
              <i class="el-icon-check is-checked" v-if="item.selected"></i>
            </li>
          </ul>
        </div>
      </transition>
    </div>
    <div
      class="condition-filter-select"
      :class="{
        open: mouseActiveIndex === 4,
        'is-selected': entPropertyList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0"
    >
      <div class="current-select" @mouseenter="mouseActiveIndex = 4">
        <span>单位类型</span>
        <em class="select-num" v-if="entPropertyList.count > 0"
          >({{ entPropertyList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 4 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-dropdown" v-show="mouseActiveIndex === 4">
          <ul>
            <li
              v-for="(item, index) in entPropertyList.list"
              :key="index"
              :class="{ active: item.selected }"
              @click="entProppertyItemClick(item)"
            >
              {{ item.keywordName }}
              <i class="el-icon-check is-checked" v-if="item.selected"></i>
            </li>
          </ul>
        </div>
      </transition>
    </div>
    <!-- <div
      class="condition-filter-select"
      :class="{
        open: mouseActiveIndex === 5,
        'is-selected': enterpriseEmployeeNumberList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0"
    >
      <div class="current-select" @mouseenter="mouseActiveIndex = 5">
        <span>公司规模</span>
        <em class="select-num" v-if="enterpriseEmployeeNumberList.count > 0"
          >({{ enterpriseEmployeeNumberList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 5 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-dropdown" v-show="mouseActiveIndex === 5">
          <ul>
            <li
              v-for="(item, index) in enterpriseEmployeeNumberList.list"
              :key="index"
              :class="{ active: item.selected }"
              @click="enterpriseEmployeeNumberItemClick(item)"
            >
              {{ item.keywordName }}
              <i class="el-icon-check is-checked" v-if="item.selected"></i>
            </li>
          </ul>
        </div>
      </transition>
    </div> -->
    <div
      class="condition-filter-select"
      :class="{
        open: mouseActiveIndex === 6,
        'is-selected': degreeList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0"
    >
      <div class="current-select" @mouseenter="mouseActiveIndex = 6">
        <span>学历要求</span>
        <em class="select-num" v-if="degreeList.count > 0"
          >({{ degreeList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 6 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-dropdown" v-show="mouseActiveIndex === 6">
          <ul>
            <li
              v-for="(item, index) in degreeList.list"
              :key="index"
              :class="{ active: item.selected }"
              @click="degreeItemClick(item)"
            >
              {{ item.name }}
              <i class="el-icon-check is-checked" v-if="item.selected"></i>
            </li>
          </ul>
        </div>
      </transition>
    </div>
    <div class="condition-filter-select" :class="{
        open: mouseActiveIndex === 7,
        'is-selected': firstDayList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0">
      <div class="current-select" @mouseenter="mouseActiveIndex = 7">
        <span>首发日</span>
        <em class="select-num" v-if="firstDayList.count > 0"
          >({{ firstDayList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 7 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-dropdown" v-show="mouseActiveIndex === 7">
          <ul>
            <li
              v-for="(item, index) in firstDayList.list"
              :key="index"
              :class="{ active: item.selected }"
              @click="firstDayItemClick(item)"
            >
              {{ item.name }}
              <i class="el-icon-check is-checked" v-if="item.selected"></i>
            </li>
          </ul>
        </div>
      </transition>
    </div>
    <div
      class="condition-filter-select"
      :class="{
        open: mouseActiveIndex === 8,
        'is-selected': welfareList.count > 0,
      }"
      @mouseleave="mouseActiveIndex = 0"
    >
      <div class="current-select" @mouseenter="mouseActiveIndex = 8">
        <span>公司福利</span>
        <em class="select-num" v-if="welfareList.count > 0"
          >({{ welfareList.count }})</em
        >
        <i :class="`${mouseActiveIndex == 8 ? 'i-ep-caret-top' : 'i-ep-caret-bottom'} icons`"></i>
      </div>
      <transition name="el-fade-in-linear">
        <div class="filter-select-checkbox" v-show="mouseActiveIndex === 8">
          <div class="select-welfare-list clearfix" v-if="welfareNameList.length > 0">
            <a
              href="javascript:;"
              v-for="item in welfareNameList"
              :key="item.keywordID"
              
            >
              {{ item.keywordName }}
              <i class="el-icon-close icons" @click="welfareNameClick(item)"></i>
            </a>
          </div>

          <el-checkbox-group
            v-model="welfareCheckboxList"
            class="checkbox-group"
            @change="welfareItemClick"
          >
            <el-checkbox
              v-for="(item, index) in welfareList.list"
              :key="index"
              :label="item.keywordID"
              >{{ item.keywordName }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
      </transition>
    </div>

    <div class="clean-search-btn" @click="clearAll">清空筛选条件</div>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #3b86f6;
.condition {
  &-filter-select {
    float: left;
    // display: inline-block;
    position: relative;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 20px;
    margin-right: 16px;

    .current-select {
      position: relative;
      display: inline-block;
      font-size: 14px;
      font-weight: 400;
      color: #333;
      line-height: 20px;
      padding: 6px 12px 6px 12px;
      cursor: pointer;
      transition: all 0.2s linear;
      .select-num {
        font-style: normal;
        display: inline-block;
        margin-left: 2px;
        vertical-align: bottom;
      }
      .icons {
        margin-left: 5px;
        font-size: 12px;
        margin-bottom: 3px;
        color: #CBCBCB;
      }
    }
    &.is-selected {
      .current-select {
        background: #fff;
        color: $active-color;
      }
    }
    .filter-select-dropdown {
      position: absolute;
      top: 40px;
      left: 0;
      width: 168px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #ededed;
      z-index: 4;
      ul {
        padding: 4px 8px;
        li {
          &.active {
            color: $active-color;
            font-weight: 500;
          }
          position: relative;
          display: block;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 20px;
          padding: 8px 24px 8px 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
          transition: all 0.2s linear;
          &:hover {
            background: $active-color;
            color: #fff;
            font-weight: 500;
          }

          .is-checked {
            position: absolute;
            top: 12px;
            right: 8px;
            font-weight: 600;
          }
        }
      }
    }
    .filter-select-checkbox {
      position: absolute;
      top: 40px;
      left: 50%;
      transform: translateX(-50%);
      width: 630px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #ededed;
      z-index: 4;
      .select-welfare-list {
        padding: 14px 24px 0;
        a {
          float: left;
          white-space: nowrap;
          background: #f5f6fb;
          border-radius: 2px;
          font-size: 14px;
          color: $active-color;
          padding: 3px 10px;
          margin-top: 8px;
          margin-right: 8px;
          cursor: default;
          .icons {
              border-radius: 100%;
              cursor: pointer;
              padding: 2px;
              transition: all 0.2s linear;
              &:hover {
                background: $active-color;
                color: #fff;
              }
            }
        }
      }
      .checkbox-group {
        
        padding: 16px 24px 22px;
        :deep(.el-checkbox,.el-checkbox-button__inner) {
          color: #333;
          font-weight: 400;
        }
        :deep(.el-checkbox__label) {
          line-height: 30px;
        }
        .el-checkbox:hover {
          color: $active-color;
        }
      }
    }
    &.open {
      overflow: visible;
      background: #fff;
      .current-select {
        color: $active-color;
      }
    }
    &::after {
      content: " ";
      position: absolute;
      left: 0;
      bottom: -8px;
      display: block;
      width: 100%;
      height: 8px;
      z-index: 4;
      background: transparent;
    }
  }
  .clean-search-btn {
    float: right;
    margin-top: 26px;
    font-size: 14px;
    font-weight: 400;
    color: #999;
    line-height: 20px;
    transition: all 0.2s linear;
    cursor: pointer;
  }
}
</style>
