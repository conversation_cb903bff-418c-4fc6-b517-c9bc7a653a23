<script setup lang="ts">
import {
  IndexPositionListItem
} from "@/api-services";
import { usePositionFavorite } from '@/composables'

const { toggleFavorite } = usePositionFavorite()

const props = defineProps<{
  positionList: Array<IndexPositionListItem> | undefined;
}>();
const emit = defineEmits<{
  (e: "deliver", notHitPName: string, positionGuid: string): void;
}>();

const activePositionItem = ref("");
const activePositionDetail = ref("");

const leavetimer = ref<ReturnType<typeof setTimeout> | undefined>();
const entertimer = ref<ReturnType<typeof setTimeout> | undefined>();
const sucessDeliverPosGuidList = ref<Array<string>>([]);
const collectionPosGuidList = ref<Array<string>>([]);
const show = ref(true);
const positionList = computed(() => props.positionList);

watch(
  () => positionList.value,
  (newval: Array<IndexPositionListItem> | undefined) => {
    if (newval) {
      show.value = (newval?.length || 0) > 0;
    }
  },
  { immediate: true }
);

const titleMouseEnter = (str: string, item: IndexPositionListItem) => {
  clearLeavetimer();
  if (item.recordType == 1) return;
  entertimer.value = setTimeout(() => {
    activePositionDetail.value = str;
    clearEntertimer();
  }, 400);
};
const titleMouseLeave = () => {
  if (entertimer.value) {
    clearEntertimer();
  } else {
    leavetimer.value = setTimeout(() => {
      activePositionDetail.value = "";
      clearLeavetimer();
    }, 500);
  }
};
const clearLeavetimer = () => {
  clearTimeout(leavetimer.value);
  leavetimer.value = undefined;
};
const clearEntertimer = () => {
  clearTimeout(entertimer.value);
  entertimer.value = undefined;
};
const goDetail = (_positionGuid: string, _trackingGuid: string) => {};
const openChat = (item: IndexPositionListItem) => {
  console.log(item);
};
const collection = async (positionGuid: string, isCollection: boolean) => {
  const res = await toggleFavorite(positionGuid, isCollection)
  if(!res.success) {
    return
  }
  if(isCollection) {
    collectionPosGuidList.value = collectionPosGuidList.value.filter(item => item !== positionGuid)
  } else {
    collectionPosGuidList.value.push(positionGuid)
  }
};

const startChatEvent = (zhiliao: boolean, enterpriseID: number) => {
  console.log(zhiliao, enterpriseID);
};
const deliver = (notHitPName: string, positionGuid: string) => {
  emit("deliver", notHitPName, positionGuid);
};

</script>

<template>
  <div class="position-list">
    <div class="search-position-list">
      <div
        class="position-item"
        :class="{
          'hover-footer': activePositionItem === (item?.positionGuid || '') + index,
        }"
        v-for="(item, index) in positionList"
:key="(item?.positionGuid || '') + index"
        @mouseleave="activePositionDetail = ''"
        @click="goDetail(item.positionGuid || '', item?.trackingGuid || '')"
      >
        <div class="position-item-body clearfix">
          <a
            :href="`${
              item.jumpLinkUrl
                ? item.jumpLinkUrl
                : `/jobDetail/${item.positionGuid}?trackingGuid=${item?.trackingGuid}`
            }`"
            class="position-item-left"
            target="_blank"
            @click.stop
          >
            <div
              class="position-title"
              @mouseenter="titleMouseEnter((item?.positionGuid || '') + index, item)"
              @mouseleave="titleMouseLeave"
            >
              <span class="position-name">
                <span v-html="item.positionName"></span>
              </span>
              <span class="position-area" v-if="item.workPlace"
                >[{{ item.workPlace }}]</span
              >
              <span class="graduate" v-if="item.isReceiveGraduate">毕</span>
              <span class="recruit" v-if="item?.isAgentRecruit">代招</span>
            </div>
            <div class="position-info">
              <span class="salary">{{ item.payPackage }}</span>
              <ul class="tag-list" v-if="item.recordType != 1">
                <li v-if="item.workAge">{{ item.workAge }}</li>
                <li>{{ item.degreeName || "学历不限" }}</li>
                <li class="emergency" v-if="item.emergencyRrecruitmentFlag">
                  急
                </li>
              </ul>
              <span class="online-tag" v-if="item.online">在线</span>
              <span
                class="online-chat"
                v-if="item.recordType != 1"
                @click.prevent="openChat(item)"
                ><i class="el-icon-chat-line-round icon-chat"></i>在线直聊</span
              >
              <span
                class="online-chat"
                style="margin-left: -10px; padding-left: 20px; border-radius: 0"
                v-else
              >
                公告招聘</span
              >
            </div>
            <transition name="el-fade-in">
              <div
                class="position-card-wrapper"
                @mouseenter="clearLeavetimer"
                @mouseleave="activePositionDetail = ''"
                v-if="activePositionDetail == (item?.positionGuid || '') + index"
              >
                <div class="position-detail-header">
                  <div class="header-info">
                    <h3 class="title">
                      <span v-html="item.positionName"></span>
                    </h3>
                    <div class="intro" v-html="item.enterpriseName"></div>
                    <a
                      href="javascript:;"
                      class="like-btn"
                      v-if="
                        item.isCollection ||
                        collectionPosGuidList.includes(item?.positionGuid || '')
                      "
                      @click.stop="collection(item?.positionGuid || '', true)"
                    >
                      <i class="iconfont icon-zwsc"></i>
                      取消收藏职位
                    </a>
                    <a
                      href="javascript:;"
                      class="like-btn"
                      v-else
                      @click.stop="collection(item?.positionGuid || '', false)"
                    >
                      <i class="iconfont icon-icon_collect"></i>
                      收藏职位
                    </a>
                  </div>
                  <div class="header-right">
                    <a
                      href="javascript:;"
                      v-if="
                        item.isDeliver ||
                        sucessDeliverPosGuidList.includes(
                          item?.positionGuid || ''
                        )
                      "
                      class="btn"
                      @click.stop="
                        startChatEvent(
                          item.zhiliao || false,
                          item.enterpriseID || 0
                        )
                      "
                      >继续聊</a
                    >
                    <a
                      href="javascript:;"
                      v-else
                      class="deliver-btn"
                      @click.stop="
                        deliver(item.notHitPName || '', item?.positionGuid || '')
                      "
                      >立即申请</a
                    >
                  </div>
                </div>
                <div class="position-detail-body">
                  <!-- <h3 class="title">职位描述：</h3> -->
                  <p class="desc" v-html="item.description"></p>
                </div>
              </div>
            </transition>
          </a>
          <div class="position-item-right">
            <div class="company-logo">
              <a
                :href="`/company/${item.enterpriseGuid}`"
                target="_blank"
                @click.stop
              >
                <img
                  :src="item?.logoUrl || ''"
                  :alt="item?.enterpriseName || ''"
                />
              </a>
            </div>
            <div class="company-info">
              <h3 class="company-name">
                <a
                  :href="`/company/${item.enterpriseGuid}`"
                  v-html="item?.enterpriseName"
                  target="_blank"
                  @click.stop
                ></a>
              </h3>
              <ul class="company-tag-list">
                <li>{{ item?.enterprisePropertyName }}</li>
                <li>{{ item?.enterpriseEmployeeNumberName }}</li>
                <li>{{ item?.enterpriseIndustryName }}</li>
              </ul>
            </div>
          </div>
          <div class="publish">{{ item.publishTimeDescribe }}</div>
        </div>
        <div
          class="position-item-footer clearfix"
          v-if="item.recordType != 1"
          @mouseenter="activePositionItem = (item?.positionGuid || '') + index"
          @mouseleave="activePositionItem = ''"
        >
          <ul class="tag-list">
            <li>{{ item?.workProperty }}</li>
            <li v-for="(it, index) in item.positionKeywords" :key="index">
              {{ it.name }}
            </li>
          </ul>
          <div
            class="info-desc"
            v-if="
              item?.positionWelfareNames &&
              item?.positionWelfareNames.length > 0
            "
          >
            {{ item?.positionWelfareNames.join(" , ") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
$active-color: #3b86f6;
.position-list {
  width: 1160px;
  min-height: 640px;
  float: left;
  margin-right: 14px;

  .search-position-list {
    width: 1160px;
    .position-item {
      position: relative;
      border-radius: 8px;
      background: #fff;
      cursor: pointer;
      letter-spacing: 0;
      margin-bottom: 16px;
      transition: all 0.2s linear;
      &-left {
        float: left;
        width: 584px;
        padding: 16px 0 16px 24px;
        height: 88px;
        .position-title {
          display: inline-block;
          vertical-align: middle;
          font-size: 18px;
          font-weight: 400;
          color: #222;
          line-height: 24px;
          transition: all 0.2s linear;
          max-width: 460px;
          .position-name {
            float: left;
            max-width: 228px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .position-area {
            float: left;
            margin-left: 12px;
            line-height: 24px;
          }
          .graduate {
            background: #65cc88;
            color: #fff;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
            margin-left: 8px;
            position: relative;
            top: -2px;
          }
          .recruit {
            background: #55d8dc;
            color: #fff;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
            margin-left: 8px;
            position: relative;
            top: -2px;
          }
        }
        .position-info {
          margin-top: 10px;
          height: 22px;
          overflow: hidden;
          .salary {
            font-size: 16px;
            font-weight: 500;
            color: #ff6666;
            line-height: 22px;
            float: left;
          }
          .tag-list {
            float: left;
            margin-left: 12px;
            max-width: 172px;
            height: 22px;
            overflow: hidden;
            li {
              padding: 2px 8px;
              float: left;
              background: #f5f6fb;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 400;
              color: #8d97ac;
              line-height: 18px;
            }
            li + li {
              margin-left: 8px;
            }
            li.emergency {
              background: #ff4e00;
              color: #fff;
            }
          }
          .online-tag {
            display: inline-block;
            position: relative;
            height: 22px;
            background: #effcff;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: $active-color;
            line-height: 22px;
            padding: 0 10px 0 20px;
            margin-left: 8px;
            &::before {
              content: " ";
              position: absolute;
              width: 7px;
              height: 7px;
              top: 8px;
              left: 8px;
              border-radius: 100%;
              background: $active-color;
              z-index: 1;
            }
          }
          .online-chat {
            display: inline-block;
            height: 22px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            background: #fcf5e2;
            color: #f5b318;
            line-height: 22px;
            padding: 0 10px;
            margin-left: 8px;
            .icon-chat {
              padding-right: 3px;
            }
          }
        }
        .position-card-wrapper {
          position: absolute;
          top: 0;
          right: 0;
          width: 700px;
          background: linear-gradient(268deg, #ffffff 0%, #f8feff 100%);
          box-shadow: 0px 6px 12px 1px rgba(0, 0, 0, 0.16);
          border-radius: 4px;
          z-index: 88;
          .position-detail-header {
            position: relative;
            height: 110px;
            padding: 20px 30px;
            border-bottom: 1px solid #e5e4e4;
            .title {
              max-width: 280px;
              font-size: 18px;
              font-weight: 400;
              color: #333;
              line-height: 22px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .intro {
              font-size: 14px;
              font-weight: 400;
              color: #666;
              line-height: 18px;
              margin-top: 11px;
              max-width: 310px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .like-btn {
              position: relative;
              display: inline-block;
              margin-top: 7px;
              font-size: 14px;
              font-weight: 400;
              color: $active-color;
            }
            .header-right {
              position: absolute;
              right: 24px;
              top: 20px;
              z-index: 1;
              .deliver-btn {
                display: block;
                width: 140px;
                height: 46px;
                background: $active-color;
                color: #fff;
                // padding: 5px 0;
                font-size: 16px;
                line-height: 46px;
                text-align: center;
                border-radius: 4px;
              }
            }
          }
          .position-detail-body {
            padding: 12px 30px 16px;
            .title {
              font-size: 14px;
              font-weight: 400;
              color: #222;
              line-height: 20px;
            }
            .desc {
              margin-top: 2px;
              color: #666;
              font-size: 14px;
              line-height: 25px;
              max-height: 270px;
              white-space: pre-wrap;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 10;
              line-clamp: 10;
              overflow: hidden;
            }
          }
        }
        &:hover {
          .position-title {
            color: $active-color;
          }
          .online-chat {
            background: #f5b318;
            color: #fff;
          }
        }
      }
      &-right {
        float: left;
        // width: 400px;
        padding: 16px 24px 16px 10px;
        .company-logo {
          float: left;
          width: 56px;
          height: 56px;
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid #f8f8f8;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .company-info {
          margin-left: 12px;
          float: left;
          .company-name {
            font-size: 16px;
            font-weight: 500;
            line-height: 22px;
            height: 22px;
            a {
              display: inline-block;
              color: #333;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 290px;
              vertical-align: middle;
              transition: all 0.2s linear;
            }
            &:hover {
              a {
                color: $active-color;
              }
            }
          }
          .company-tag-list {
            margin-top: 12px;
            height: 22px;
            overflow: hidden;
            max-width: 298px;
            li {
              display: inline-block;
              // background: #f8f8f8;
              padding: 1px 8px;
              // border-radius: 4px;
              font-size: 13px;
              font-weight: 400;
              color: #8d97ac;
              line-height: 18px;
              &:first-of-type {
                padding-left: 0;
              }
            }
          }
        }
      }
      &-footer {
        padding: 15px 24px;
        background: #f6f9ff;
        border-radius: 0 0 12px 12px;
        cursor: pointer;
        .tag-list {
          float: left;
          height: 18px;
          margin-left: -8px;
          width: 490px;
          overflow: hidden;
          margin-right: 84px;
          li {
            display: inline-block;
            position: relative;
            padding: 0 8px;
            font-size: 14px;
            font-weight: 400;
            color: #666;
            line-height: 18px;
            white-space: nowrap;
          }
          li + li:before {
            content: " ";
            position: absolute;
            top: 4px;
            left: 0;
            width: 1px;
            height: 10px;
            background: #e0e0e0;
            z-index: 1;
          }
        }
        .info-desc {
          float: left;
          width: 362px;
          font-size: 14px;
          font-weight: 400;
          color: #666;
          line-height: 18px;
          word-break: break-word;
          -ms-word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 7px;
        }
      }
      &:hover {
        background: #fcfeff;
        box-shadow: 6px 6px 20px 1px rgba(0, 0, 0, 0.16);
        z-index: 3;
      }
      &.hover-footer:hover {
        .position-title {
          color: $active-color;
        }
        .online-chat {
          background: #f5b318;
          color: #fff;
        }
      }

      .publish {
        position: absolute;
        right: 20px;
        top: 20px;
      }
    }
  }
  a:hover {
    color: $active-color;
  }
  .pageination {
    margin: 20px 0;
    text-align: center;
  }
  .login-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
  }
  .empty-position {
    text-align: center;
    min-height: 540px;
    position: relative;
    background: #fff;
    // padding: 200px 0;
    border-radius: 12px;
    .empty-box {
      width: 360px;
      margin: 0 auto;
      font-size: 16px;
      color: #a5abb5;
    }
  }
}
</style>
