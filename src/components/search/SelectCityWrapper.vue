<script setup lang="ts">
import {  KeywordItemDto } from '@/api-services';

import { useDebounceFn } from '@vueuse/core';


const props = defineProps<{
  modelValue: boolean;
  city: Array<KeywordItemDto>;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "close"): void;
  (e: "comfirm", value: KeywordItemDto): void;
}>();

const cityList = computed(() => props.city);

const activeIndex = ref(0);

onMounted(async () => {
    
});


const gxCityList = computed(() => {
  const list =  cityList.value.filter((item) => item.parentID == 1 || item.keywordID == 1);
  return list.map(item=> {
    if(item.keywordID == 1) {
      item.keywordName = '全部'
    }
    return item
  })
});

const qwCityList = computed(() => {
  return cityList.value.filter(
    (item) =>
      item.parentID == -1 &&
      item.keywordID != 1 &&
      item.keywordID != 47 &&
      item.keywordID != 227
  );
});

const qtCityList = computed(() => {
  return cityList.value.filter(
    (item) => item.keywordID == 47 || item.keywordID == 227
  );
});

const beforeClose = () => {
  emit("close");
  emit("update:modelValue", false);
};

// 使用防抖函数防止快速连续点击导致重复调用
const selectCity = useDebounceFn((item: KeywordItemDto) => {
  //contrlWorkPlace(item.keywordID?.toString() as string);
  emit("comfirm", item);
  emit("update:modelValue", false);
}, 300); // 300ms 防抖延迟
</script>

<template>
  <div class="city-wrapper">
    <el-dialog
      title="选择城市"
      :model-value="modelValue"
      @update:model-value="(val: boolean) => emit('update:modelValue', val)"
      :before-close="beforeClose"
      width="800px"
    >
      <div class="city-header"></div>
      <div class="city-content clearfix">
        <div class="city-body-menu">
          <div
            class="menu-item"
            :class="{ active: activeIndex == 0 }"
            @click="activeIndex = 0"
          >
            区内
          </div>
          <div
            class="menu-item"
            :class="{ active: activeIndex == 1 }"
            @click="activeIndex = 1"
          >
            区外
          </div>
          <div
            class="menu-item"
            :class="{ active: activeIndex == 2 }"
            @click="activeIndex = 2"
          >
            其他
          </div>
        </div>
        <div class="city-body-right">
          <div class="city-list" v-show="activeIndex == 0">
            <div
              class="city-list-item"
              v-for="item in gxCityList"
              :key="item.keywordID"
              @click="selectCity(item)"
            >
              <a href="javascript:;">{{ item.keywordName }}</a>
            </div>
          </div>
          <div class="city-list" v-show="activeIndex == 1">
            <div
              class="city-list-item"
              v-for="item in qwCityList"
              :key="item.keywordID"
              @click="selectCity(item)"
            >
              <a href="javascript:;">{{ item.keywordName }}</a>
            </div>
          </div>
          <div class="city-list" v-show="activeIndex == 2">
            <div
              class="city-list-item"
              v-for="item in qtCityList"
              :key="item.keywordID"
              @click="selectCity(item)"
            >
              <a href="javascript:;">{{ item.keywordName }}</a>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #3b86f6;
.city-wrapper {
  :deep(.el-dialog) {
    border-radius: 12px;
    min-height: 480px;
  }
  .city-content {
    .city-body-menu {
      float: left;
      width: 170px;
      height: 400px;
      background: #ecedf4;
      .menu-item {
        height: 64px;
        line-height: 64px;
        padding-left: 24px;
        cursor: pointer;
        &.active {
          background: #f4f5f9;
          color: $active-color;
        }
      }
    }
    .city-body-right {
      float: left;
      width: 500px;
      background: #fff;
      .city-list {
        &-item {
          float: left;
          line-height: 40px;
          width: 100px;
          text-align: center;
          font-size: 14px;
          margin-bottom: 10px;
          padding: 0 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          transition: color 0.2s linear;
          box-sizing: border-box;
          a{
            color: #333;
          }
          &:hover {
            a {
              color: $active-color;
            }
          }
        }
      }
    }
  }
}
</style>
