<script setup lang="ts">
import { useCityStore } from "@/stores/cityStore";
import SearchInput from "./searchInput.vue";
import SelectCityWrapper from "./SelectCityWrapper.vue";
import CityArea from "./CityArea.vue";
import Condition from "./condition.vue";
import { feature, getPcAPI } from "@/utils/axios-utils";
import {
  ApplicationPlatform,
  BusinessDistrictOutput,
  DataApi,
  KeywordItemDto,
  PositionApi,
  SearchOptions,
} from "@/api-services";

const props = withDefaults(
  defineProps<{
    isOddJob?: boolean;
  }>(),
  {
    isOddJob: false,
  }
);

const emit = defineEmits<{
  (e: "search", value: {
    keyword: string;
    positionType: Array<number>;
    industryType: Array<number>;
    schType: number;
    areaBusiness:Array<string>;
    salary:number;
    workCycle:number;
    workHour:number;
    paymentType:number;
    emergency:boolean;
    online:boolean;
    sort:number;
    welfare:Array<number>;
    workProperty:Array<number>;
    entProp:Array<number>;
    firmSize:Array<number>;
    edu:Array<number>;
    firstDay:string;
  }): void;
}>();

const cityStore = useCityStore();
const districtId = computed(() => cityStore.cityInfo.id);
const cityId = ref(cityStore.cityInfo.cityId);
const cityName = ref("");
const tabActiveIndex = ref(1);
const schType = ref(1);
const cityList = ref<Array<KeywordItemDto>>([]);

const cityAreaList = ref<Array<BusinessDistrictOutput>>([]);
const cityAreaSelect = ref<string>("");

const showCityWrapper = ref(false);
const searchOptionList = ref<SearchOptions>();

const sort = ref([
  {
    name: "智能匹配",
    value: 0,
  },

  {
    name: "更新日",
    value: 2,
  },
  {
    name: "薪酬",
    value: 1,
  },
]);
const listSortIndex = ref(0);
const checkbox = ref<Array<string>>([]);

// 筛选条件状态管理
const selectedConditions = ref({
  salary: "",
  workProperty: "",
  entProp: "",
  firmSize: "",
  edu: "",
  firstDay: "",
  welfare: "",
});

const paymentType = ref([
  {
    id: 0,
    name: "不限",
  },
  {
    id: 1,
    name: "日结",
  },

  {
    id: 2,
    name: "月结",
  },

  {
    id: 3,
    name: "完工结",
  },
]);
const workCycle = ref([
  {
    id: 0,
    name: "不限",
  },

  {
    id: 1,
    name: "长期可做",
  },
  {
    id: 2,
    name: "短期兼职",
  },
]);
const workHour = ref([
  {
    id: 0,
    name: "不限",
  },
  {
    id: 1,
    name: "上午",
  },
  {
    id: 2,
    name: "中午",
  },
  {
    id: 3,
    name: "下午",
  },
]);
const areaCity = computed(() =>
  cityList.value.filter((item) => item.parentID == cityId.value)
);

watch(
  () => cityId.value,
  async () => {
    await cityAreaInit();
  }
);

const cityInit = async () => {
  const [err, res] = await feature(
    getPcAPI(DataApi).apiDataDistrictGet(0, true, districtId.value, ApplicationPlatform.NUMBER_0)
  );
  if (!err && res.data.code == 1) {
    cityList.value = res.data?.data || [];
    cityList.value = cityList.value.map((item) => {
      if (item.keywordName == "广西壮族自治区") {
        item.keywordName = "广西";
      }
      return item;
    });
  }
};
const cityAreaInit = async () => {
  const [err, res] = await feature(
    getPcAPI(DataApi).apiDataBusinessDistrictsByCityCityDictIdGet(cityId.value)
  );
  if (!err && res.data.code == 1) {
    cityAreaList.value = res.data?.data || [];
  }
};
const searchOptionInit = async () => {
  const [err, res] = await feature(
    getPcAPI(PositionApi).apiPositionSearchOptionGet(
      districtId.value,
      ApplicationPlatform.NUMBER_0
    )
  );
  if (!err && res.data.code == 1) {
    searchOptionList.value = res.data?.data;
  }
};
const changeTabIndex = (index: number) => {
  if (index == 0) {
    showCityWrapper.value = true;
  }
  //tabActiveIndex.value = index;
};
const handleComfirmCity = (value: KeywordItemDto) => {
  cityId.value = value.keywordID as number;
  if (value.keywordName == "全部") {
    cityName.value = "广西";
  } else {
    cityName.value = value.keywordName?.replace("市", "") || "";
  }
  showCityWrapper.value = false;
};
const sortChange = (value: number) => {
  listSortIndex.value = value;
  // 排序变化时触发搜索
  triggerSearch();
};

const emergencyChange = (flag: boolean) => {
  // 大龄选项变化时触发搜索
  triggerSearch();
};

const onlineChange = (flag: boolean) => {
  // 在线选项变化时触发搜索
  triggerSearch();
};

// CityArea 组件事件处理
const handleAreaBusinessChange = (value: string) => {
  currentAreaBusiness.value = value;
  // 区域选择变化时触发搜索
  triggerSearch();
};
// 存储搜索关键词等基础信息
const searchKeywords = ref({
  keyword: "",
  positionType: "",
  industryType: "",
});

// 存储区域业务选择
const currentAreaBusiness = ref("");

const search = (value: {
  keyword: string;
  positionType: string;
  industryType: string;
  schType: number;
}) => {
  // 更新搜索关键词
  searchKeywords.value = {
    keyword: value.keyword,
    positionType: value.positionType,
    industryType: value.industryType,
  };
  console.log(value);
  // 触发搜索，整合筛选条件
  triggerSearch();
};

// condition 组件事件处理
const handleConditionChange = (condition: { name: string; value: string }) => {
  selectedConditions.value[condition.name as keyof typeof selectedConditions.value] = condition.value;
  // 条件变化时触发搜索
  triggerSearch();
};

const handleConditionClear = () => {
  // 清空所有筛选条件
  selectedConditions.value = {
    salary: "",
    workProperty: "",
    entProp: "",
    firmSize: "",
    edu: "",
    firstDay: "",
    welfare: "",
  };
  // 清空后触发搜索
  triggerSearch();
};

// 触发搜索的统一方法
const triggerSearch = () => {
  // 构造搜索参数，整合所有条件
  const searchParams = {
    keyword: searchKeywords.value.keyword,
    positionType: searchKeywords.value.positionType ? searchKeywords.value.positionType.split(',').map(Number) : [],
    industryType: searchKeywords.value.industryType ? searchKeywords.value.industryType.split(',').map(Number) : [],
    schType: schType.value,
    areaBusiness: currentAreaBusiness.value ? currentAreaBusiness.value.split(',') : [],
    salary: selectedConditions.value.salary ? Number(selectedConditions.value.salary) : 0,
    workCycle: 0, // oddJob 相关
    workHour: 0, // oddJob 相关
    paymentType: 0, // oddJob 相关
    emergency: checkbox.value.includes("emergency"),
    online: checkbox.value.includes("online"),
    sort: listSortIndex.value,
    // 将字符串转换为数组格式
    welfare: selectedConditions.value.welfare ? selectedConditions.value.welfare.split(',').map(Number) : [],
    workProperty: selectedConditions.value.workProperty ? [Number(selectedConditions.value.workProperty)] : [],
    entProp: selectedConditions.value.entProp ? [Number(selectedConditions.value.entProp)] : [],
    firmSize: selectedConditions.value.firmSize ? [Number(selectedConditions.value.firmSize)] : [],
    edu: selectedConditions.value.edu ? [Number(selectedConditions.value.edu)] : [],
    firstDay: selectedConditions.value.firstDay || '',
  };
  
  console.log('搜索参数:', searchParams);
  
  // 触发父组件的搜索事件
  emit('search', searchParams);
};
onMounted(async () => {
  //cityId.value = districtId.value;
  cityName.value = cityStore.cityInfo.name;
  await cityInit();
  await cityAreaInit();
  await searchOptionInit();
});
</script>

<template>
  <div class="rounded-8px p-20px my-20px bg-#F1F7FB">
    <SearchInput @search="search" />
    <div class="city-area-select" ref="cityAreaSelect">
      <div class="city-area-dropdown">
        <div class="position-search-location">
          <div class="search-location-item area" @click="changeTabIndex(0)">
            <span class="text">{{ cityName }}</span>
            <i class="icon i-ep-arrow-up"></i>
          </div>
          <div
            class="search-location-item"
            :class="{ active: tabActiveIndex == 1 }"
            @click="changeTabIndex(1)"
            v-if="schType == 1"
          >
            <span class="text">区域选择</span
            ><i class="icon i-ep-arrow-up mb-5px"></i>
          </div>
        </div>
        <template v-if="tabActiveIndex == 1 && schType == 1">
          <city-area 
            :city="areaCity" 
            :cityArea="cityAreaList" 
            @area-business-change="handleAreaBusinessChange"
          />
        </template>
      </div>
    </div>
    <div class="odd-area-select" v-if="isOddJob">
      <div class="flex-center">
        <div class="odd-name">结算方式:</div>
        <div
          class="odd-item"
          :class="{ active: item.id == 0 }"
          v-for="item in paymentType"
          :key="item.id"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="flex-center ml-46px">
        <div class="odd-name">工作周期:</div>
        <div
          class="odd-item"
          :class="{ active: item.id == 0 }"
          v-for="item in workCycle"
          :key="item.id"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="flex-center ml-46px">
        <div class="odd-name">上班时段:</div>
        <div
          class="odd-item"
          :class="{ active: item.id == 0 }"
          v-for="item in workHour"
          :key="item.id"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <condition
      :search-options="searchOptionList"
      :selected-conditions="selectedConditions"
      @condition-change="handleConditionChange"
      @condition-clear="handleConditionClear"
      v-if="schType == 1 && !isOddJob && searchOptionList"
    />
    <div class="clearfix"></div>
  </div>
  <select-city-wrapper
    v-model="showCityWrapper"
    :city="cityList"
    @close="showCityWrapper = false"
    @comfirm="handleComfirmCity"
  />

  <div class="listSort" v-if="schType == 1">
    <ul class="listSort-ul">
      <li class="listSort-item" v-for="(item, index) in sort" :key="index">
        <a
          class="listSort-item-a"
          :class="{ active: listSortIndex == item.value }"
          @click="sortChange(item.value)"
          >{{ item.name }}
          <i class="iconfont sort icon-arrowDown2" v-if="index != 0"></i>
        </a>
      </li>
    </ul>
    <div class="listSort-checkboxList">
      <el-checkbox-group class="mt-6px" v-model="checkbox">
        <el-checkbox v-if="!isOddJob" label="emergency" @change="emergencyChange"
          >大龄</el-checkbox
        >
        <el-checkbox label="online" @change="onlineChange">在线</el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #3b86f6;
.city-area-select {
  position: relative;
  &.pick-up {
    display: inline-block;
    height: 32px;
    overflow: hidden;

    .city-area-dropdown {
      display: none;
      position: absolute;
      width: 1184px;
      top: 40px;
      left: 0;
      z-index: 4;
      padding: 16px;
      background: #fff;
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      border: 1px solid #ededed;

      .area {
        display: none;
      }
    }
    .city-current {
      display: block;
    }
  }
  &.pick-up:hover {
    overflow: visible;
    .city-area-dropdown {
      display: block;
    }
    &::after {
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -8px;
      height: 8px;
      background: transparent;
      z-index: 4;
    }
  }
  .city-current {
    display: none;
    position: relative;
    background: #f2f3f5;
    color: $active-color;
    font-weight: 500;
    border-radius: 4px;
    font-size: 14px;
    line-height: 20px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s linear;
  }
  .position-search-location {
    height: 41px;
    .search-location-item {
      display: inline-block;
      width: 100px;
      height: 41px;
      line-height: 41px;
      text-align: center;
      font-size: 14px;
      color: #34495e;
      cursor: pointer;
      &.area {
        padding-right: 22px;
      }
      .text {
        padding-right: 8px;
      }
      &.active {
        color: $active-color;
        .icon {
          color: $active-color;
          transform: rotateZ(0deg);
        }
      }
      .icon {
        color: #d2d2d2;
        //padding-left: 8px;
        transform: rotateZ(180deg);
        transition: transform 0.3s;
      }
    }
  }
}
.odd-area-select {
  @apply flex  text-14px mt-20px;
  .odd-name {
    @apply text-#9B9B9B;
  }
  .odd-item {
    @apply px-8px py-3px mx-4px;
    &.active {
      @apply bg-#3B86F6 text-white rounded-20px;
    }
  }
}
.listSort {
  height: 44px;
  overflow: hidden;
  background: #F6F6F6;
  margin: 0 auto 16px;
  clear: both;
  border-bottom: 3px solid $active-color;
  &-ul {
    float: left;
    font-size: 14px;
    letter-spacing: 0;
  }
  &-item {
    float: left;
    position: relative;
    //border-right: 1px solid #eee;
    width: 143px;
    cursor: pointer;
    &-a {
      display: block;
      width: 100%;
      line-height: 44px;
      text-align: center;
      color: #151515;
      cursor: pointer;
      &:hover {
        color: $active-color;
      }
      &.active {
        background: $active-color;
        color: #fff;
        font-weight: bold;
      }
      .sort {
        color: #cbcbcb;
        font-size: 12px;
      }
      .icon-arrowUp5 {
        top: -4px;
        position: absolute;
        right: 40px;
      }
      .icon-arrowDown5 {
        position: absolute;
        top: 4px;
        right: 40px;
      }
    }
  }
  &-checkboxList {
    line-height: 44px;
    float: left;
    padding-left: 40px;
  }
}
</style>
