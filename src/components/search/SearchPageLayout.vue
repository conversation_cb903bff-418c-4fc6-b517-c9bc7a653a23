<template>
  <div
    class="container-lg relative box-border bg-white mt-20px px-20px overflow-hidden"
  >
    <!-- 搜索框 -->
    <search-box v-bind="searchBoxProps" @search="handleSearch" />

    <!-- 使用分页组件，延迟初始化避免重复调用 -->
    <Pagination 
      v-if="isReady"
      ref="paginationRef"
      :ApiFunction="apiFunction" 
      :option="{}"
      :asyncOption="{}"
      :showAffix="false"
      layout="prev, pager, next, jumper"
    >
      <template #default="{ tableData, loading, empty }">
        <!-- 加载状态 -->
        <el-skeleton :loading="loading" :count="4" :throttle="100" animated>
          <template #template>
            <div class="skele">
              <div class="skele-left">
                <el-skeleton-item variant="p" class="p1"></el-skeleton-item>
                <el-skeleton-item variant="p" class="p2"></el-skeleton-item>
                <el-skeleton-item variant="p" class="p3"></el-skeleton-item>
              </div>
              <div class="skele-right">
                <div class="clearfix">
                  <el-skeleton-item
                    variant="image"
                    class="image"
                  ></el-skeleton-item>
                  <div class="right">
                    <el-skeleton-item
                      variant="p"
                      style="width: 550px; height: 24px; display: block"
                    ></el-skeleton-item>
                    <el-skeleton-item
                      variant="p"
                      style="width: 550px; height: 24px; margin-top: 8px"
                    ></el-skeleton-item>
                  </div>
                </div>
                <div style="margin-top: 16px; margin-left: 10px">
                  <el-skeleton-item
                    variant="p"
                    style="width: 615px; height: 24px; display: block"
                  ></el-skeleton-item>
                </div>
              </div>
            </div>
          </template>
          <template #default>
            <!-- 自定义结果展示插槽 -->
            <slot 
              name="results" 
              :tableData="tableData" 
              :loading="loading" 
              :empty="empty"
            >
              <!-- 默认结果展示 - position-list -->
              <div v-if="!empty">
                <position-list :position-list="tableData" @deliver="handleDeliver" />
              </div>
              <!-- 空状态 -->
              <div v-else class="min-h-200px flex items-center justify-center">
                <el-empty :description="emptyDescription" />
              </div>
            </slot>
          </template>
        </el-skeleton>
      </template>
    </Pagination>
    <resume-list-popup v-model:visible="resumeListPopupVisible" :pos-name="deliverPosName" :pos-guid="deliverPosGuid" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import SearchBox from "./searchBox.vue";
import PositionList from "./positionList.vue";
import Pagination from "@/components/table/Pagination.vue";
import { SearchService } from '@/services/searchService';
import type { BaseSearchParams,SearchConfig } from "@/types/search";
import ResumeListPopup from "@/components/search/resumeListPopup.vue";



// 定义组件 props
interface Props {
  searchType: SearchConfig['searchType'];
  defaultParams: Record<string, any>;
  searchBoxProps?: Record<string, any>;
  emptyText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  searchBoxProps: () => ({}),
  emptyText: "暂无符合条件的职位"
});

// 动态搜索参数（来自搜索框的条件）
const searchParams = ref<Partial<BaseSearchParams>>({
  keyword: "",
  positionType: [],
  industryType: [],
  areaBusiness: [],
  salary: 0,
  emergency: false,
  online: false,
  sort: 0,
  welfare: [],
  workProperty: [],
  entProp: [],
  firmSize: [],
  edu: [],
  firstDay: "",
});

const paginationRef = ref<any>(null);
const isReady = ref(false);
const isInitialized = ref(false);
const resumeListPopupVisible = ref(false);
const deliverPosName = ref("");
const deliverPosGuid = ref("");

// 计算空状态描述
const emptyDescription = computed(() => {
  const typeMap = {
    elderly: "暂无符合条件的大龄工作者职位",
    oddJob: "暂无符合条件的兼职职位", 
    park: "暂无符合条件的园区招聘职位",
    search: "暂无符合条件的职位"
  };
  return typeMap[props.searchType] || props.emptyText;
});

// 使用SearchService创建API函数
const apiFunction = SearchService.createPaginationApiFunction(
  props.searchType,
  props.defaultParams,
  () => searchParams.value
);

// 处理搜索事件
const handleSearch = (params: BaseSearchParams) => {
  console.log(`[${props.searchType}] 收到搜索参数:`, params);
  
  // 如果还在初始化阶段，忽略搜索事件，避免重复调用API
  if (!isInitialized.value) {
    console.log(`[${props.searchType}] 初始化阶段，忽略搜索事件`);
    return;
  }
  
  // 更新搜索参数
  searchParams.value = { ...searchParams.value, ...params };
  
  // 触发分页组件重新搜索（重置到第一页）
  if (paginationRef.value) {
    paginationRef.value.asyncOptionReset();
  }
};
const handleDeliver = (notHitPName: string, positionGuid: string) => {
  deliverPosName.value = notHitPName;
  deliverPosGuid.value = positionGuid;
  resumeListPopupVisible.value = true;
};
const handleSuccess = (positionGuid: string) => {
  console.log(positionGuid);
};
// 延迟初始化分页组件，避免重复调用
onMounted(async () => {
  await nextTick();
  isReady.value = true;
  
  // 延迟标记初始化完成，允许后续搜索事件
  setTimeout(() => {
    isInitialized.value = true;
    console.log(`[${props.searchType}] 初始化完成，现在可以响应搜索事件`);
  }, 500); // 给足够时间让初始化完成
});

// 暴露方法给父组件
defineExpose({
  refreshSearch: () => {
    if (paginationRef.value) {
      paginationRef.value.asyncOptionReset();
    }
  },
  clearSearch: () => {
    searchParams.value = {
      keyword: "",
      positionType: [],
      industryType: [],
      areaBusiness: [],
      salary: 0,
      emergency: false,
      online: false,
      sort: 0,
      welfare: [],
      workProperty: [],
      entProp: [],
      firmSize: [],
      edu: [],
      firstDay: "",
    };
    if (paginationRef.value) {
      paginationRef.value.asyncOptionReset();
    }
  }
});
</script>

<style lang="scss" scoped>
.skele {
  width: 1160px;
  height: 136px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  &-left {
    float: left;
    width: 484px;
    .p1 {
      width: 240px;
      height: 24px;
      margin: 16px 0 8px 0px;
    }
    .p2 {
      width: 268px;
      height: 24px;
      margin: 0 0 16px 0px;
      display: block;
    }
    .p3 {
      width: 368px;
      height: 24px;
      margin: 0 0 16px 0px;
      display: block;
    }
  }
  &-right {
    float: left;
    .image {
      width: 54px;
      height: 54px;
      margin: 16px 0 8px 10px;
      float: left;
    }
    .right {
      float: left;
      margin-left: 12px;
      margin-top: 16px;
    }
  }
}

.min-h-200px {
  min-height: 200px;
}
</style>